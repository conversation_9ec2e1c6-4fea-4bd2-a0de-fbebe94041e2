#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
止盈止损验证器
解决问题4: 验证能否真正达到止盈、止损
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_fetcher import DataFetcher

class ValidationResult(Enum):
    """验证结果"""
    ACHIEVABLE = "achievable"        # 可达到
    PARTIALLY_ACHIEVABLE = "partial" # 部分可达到
    UNREALISTIC = "unrealistic"     # 不现实
    INSUFFICIENT_DATA = "no_data"   # 数据不足

@dataclass
class StopLossTarget:
    """止损目标"""
    price: float
    percentage: float
    reasoning: str
    confidence: float

@dataclass
class ProfitTarget:
    """止盈目标"""
    level: int                # 止盈级别 1,2,3
    price: float
    percentage: float
    reasoning: str
    confidence: float
    time_expectation: int     # 预期达到时间(分钟)

@dataclass
class ValidationReport:
    """验证报告"""
    entry_price: float
    stop_loss: StopLossTarget
    profit_targets: List[ProfitTarget]
    overall_result: ValidationResult
    achievability_score: float      # 可达性评分 0-1
    risk_reward_ratio: float
    historical_success_rate: float
    recommendations: List[str]
    warnings: List[str]

class StopProfitValidator:
    """止盈止损验证器"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        self.symbol = symbol
        self.exchange = exchange
        
        # 初始化数据获取器
        self.data_fetcher = DataFetcher(exchange_id=exchange, symbols=[symbol])
        
        # 历史验证数据
        self.validation_history = []
        
        # 验证参数
        self.validation_config = {
            'lookback_days': 30,        # 回看天数
            'min_samples': 50,          # 最小样本数
            'volatility_threshold': 0.05, # 波动率阈值
            'time_decay_factor': 0.95,  # 时间衰减因子
            'confidence_threshold': 0.7  # 置信度阈值
        }
        
        logger.info(f"止盈止损验证器初始化完成 - {symbol}")
    
    def validate_stop_profit_levels(self, entry_price: float, stop_loss: float, 
                                  take_profits: List[float], timeframe: str = '15m') -> ValidationReport:
        """
        验证止盈止损水平
        
        参数:
            entry_price: 入场价格
            stop_loss: 止损价格
            take_profits: 止盈价格列表
            timeframe: 时间周期
            
        返回:
            ValidationReport: 验证报告
        """
        try:
            logger.info(f"开始验证止盈止损水平 - 入场价格: {entry_price}")
            
            # 1. 获取历史数据
            historical_data = self._get_historical_data(timeframe)
            if historical_data.empty:
                return self._create_insufficient_data_report(entry_price, stop_loss, take_profits)
            
            # 2. 创建止损目标
            stop_loss_target = self._create_stop_loss_target(entry_price, stop_loss)
            
            # 3. 创建止盈目标列表
            profit_targets = self._create_profit_targets(entry_price, take_profits)
            
            # 4. 历史回测验证
            historical_results = self._backtest_historical_performance(
                historical_data, entry_price, stop_loss, take_profits
            )
            
            # 5. 波动率分析
            volatility_analysis = self._analyze_volatility_feasibility(
                historical_data, entry_price, stop_loss, take_profits
            )
            
            # 6. 时间可达性分析
            time_analysis = self._analyze_time_feasibility(
                historical_data, entry_price, take_profits, timeframe
            )
            
            # 7. 综合评估
            overall_result, achievability_score = self._calculate_overall_assessment(
                historical_results, volatility_analysis, time_analysis
            )
            
            # 8. 生成建议和警告
            recommendations, warnings = self._generate_recommendations_and_warnings(
                historical_results, volatility_analysis, time_analysis, 
                stop_loss_target, profit_targets
            )
            
            # 9. 计算风险收益比
            risk_reward_ratio = self._calculate_risk_reward_ratio(
                entry_price, stop_loss, take_profits
            )
            
            # 10. 创建验证报告
            report = ValidationReport(
                entry_price=entry_price,
                stop_loss=stop_loss_target,
                profit_targets=profit_targets,
                overall_result=overall_result,
                achievability_score=achievability_score,
                risk_reward_ratio=risk_reward_ratio,
                historical_success_rate=historical_results.get('success_rate', 0.0),
                recommendations=recommendations,
                warnings=warnings
            )
            
            # 11. 记录验证历史
            self._record_validation(report)
            
            logger.info(f"止盈止损验证完成 - 总体结果: {overall_result.value}, 评分: {achievability_score:.2f}")
            return report
            
        except Exception as e:
            logger.error(f"验证止盈止损失败: {str(e)}")
            return self._create_error_report(entry_price, stop_loss, take_profits, str(e))
    
    def _get_historical_data(self, timeframe: str) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 计算需要的数据量
            days = self.validation_config['lookback_days']
            
            # 根据时间周期计算K线数量
            timeframe_minutes = {
                '1m': 1, '5m': 5, '15m': 15, '30m': 30, 
                '1h': 60, '4h': 240, '1d': 1440
            }
            
            minutes_per_day = 1440
            period_minutes = timeframe_minutes.get(timeframe, 15)
            limit = int(days * minutes_per_day / period_minutes)
            
            # 获取数据
            df = self.data_fetcher.get_klines(symbol=self.symbol, limit=limit, timeframe=timeframe)
            
            if not df.empty:
                logger.info(f"获取历史数据成功: {len(df)} 条 {timeframe} 数据")
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _create_stop_loss_target(self, entry_price: float, stop_loss: float) -> StopLossTarget:
        """创建止损目标"""
        try:
            percentage = abs(entry_price - stop_loss) / entry_price * 100
            
            # 评估止损合理性
            if percentage <= 2:
                reasoning = "止损幅度较小，风险控制严格"
                confidence = 0.9
            elif percentage <= 5:
                reasoning = "止损幅度适中，风险控制合理"
                confidence = 0.8
            elif percentage <= 10:
                reasoning = "止损幅度较大，需要谨慎"
                confidence = 0.6
            else:
                reasoning = "止损幅度过大，风险较高"
                confidence = 0.3
            
            return StopLossTarget(
                price=stop_loss,
                percentage=percentage,
                reasoning=reasoning,
                confidence=confidence
            )
            
        except Exception as e:
            logger.warning(f"创建止损目标失败: {str(e)}")
            return StopLossTarget(stop_loss, 0, "创建失败", 0.0)
    
    def _create_profit_targets(self, entry_price: float, take_profits: List[float]) -> List[ProfitTarget]:
        """创建止盈目标列表"""
        profit_targets = []
        
        try:
            for i, tp_price in enumerate(take_profits):
                level = i + 1
                percentage = abs(tp_price - entry_price) / entry_price * 100
                
                # 评估止盈合理性
                if percentage <= 3:
                    reasoning = f"第{level}止盈目标保守，容易达到"
                    confidence = 0.8
                    time_expectation = 30 + i * 15  # 30, 45, 60分钟
                elif percentage <= 8:
                    reasoning = f"第{level}止盈目标适中，有一定挑战"
                    confidence = 0.6
                    time_expectation = 60 + i * 30  # 60, 90, 120分钟
                elif percentage <= 15:
                    reasoning = f"第{level}止盈目标激进，需要较强趋势"
                    confidence = 0.4
                    time_expectation = 120 + i * 60  # 120, 180, 240分钟
                else:
                    reasoning = f"第{level}止盈目标过于激进，实现困难"
                    confidence = 0.2
                    time_expectation = 240 + i * 120  # 240, 360, 480分钟
                
                profit_target = ProfitTarget(
                    level=level,
                    price=tp_price,
                    percentage=percentage,
                    reasoning=reasoning,
                    confidence=confidence,
                    time_expectation=time_expectation
                )
                
                profit_targets.append(profit_target)
                
        except Exception as e:
            logger.warning(f"创建止盈目标失败: {str(e)}")
        
        return profit_targets
    
    def _backtest_historical_performance(self, df: pd.DataFrame, entry_price: float, 
                                       stop_loss: float, take_profits: List[float]) -> Dict[str, Any]:
        """历史回测验证"""
        try:
            if len(df) < self.validation_config['min_samples']:
                return {'success_rate': 0.0, 'avg_time_to_target': 0, 'hit_rates': []}
            
            results = {
                'total_tests': 0,
                'stop_loss_hits': 0,
                'profit_hits': [0] * len(take_profits),
                'avg_time_to_target': [0] * len(take_profits),
                'success_rate': 0.0,
                'hit_rates': []
            }
            
            # 模拟交易测试
            for i in range(len(df) - 50):  # 保留50个点作为未来数据
                test_entry = df['close'].iloc[i]
                future_data = df.iloc[i+1:i+51]  # 未来50个点
                
                if future_data.empty:
                    continue
                
                # 调整止盈止损到当前价格水平
                price_ratio = test_entry / entry_price
                adjusted_stop_loss = stop_loss * price_ratio
                adjusted_take_profits = [tp * price_ratio for tp in take_profits]
                
                # 测试这次交易
                test_result = self._simulate_single_trade(
                    future_data, test_entry, adjusted_stop_loss, adjusted_take_profits
                )
                
                results['total_tests'] += 1
                if test_result['stop_loss_hit']:
                    results['stop_loss_hits'] += 1
                
                for j, hit in enumerate(test_result['profit_hits']):
                    if hit:
                        results['profit_hits'][j] += 1
                        if test_result['time_to_targets'][j] > 0:
                            results['avg_time_to_target'][j] += test_result['time_to_targets'][j]
            
            # 计算成功率和平均时间
            if results['total_tests'] > 0:
                results['success_rate'] = (results['total_tests'] - results['stop_loss_hits']) / results['total_tests']
                
                for j in range(len(take_profits)):
                    hit_rate = results['profit_hits'][j] / results['total_tests']
                    results['hit_rates'].append(hit_rate)
                    
                    if results['profit_hits'][j] > 0:
                        results['avg_time_to_target'][j] /= results['profit_hits'][j]
            
            return results
            
        except Exception as e:
            logger.warning(f"历史回测失败: {str(e)}")
            return {'success_rate': 0.0, 'avg_time_to_target': 0, 'hit_rates': []}
    
    def _simulate_single_trade(self, future_data: pd.DataFrame, entry_price: float,
                             stop_loss: float, take_profits: List[float]) -> Dict[str, Any]:
        """模拟单次交易"""
        try:
            result = {
                'stop_loss_hit': False,
                'profit_hits': [False] * len(take_profits),
                'time_to_targets': [0] * len(take_profits)
            }
            
            for i, row in future_data.iterrows():
                high = row['high']
                low = row['low']
                
                # 检查止损
                if (entry_price > stop_loss and low <= stop_loss) or \
                   (entry_price < stop_loss and high >= stop_loss):
                    result['stop_loss_hit'] = True
                    break
                
                # 检查止盈
                for j, tp in enumerate(take_profits):
                    if not result['profit_hits'][j]:
                        if (entry_price < tp and high >= tp) or \
                           (entry_price > tp and low <= tp):
                            result['profit_hits'][j] = True
                            result['time_to_targets'][j] = i + 1  # 时间步数
            
            return result
            
        except Exception as e:
            logger.warning(f"模拟单次交易失败: {str(e)}")
            return {'stop_loss_hit': True, 'profit_hits': [False] * len(take_profits), 'time_to_targets': [0] * len(take_profits)}

    def _analyze_volatility_feasibility(self, df: pd.DataFrame, entry_price: float,
                                       stop_loss: float, take_profits: List[float]) -> Dict[str, Any]:
        """分析波动率可行性"""
        try:
            if len(df) < 20:
                return {'feasible': False, 'reason': '数据不足'}

            # 计算历史波动率
            returns = df['close'].pct_change().dropna()
            daily_volatility = returns.std()

            # 计算所需的价格移动幅度
            stop_loss_move = abs(entry_price - stop_loss) / entry_price
            profit_moves = [abs(tp - entry_price) / entry_price for tp in take_profits]

            analysis = {
                'daily_volatility': daily_volatility,
                'stop_loss_feasible': stop_loss_move <= daily_volatility * 2,
                'profit_feasibility': [],
                'volatility_score': 0.0
            }

            # 分析每个止盈目标的可行性
            for i, move in enumerate(profit_moves):
                # 基于波动率判断可行性
                if move <= daily_volatility:
                    feasibility = 'high'
                    score = 0.9
                elif move <= daily_volatility * 2:
                    feasibility = 'medium'
                    score = 0.6
                elif move <= daily_volatility * 3:
                    feasibility = 'low'
                    score = 0.3
                else:
                    feasibility = 'very_low'
                    score = 0.1

                analysis['profit_feasibility'].append({
                    'level': i + 1,
                    'required_move': move,
                    'feasibility': feasibility,
                    'score': score
                })

            # 计算总体波动率评分
            if analysis['profit_feasibility']:
                analysis['volatility_score'] = sum(pf['score'] for pf in analysis['profit_feasibility']) / len(analysis['profit_feasibility'])

            return analysis

        except Exception as e:
            logger.warning(f"分析波动率可行性失败: {str(e)}")
            return {'feasible': False, 'reason': f'分析失败: {str(e)}'}

    def _analyze_time_feasibility(self, df: pd.DataFrame, entry_price: float,
                                take_profits: List[float], timeframe: str) -> Dict[str, Any]:
        """分析时间可行性"""
        try:
            if len(df) < 50:
                return {'feasible': False, 'reason': '数据不足'}

            # 计算历史价格达到类似幅度所需的时间
            analysis = {
                'timeframe': timeframe,
                'time_estimates': [],
                'time_score': 0.0
            }

            for i, tp in enumerate(take_profits):
                move_percentage = abs(tp - entry_price) / entry_price

                # 统计历史上达到类似幅度的时间
                time_samples = []

                for j in range(len(df) - 20):
                    start_price = df['close'].iloc[j]
                    target_price = start_price * (1 + move_percentage if tp > entry_price else 1 - move_percentage)

                    # 寻找达到目标价格的时间
                    for k in range(j + 1, min(j + 21, len(df))):
                        if (tp > entry_price and df['high'].iloc[k] >= target_price) or \
                           (tp < entry_price and df['low'].iloc[k] <= target_price):
                            time_samples.append(k - j)
                            break

                # 计算平均时间和成功率
                if time_samples:
                    avg_time = sum(time_samples) / len(time_samples)
                    success_rate = len(time_samples) / (len(df) - 20)
                else:
                    avg_time = float('inf')
                    success_rate = 0.0

                analysis['time_estimates'].append({
                    'level': i + 1,
                    'avg_time_periods': avg_time,
                    'success_rate': success_rate,
                    'samples': len(time_samples)
                })

            # 计算时间评分
            if analysis['time_estimates']:
                time_scores = [te['success_rate'] for te in analysis['time_estimates']]
                analysis['time_score'] = sum(time_scores) / len(time_scores)

            return analysis

        except Exception as e:
            logger.warning(f"分析时间可行性失败: {str(e)}")
            return {'feasible': False, 'reason': f'分析失败: {str(e)}'}

    def _calculate_overall_assessment(self, historical_results: Dict[str, Any],
                                    volatility_analysis: Dict[str, Any],
                                    time_analysis: Dict[str, Any]) -> Tuple[ValidationResult, float]:
        """计算总体评估"""
        try:
            # 各维度评分
            historical_score = historical_results.get('success_rate', 0.0)
            volatility_score = volatility_analysis.get('volatility_score', 0.0)
            time_score = time_analysis.get('time_score', 0.0)

            # 加权平均
            weights = [0.4, 0.3, 0.3]  # 历史、波动率、时间
            overall_score = (historical_score * weights[0] +
                           volatility_score * weights[1] +
                           time_score * weights[2])

            # 确定验证结果
            if overall_score >= 0.7:
                result = ValidationResult.ACHIEVABLE
            elif overall_score >= 0.5:
                result = ValidationResult.PARTIALLY_ACHIEVABLE
            elif overall_score >= 0.3:
                result = ValidationResult.UNREALISTIC
            else:
                result = ValidationResult.INSUFFICIENT_DATA

            return result, overall_score

        except Exception as e:
            logger.warning(f"计算总体评估失败: {str(e)}")
            return ValidationResult.INSUFFICIENT_DATA, 0.0

    def _generate_recommendations_and_warnings(self, historical_results: Dict[str, Any],
                                             volatility_analysis: Dict[str, Any],
                                             time_analysis: Dict[str, Any],
                                             stop_loss_target: StopLossTarget,
                                             profit_targets: List[ProfitTarget]) -> Tuple[List[str], List[str]]:
        """生成建议和警告"""
        recommendations = []
        warnings = []

        try:
            # 基于历史成功率的建议
            success_rate = historical_results.get('success_rate', 0.0)
            if success_rate < 0.3:
                warnings.append("历史成功率较低，建议调整止损位置")
                recommendations.append("考虑放宽止损幅度或选择更保守的入场点")
            elif success_rate > 0.7:
                recommendations.append("历史成功率较高，当前设置较为合理")

            # 基于波动率的建议
            volatility_score = volatility_analysis.get('volatility_score', 0.0)
            if volatility_score < 0.4:
                warnings.append("基于当前波动率，止盈目标可能过于激进")
                recommendations.append("建议降低止盈目标或延长持仓时间")

            # 基于止损幅度的建议
            if stop_loss_target.percentage > 8:
                warnings.append(f"止损幅度{stop_loss_target.percentage:.1f}%较大，风险较高")
                recommendations.append("考虑缩小止损幅度以控制风险")
            elif stop_loss_target.percentage < 1:
                warnings.append("止损幅度过小，可能频繁触发")
                recommendations.append("适当放宽止损幅度以避免噪音干扰")

            # 基于止盈目标的建议
            for target in profit_targets:
                if target.percentage > 15:
                    warnings.append(f"第{target.level}止盈目标{target.percentage:.1f}%过于激进")
                    recommendations.append(f"建议调整第{target.level}止盈目标到更现实的水平")

            # 基于时间分析的建议
            time_estimates = time_analysis.get('time_estimates', [])
            for estimate in time_estimates:
                if estimate['success_rate'] < 0.3:
                    recommendations.append(f"第{estimate['level']}止盈目标历史达成率较低，建议延长预期时间")

            # 风险收益比建议
            if len(profit_targets) > 0:
                first_target_ratio = profit_targets[0].percentage / stop_loss_target.percentage
                if first_target_ratio < 1.5:
                    warnings.append("风险收益比偏低，建议优化")
                    recommendations.append("考虑提高第一止盈目标或缩小止损幅度")

        except Exception as e:
            logger.warning(f"生成建议和警告失败: {str(e)}")
            warnings.append("建议生成失败，请手动评估")

        return recommendations, warnings

    def _calculate_risk_reward_ratio(self, entry_price: float, stop_loss: float,
                                   take_profits: List[float]) -> float:
        """计算风险收益比"""
        try:
            if not take_profits:
                return 0.0

            risk = abs(entry_price - stop_loss)
            reward = abs(take_profits[0] - entry_price)  # 使用第一个止盈目标

            if risk == 0:
                return float('inf')

            return reward / risk

        except Exception as e:
            logger.warning(f"计算风险收益比失败: {str(e)}")
            return 0.0

    def _record_validation(self, report: ValidationReport):
        """记录验证历史"""
        try:
            validation_record = {
                'timestamp': datetime.now(),
                'entry_price': report.entry_price,
                'stop_loss': report.stop_loss.price,
                'take_profits': [tp.price for tp in report.profit_targets],
                'result': report.overall_result.value,
                'score': report.achievability_score,
                'risk_reward_ratio': report.risk_reward_ratio
            }

            self.validation_history.append(validation_record)

            # 保持历史记录在合理范围内
            if len(self.validation_history) > 1000:
                self.validation_history = self.validation_history[-1000:]

        except Exception as e:
            logger.warning(f"记录验证历史失败: {str(e)}")

    def _create_insufficient_data_report(self, entry_price: float, stop_loss: float,
                                       take_profits: List[float]) -> ValidationReport:
        """创建数据不足报告"""
        return ValidationReport(
            entry_price=entry_price,
            stop_loss=StopLossTarget(stop_loss, 0, "数据不足", 0.0),
            profit_targets=[ProfitTarget(i+1, tp, 0, "数据不足", 0.0, 0) for i, tp in enumerate(take_profits)],
            overall_result=ValidationResult.INSUFFICIENT_DATA,
            achievability_score=0.0,
            risk_reward_ratio=0.0,
            historical_success_rate=0.0,
            recommendations=["获取更多历史数据后重新验证"],
            warnings=["数据不足，无法进行有效验证"]
        )

    def _create_error_report(self, entry_price: float, stop_loss: float,
                           take_profits: List[float], error_msg: str) -> ValidationReport:
        """创建错误报告"""
        return ValidationReport(
            entry_price=entry_price,
            stop_loss=StopLossTarget(stop_loss, 0, f"验证失败: {error_msg}", 0.0),
            profit_targets=[ProfitTarget(i+1, tp, 0, f"验证失败: {error_msg}", 0.0, 0) for i, tp in enumerate(take_profits)],
            overall_result=ValidationResult.INSUFFICIENT_DATA,
            achievability_score=0.0,
            risk_reward_ratio=0.0,
            historical_success_rate=0.0,
            recommendations=["修复错误后重新验证"],
            warnings=[f"验证过程出错: {error_msg}"]
        )

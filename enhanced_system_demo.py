#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强交易系统演示
展示所有改进功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_trading_system import EnhancedTradingSystem
from loguru import logger

def demo_enhanced_trading_system():
    """演示增强交易系统"""
    print("=" * 80)
    print("🚀 增强交易系统演示")
    print("=" * 80)
    
    try:
        # 1. 初始化增强交易系统
        print("\n1️⃣ 初始化增强交易系统...")
        system = EnhancedTradingSystem(symbol="BTC/USDT", exchange="gate")
        print("✅ 系统初始化完成")
        
        # 2. 分析交易机会
        print("\n2️⃣ 开始分析交易机会...")
        decision = system.analyze_trading_opportunity()
        
        # 3. 显示分析结果
        print("\n" + "=" * 60)
        print("📊 交易决策分析结果")
        print("=" * 60)
        
        # 基本决策信息
        print(f"🎯 交易动作: {decision.action.upper()}")
        print(f"📊 置信度: {decision.confidence:.1%}")
        print(f"💰 入场价格: ${decision.entry_price:,.2f}")
        print(f"🛡️ 止损价格: ${decision.stop_loss:,.2f}")
        print(f"🎯 止盈目标: {[f'${tp:,.2f}' for tp in decision.take_profits]}")
        print(f"📏 仓位建议: {decision.position_size}")
        print(f"⚠️ 风险等级: {decision.risk_level}")
        print(f"⏰ 主要周期: {decision.timeframe}")
        
        # 详细分析理由
        print(f"\n📝 决策理由:")
        print(f"   {decision.reasoning}")
        
        # 大周期趋势状态
        print(f"\n🔍 大周期趋势分析:")
        major_trend = decision.major_trend_status
        print(f"   • 趋势状态: {major_trend['status'].upper()}")
        print(f"   • 置信度: {major_trend['confidence']:.1%}")
        print(f"   • 是否变盘: {'是' if major_trend.get('is_changing', False) else '否'}")
        if 'level_trends' in major_trend:
            print(f"   • 各级别趋势: {major_trend['level_trends']}")
        
        # 概率分析
        print(f"\n📈 概率分析:")
        prob_analysis = decision.probability_analysis
        if prob_analysis.get('enhanced'):
            confidence_info = prob_analysis.get('confidence_info', {})
            print(f"   • 概率优化: 已启用")
            print(f"   • 主要概率: {prob_analysis.get('primary_probability', 0):.1%}")
            print(f"   • 置信等级: {confidence_info.get('confidence', 'unknown')}")
            print(f"   • 置信评分: {confidence_info.get('score', 0):.2f}")
        else:
            print(f"   • 概率优化: 未启用 - {prob_analysis.get('reason', '未知原因')}")
        
        # 止盈止损验证
        print(f"\n🔍 止盈止损验证:")
        if decision.validation_report:
            report = decision.validation_report
            print(f"   • 验证结果: {report.overall_result.value.upper()}")
            print(f"   • 可达性评分: {report.achievability_score:.2f}")
            print(f"   • 风险收益比: {report.risk_reward_ratio:.2f}")
            print(f"   • 历史成功率: {report.historical_success_rate:.1%}")
            
            # 止损分析
            print(f"   • 止损分析:")
            print(f"     - 价格: ${report.stop_loss.price:,.2f}")
            print(f"     - 幅度: {report.stop_loss.percentage:.1f}%")
            print(f"     - 置信度: {report.stop_loss.confidence:.1%}")
            print(f"     - 理由: {report.stop_loss.reasoning}")
            
            # 止盈分析
            print(f"   • 止盈分析:")
            for target in report.profit_targets:
                print(f"     - 第{target.level}目标: ${target.price:,.2f} ({target.percentage:.1f}%) "
                      f"置信度{target.confidence:.1%} 预期{target.time_expectation}分钟")
        else:
            print(f"   • 验证结果: 未进行验证")
        
        # 警告信息
        if decision.warnings:
            print(f"\n⚠️ 警告信息:")
            for warning in decision.warnings:
                print(f"   • {warning}")
        
        # 建议信息
        if decision.recommendations:
            print(f"\n💡 建议信息:")
            for recommendation in decision.recommendations:
                print(f"   • {recommendation}")
        
        # 4. 系统改进总结
        print("\n" + "=" * 60)
        print("🎯 系统改进总结")
        print("=" * 60)
        
        improvements = [
            "✅ 多级别联立买卖点分析 - 大周期看方向，小周期找时机",
            "✅ 大周期趋势状态判断 - 明确看多/看空/变盘状态",
            "✅ 买卖点合理性验证 - 多维度信号质量评估",
            "✅ 止盈止损历史验证 - 基于历史数据的可达性分析",
            "✅ 概率计算优化 - 自适应权重和多方法集成",
            "✅ 风险管理增强 - 综合风险评估和仓位建议"
        ]
        
        for improvement in improvements:
            print(f"   {improvement}")
        
        # 5. 投入使用建议
        print(f"\n💼 投入使用建议:")
        if decision.action != 'wait' and decision.confidence >= 0.6:
            print(f"   🟢 系统建议: 可以考虑{decision.action.upper()}操作")
            print(f"   📊 置信度: {decision.confidence:.1%} (≥60%)")
            print(f"   ⚠️ 风险等级: {decision.risk_level}")
            
            if decision.validation_report and decision.validation_report.achievability_score >= 0.5:
                print(f"   ✅ 止盈止损: 验证通过 (评分{decision.validation_report.achievability_score:.2f})")
            else:
                print(f"   ⚠️ 止盈止损: 需要谨慎")
        else:
            print(f"   🟡 系统建议: 等待更好机会")
            print(f"   📊 当前置信度: {decision.confidence:.1%}")
            print(f"   💡 等待理由: {decision.reasoning}")
        
        print(f"\n🔄 下一步:")
        print(f"   1. 持续监控市场变化")
        print(f"   2. 等待信号确认或新的机会")
        print(f"   3. 严格执行风险管理")
        
        return decision
        
    except Exception as e:
        logger.error(f"演示失败: {str(e)}")
        print(f"❌ 演示失败: {str(e)}")
        return None

def show_system_capabilities():
    """展示系统能力"""
    print("\n" + "=" * 60)
    print("🔧 系统核心能力")
    print("=" * 60)
    
    capabilities = {
        "多级别联立分析": [
            "支持5m/15m/30m/1h/4h/1d多个时间周期",
            "大周期确定方向，小周期寻找入场时机",
            "自动检测级别间的共振和分歧",
            "动态权重调整，大级别权重更高"
        ],
        "智能信号过滤": [
            "只在大周期方向明确时给出信号",
            "大周期即将变盘时建议空仓等待",
            "多维度信号质量评估",
            "历史准确率反馈学习"
        ],
        "概率计算优化": [
            "传统权重法、机器学习法、集成方法",
            "自适应权重调整",
            "历史反馈学习",
            "概率分布合理性验证"
        ],
        "止盈止损验证": [
            "历史回测验证可达性",
            "波动率分析可行性",
            "时间可行性评估",
            "风险收益比计算"
        ],
        "风险管理": [
            "动态风险等级评估",
            "仓位大小建议",
            "多重安全检查",
            "实时警告和建议"
        ]
    }
    
    for capability, features in capabilities.items():
        print(f"\n🎯 {capability}:")
        for feature in features:
            print(f"   • {feature}")

if __name__ == "__main__":
    # 运行演示
    decision = demo_enhanced_trading_system()
    
    # 展示系统能力
    show_system_capabilities()
    
    print("\n" + "=" * 80)
    print("✅ 增强交易系统演示完成")
    print("=" * 80)
    
    if decision:
        print(f"\n💡 最终建议: {decision.action.upper()}")
        print(f"📊 综合置信度: {decision.confidence:.1%}")
        print(f"⚠️ 风险提示: 请结合实际市场情况谨慎决策")
    
    print("\n🔄 后续改进方向:")
    print("   1. 连接实盘交易所API")
    print("   2. 增加更多技术指标")
    print("   3. 机器学习模型训练")
    print("   4. 实时监控和报警")
    print("   5. 回测系统完善")

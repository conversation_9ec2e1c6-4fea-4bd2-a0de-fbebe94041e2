#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
走势推演引擎 - 轻量版
不依赖pandas等复杂库，展示核心功能
"""

import ccxt
import os
import json
from datetime import datetime, timedelta
from loguru import logger
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

class TrendType(Enum):
    """走势类型枚举"""
    UP = "up"
    DOWN = "down"
    CONSOLIDATION = "consolidation"

@dataclass
class TrendScenario:
    """走势情景"""
    trend_type: TrendType
    probability: float
    target_price: Optional[float]
    time_horizon: Optional[int]
    confidence_level: str
    supporting_factors: List[str]
    risk_factors: List[str]
    description: str

@dataclass
class EvolutionResult:
    """推演结果"""
    primary_scenario: TrendScenario
    alternative_scenarios: List[TrendScenario]
    recommended_action: str
    profit_target: Optional[float]
    stop_loss: Optional[float]
    position_size: str
    analysis_timestamp: datetime
    data_source: str = "live"
    chart_info: Optional[str] = None

class TrendEvolutionLite:
    """走势推演引擎 - 轻量版"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        """初始化推演引擎"""
        self.symbol = symbol
        self.exchange_name = exchange
        self.exchange = None
        
        # 确保输出目录存在
        os.makedirs("charts/trend_evolution", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        logger.info(f"走势推演引擎(轻量版)初始化完成 - {symbol}")
    
    def _init_exchange(self):
        """初始化交易所连接"""
        if self.exchange is None:
            self.exchange = ccxt.gate({
                'timeout': 30000,
                'enableRateLimit': True,
            })
            self.exchange.load_markets()
            logger.info("交易所连接已建立")
    
    def fetch_market_data(self, timeframes: List[str] = None, limit: int = 100) -> Dict[str, List]:
        """获取市场数据"""
        if timeframes is None:
            timeframes = ['15m', '1h']
        
        try:
            self._init_exchange()
            market_data = {}
            
            for tf in timeframes:
                logger.info(f"获取 {tf} 级别数据...")
                ohlcv = self.exchange.fetch_ohlcv(
                    symbol=self.symbol,
                    timeframe=tf,
                    limit=min(limit, 1000)  # 限制最大数量
                )
                
                if ohlcv:
                    market_data[tf] = ohlcv
                    logger.info(f"成功获取 {tf} 级别数据: {len(ohlcv)} 条")
            
            return market_data
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {str(e)}")
            return {}
    
    def analyze_trend(self, ohlcv_data: List) -> Dict[str, Any]:
        """分析趋势"""
        try:
            if not ohlcv_data or len(ohlcv_data) < 20:
                return {'trend': 'unknown', 'strength': 0}
            
            # 提取价格数据
            prices = [candle[4] for candle in ohlcv_data]  # 收盘价
            highs = [candle[2] for candle in ohlcv_data]   # 最高价
            lows = [candle[3] for candle in ohlcv_data]    # 最低价
            volumes = [candle[5] for candle in ohlcv_data] # 成交量
            
            # 计算移动平均线
            ma_5 = sum(prices[-5:]) / 5
            ma_10 = sum(prices[-10:]) / 10
            ma_20 = sum(prices[-20:]) / 20
            
            current_price = prices[-1]
            
            # 趋势判断
            trend = 'consolidation'
            strength = 5
            
            if current_price > ma_5 > ma_10 > ma_20:
                trend = 'up'
                strength = 8
            elif current_price < ma_5 < ma_10 < ma_20:
                trend = 'down'
                strength = 8
            elif ma_5 > ma_20 and current_price > ma_10:
                trend = 'up'
                strength = 6
            elif ma_5 < ma_20 and current_price < ma_10:
                trend = 'down'
                strength = 6
            
            # 计算动量
            momentum = (prices[-1] - prices[-10]) / prices[-10] * 100
            
            # 计算波动率
            price_changes = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
            volatility = (sum([abs(change) for change in price_changes[-10:]]) / 10) * 100
            
            # 成交量分析
            avg_volume = sum(volumes[-10:]) / 10
            volume_trend = 'increasing' if volumes[-1] > avg_volume * 1.2 else 'normal'
            
            return {
                'trend': trend,
                'strength': strength,
                'momentum': momentum,
                'volatility': volatility,
                'volume_trend': volume_trend,
                'current_price': current_price,
                'ma_5': ma_5,
                'ma_10': ma_10,
                'ma_20': ma_20,
                'support_level': min(lows[-10:]),
                'resistance_level': max(highs[-10:])
            }
            
        except Exception as e:
            logger.error(f"趋势分析失败: {str(e)}")
            return {'trend': 'unknown', 'strength': 0}
    
    def generate_scenarios(self, analysis_15m: Dict, analysis_1h: Dict) -> List[TrendScenario]:
        """生成走势情景"""
        scenarios = []
        
        try:
            current_price = analysis_15m.get('current_price', 0)
            
            # 基于15分钟分析的主要情景
            trend_15m = analysis_15m.get('trend', 'consolidation')
            strength_15m = analysis_15m.get('strength', 5)
            momentum_15m = analysis_15m.get('momentum', 0)
            
            # 基于1小时分析的确认
            trend_1h = analysis_1h.get('trend', 'consolidation')
            strength_1h = analysis_1h.get('strength', 5)
            
            # 生成主要情景
            if trend_15m == 'up' and trend_1h == 'up':
                # 多级别上涨确认
                scenarios.append(TrendScenario(
                    trend_type=TrendType.UP,
                    probability=0.75,
                    target_price=current_price * 1.03,
                    time_horizon=60,
                    confidence_level='high',
                    supporting_factors=['15m上涨趋势', '1h级别确认', f'动量: {momentum_15m:.2f}%'],
                    risk_factors=['市场波动风险'],
                    description='多级别上涨趋势确认，建议逢低买入'
                ))
            elif trend_15m == 'down' and trend_1h == 'down':
                # 多级别下跌确认
                scenarios.append(TrendScenario(
                    trend_type=TrendType.DOWN,
                    probability=0.75,
                    target_price=current_price * 0.97,
                    time_horizon=60,
                    confidence_level='high',
                    supporting_factors=['15m下跌趋势', '1h级别确认', f'动量: {momentum_15m:.2f}%'],
                    risk_factors=['反弹风险'],
                    description='多级别下跌趋势确认，建议逢高卖出'
                ))
            else:
                # 震荡或不确定情景
                scenarios.append(TrendScenario(
                    trend_type=TrendType.CONSOLIDATION,
                    probability=0.60,
                    target_price=None,
                    time_horizon=120,
                    confidence_level='medium',
                    supporting_factors=['级别间存在分歧'],
                    risk_factors=['方向不明确'],
                    description='多级别趋势分歧，建议观望等待'
                ))
            
            # 生成备选情景
            if len(scenarios) > 0 and scenarios[0].trend_type != TrendType.CONSOLIDATION:
                # 添加反向情景
                if scenarios[0].trend_type == TrendType.UP:
                    scenarios.append(TrendScenario(
                        trend_type=TrendType.DOWN,
                        probability=0.20,
                        target_price=current_price * 0.98,
                        time_horizon=90,
                        confidence_level='low',
                        supporting_factors=[],
                        risk_factors=['趋势反转风险'],
                        description='上涨失败后的回调情景'
                    ))
                else:
                    scenarios.append(TrendScenario(
                        trend_type=TrendType.UP,
                        probability=0.20,
                        target_price=current_price * 1.02,
                        time_horizon=90,
                        confidence_level='low',
                        supporting_factors=[],
                        risk_factors=['下跌反弹风险'],
                        description='下跌后的技术反弹情景'
                    ))
            
            return scenarios
            
        except Exception as e:
            logger.error(f"生成情景失败: {str(e)}")
            return [TrendScenario(
                trend_type=TrendType.CONSOLIDATION,
                probability=1.0,
                target_price=None,
                time_horizon=60,
                confidence_level='low',
                supporting_factors=[],
                risk_factors=['数据不足'],
                description='数据不足，建议等待'
            )]
    
    def evolve_trend_live(self) -> EvolutionResult:
        """执行走势推演"""
        try:
            logger.info("开始执行走势推演...")
            
            # 获取多级别数据
            market_data = self.fetch_market_data(['15m', '1h'], 100)
            
            if not market_data:
                raise Exception("无法获取市场数据")
            
            # 分析各级别趋势
            analysis_15m = self.analyze_trend(market_data.get('15m', []))
            analysis_1h = self.analyze_trend(market_data.get('1h', []))
            
            # 生成走势情景
            scenarios = self.generate_scenarios(analysis_15m, analysis_1h)
            
            if not scenarios:
                raise Exception("无法生成走势情景")
            
            # 选择主要情景
            primary_scenario = scenarios[0]
            alternative_scenarios = scenarios[1:] if len(scenarios) > 1 else []
            
            # 生成操作建议
            if primary_scenario.trend_type == TrendType.UP:
                recommended_action = "buy"
                position_size = "medium" if primary_scenario.confidence_level == 'high' else "light"
            elif primary_scenario.trend_type == TrendType.DOWN:
                recommended_action = "sell"
                position_size = "medium" if primary_scenario.confidence_level == 'high' else "light"
            else:
                recommended_action = "wait"
                position_size = "light"
            
            # 计算止盈止损
            current_price = analysis_15m.get('current_price', 0)
            profit_target = primary_scenario.target_price
            
            if primary_scenario.trend_type == TrendType.UP:
                stop_loss = current_price * 0.98
            elif primary_scenario.trend_type == TrendType.DOWN:
                stop_loss = current_price * 1.02
            else:
                stop_loss = None
            
            # 生成图表信息
            chart_info = self._generate_chart_info(analysis_15m, analysis_1h, primary_scenario)
            
            result = EvolutionResult(
                primary_scenario=primary_scenario,
                alternative_scenarios=alternative_scenarios,
                recommended_action=recommended_action,
                profit_target=profit_target,
                stop_loss=stop_loss,
                position_size=position_size,
                analysis_timestamp=datetime.now(),
                data_source="live",
                chart_info=chart_info
            )
            
            logger.info(f"走势推演完成 - 主要情景: {primary_scenario.trend_type.value}")
            return result
            
        except Exception as e:
            logger.error(f"走势推演失败: {str(e)}")
            return self._create_default_result()
    
    def _generate_chart_info(self, analysis_15m: Dict, analysis_1h: Dict, scenario: TrendScenario) -> str:
        """生成图表信息"""
        try:
            chart_data = {
                'symbol': self.symbol,
                'timestamp': datetime.now().isoformat(),
                'current_price': analysis_15m.get('current_price', 0),
                'trend_15m': analysis_15m.get('trend', 'unknown'),
                'trend_1h': analysis_1h.get('trend', 'unknown'),
                'scenario': scenario.trend_type.value,
                'probability': scenario.probability,
                'support': analysis_15m.get('support_level', 0),
                'resistance': analysis_15m.get('resistance_level', 0),
                'ma_levels': {
                    'ma_5': analysis_15m.get('ma_5', 0),
                    'ma_10': analysis_15m.get('ma_10', 0),
                    'ma_20': analysis_15m.get('ma_20', 0)
                }
            }
            
            # 保存图表数据
            chart_file = f"charts/trend_evolution/{self.symbol.replace('/', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(chart_file, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f, indent=2, ensure_ascii=False)
            
            return chart_file
            
        except Exception as e:
            logger.error(f"生成图表信息失败: {str(e)}")
            return None
    
    def _create_default_result(self) -> EvolutionResult:
        """创建默认结果"""
        default_scenario = TrendScenario(
            trend_type=TrendType.CONSOLIDATION,
            probability=1.0,
            target_price=None,
            time_horizon=60,
            confidence_level='low',
            supporting_factors=['数据不足'],
            risk_factors=['高不确定性'],
            description='数据不足，建议等待'
        )
        
        return EvolutionResult(
            primary_scenario=default_scenario,
            alternative_scenarios=[],
            recommended_action='wait',
            profit_target=None,
            stop_loss=None,
            position_size='light',
            analysis_timestamp=datetime.now()
        )

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强回测系统
集成完整的交易系统进行历史回测，严格不倒果为因
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from loguru import logger
import sys
import os
import copy

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_fetcher import DataFetcher
from multi_level_analysis import MultiLevelAnalysis
from chan_analysis import ChanAnalysis

@dataclass
class BacktestSignal:
    """回测信号记录"""
    timestamp: datetime
    action: str                    # buy/sell/wait
    entry_price: float
    stop_loss: float
    take_profits: List[float]
    confidence: float
    reasoning: str
    risk_level: str
    timeframe: str
    major_trend_status: Dict[str, Any]

@dataclass
class TradeResult:
    """交易结果"""
    signal: BacktestSignal
    entry_time: datetime
    exit_time: Optional[datetime]
    exit_price: Optional[float]
    exit_reason: str               # stop_loss/take_profit_1/take_profit_2/take_profit_3/timeout
    profit_loss: float             # 盈亏金额
    profit_loss_pct: float         # 盈亏百分比
    duration_minutes: int          # 持仓时间(分钟)
    success: bool                  # 是否成功

@dataclass
class BacktestReport:
    """回测报告"""
    start_time: datetime
    end_time: datetime
    total_signals: int
    buy_signals: int
    sell_signals: int
    wait_signals: int
    total_trades: int
    successful_trades: int
    failed_trades: int
    win_rate: float
    total_profit_loss: float
    total_profit_loss_pct: float
    max_drawdown: float
    sharpe_ratio: float
    trade_results: List[TradeResult]
    signal_accuracy: float
    avg_holding_time: float

class EnhancedBacktest:
    """增强回测系统"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        self.symbol = symbol
        self.exchange = exchange
        
        # 回测配置
        self.config = {
            'max_holding_time': 240,      # 最大持仓时间(分钟)
            'initial_capital': 10000,     # 初始资金
            'position_size': 0.1,         # 每次交易仓位大小
            'commission_rate': 0.001,     # 手续费率
            'slippage': 0.0005,          # 滑点
            'min_data_points': 200,       # 最少数据点数
        }
        
        # 多级别分析器
        self.multi_level_analyzer = None
        
        logger.info(f"增强回测系统初始化完成 - {symbol}")
    
    def run_enhanced_backtest(self, start_date: str, end_date: str, 
                            primary_timeframe: str = '15m') -> BacktestReport:
        """
        运行增强回测
        
        参数:
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            primary_timeframe: 主要分析时间周期
            
        返回:
            BacktestReport: 回测报告
        """
        try:
            logger.info(f"开始增强回测: {start_date} 到 {end_date}, 主周期: {primary_timeframe}")
            
            # 1. 初始化多级别分析器
            self.multi_level_analyzer = MultiLevelAnalysis(
                symbol=self.symbol, 
                exchange_id=self.exchange
            )
            
            # 2. 获取主时间周期的历史数据
            primary_data = self._get_historical_data(start_date, end_date, primary_timeframe)
            if primary_data.empty:
                raise ValueError("无法获取主时间周期历史数据")
            
            logger.info(f"获取主时间周期数据成功: {len(primary_data)} 条记录")
            
            # 3. 按时间顺序逐个分析
            signals = []
            trades = []
            
            # 需要足够的历史数据来进行分析
            start_index = self.config['min_data_points']
            
            for i in range(start_index, len(primary_data)):
                current_time = primary_data.index[i]
                
                # 4. 获取当前时刻之前的数据（严格不倒果为因）
                available_data = primary_data.iloc[:i+1]
                
                # 5. 基于可用数据进行缠论分析
                signal = self._analyze_with_chan_theory(available_data, current_time, primary_timeframe)
                
                if signal:
                    signals.append(signal)
                    
                    # 6. 如果是买卖信号，模拟交易
                    if signal.action in ['buy', 'sell']:
                        trade_result = self._simulate_trade(signal, primary_data, i)
                        if trade_result:
                            trades.append(trade_result)
                
                # 每50个点输出一次进度
                if i % 50 == 0:
                    progress = (i - start_index) / (len(primary_data) - start_index) * 100
                    logger.info(f"回测进度: {progress:.1f}% ({i}/{len(primary_data)})")
            
            # 7. 生成回测报告
            report = self._generate_backtest_report(signals, trades, 
                                                  primary_data.index[start_index], 
                                                  primary_data.index[-1])
            
            logger.info(f"增强回测完成: 总信号{len(signals)}个, 交易{len(trades)}笔")
            return report
            
        except Exception as e:
            logger.error(f"增强回测失败: {str(e)}")
            raise
    
    def _get_historical_data(self, start_date: str, end_date: str, timeframe: str) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 初始化数据获取器
            data_fetcher = DataFetcher(
                exchange_id=self.exchange, 
                symbols=[self.symbol], 
                timeframe=timeframe
            )
            
            # 计算需要的数据量
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            days = (end_dt - start_dt).days + 1
            
            # 根据时间周期计算K线数量
            timeframe_minutes = {
                '1m': 1, '5m': 5, '15m': 15, '30m': 30, 
                '1h': 60, '4h': 240, '1d': 1440
            }
            
            minutes_per_day = 1440
            period_minutes = timeframe_minutes.get(timeframe, 15)
            limit = int(days * minutes_per_day / period_minutes) + 500  # 多获取一些数据
            
            # 获取数据
            df = data_fetcher.get_klines(symbol=self.symbol, limit=limit)
            
            if not df.empty:
                # 设置时间戳为索引
                df.set_index('timestamp', inplace=True)
                
                # 过滤到指定日期范围
                start_filter = pd.to_datetime(start_date)
                end_filter = pd.to_datetime(end_date) + timedelta(days=1)
                
                df = df[(df.index >= start_filter) & (df.index < end_filter)]
                
                logger.info(f"获取历史数据成功: {len(df)} 条 {timeframe} 数据")
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _analyze_with_chan_theory(self, available_data: pd.DataFrame, 
                                current_time: datetime, timeframe: str) -> Optional[BacktestSignal]:
        """
        使用缠论进行分析（严格不使用未来数据）
        
        参数:
            available_data: 当前时刻及之前的所有可用数据
            current_time: 当前分析时间点
            timeframe: 时间周期
            
        返回:
            BacktestSignal: 分析信号
        """
        try:
            # 确保有足够的数据进行分析
            if len(available_data) < 50:
                return None
            
            # 使用最后200个数据点进行分析（如果有的话）
            analysis_data = available_data.tail(200) if len(available_data) >= 200 else available_data
            
            # 重置索引以便缠论分析器使用
            analysis_data_reset = analysis_data.reset_index()
            
            # 创建缠论分析器
            chan_analyzer = ChanAnalysis()
            
            # 进行缠论分析
            chan_analyzer.analyze(analysis_data_reset)
            
            # 获取分析结果
            signal = self._generate_signal_from_chan_analysis(
                chan_analyzer, analysis_data, current_time, timeframe
            )
            
            return signal
            
        except Exception as e:
            logger.warning(f"在时间点 {current_time} 缠论分析失败: {str(e)}")
            return None
    
    def _generate_signal_from_chan_analysis(self, chan_analyzer: ChanAnalysis, 
                                          data: pd.DataFrame, timestamp: datetime, 
                                          timeframe: str) -> Optional[BacktestSignal]:
        """
        基于缠论分析结果生成信号
        """
        try:
            # 获取最新价格
            latest_price = data['close'].iloc[-1]
            
            # 检查是否有买卖点
            buy_sell_points = chan_analyzer.buy_sell_points
            
            action = 'wait'
            confidence = 0.5
            reasoning = "缠论分析：无明确买卖点"
            
            if buy_sell_points:
                # 获取最新的买卖点
                latest_point = buy_sell_points[-1]
                point_type = latest_point.get('type', '')
                point_level = latest_point.get('level', 0)
                
                # 检查买卖点的时效性（最近5个K线内）
                point_index = latest_point.get('index', 0)
                current_index = len(data) - 1
                
                if current_index - point_index <= 5:  # 5个K线内的信号
                    if '买' in point_type:
                        action = 'buy'
                        confidence = min(0.8, 0.5 + point_level * 0.1)
                        reasoning = f"缠论分析：检测到{point_type}，级别{point_level}"
                    elif '卖' in point_type:
                        action = 'sell'
                        confidence = min(0.8, 0.5 + point_level * 0.1)
                        reasoning = f"缠论分析：检测到{point_type}，级别{point_level}"
            
            # 计算止盈止损
            stop_loss, take_profits = self._calculate_stop_profit_levels(
                action, latest_price, data, chan_analyzer
            )
            
            # 评估风险等级
            risk_level = self._assess_risk_level(confidence, data)
            
            # 创建信号
            signal = BacktestSignal(
                timestamp=timestamp,
                action=action,
                entry_price=latest_price,
                stop_loss=stop_loss,
                take_profits=take_profits,
                confidence=confidence,
                reasoning=reasoning,
                risk_level=risk_level,
                timeframe=timeframe,
                major_trend_status={'status': 'unknown', 'confidence': confidence}
            )
            
            return signal
            
        except Exception as e:
            logger.warning(f"生成缠论信号失败: {str(e)}")
            return None
    
    def _calculate_stop_profit_levels(self, action: str, entry_price: float, 
                                    data: pd.DataFrame, chan_analyzer: ChanAnalysis) -> Tuple[float, List[float]]:
        """计算止盈止损水平"""
        try:
            if action not in ['buy', 'sell']:
                return 0, []
            
            # 计算ATR作为波动率参考
            high_low = data['high'] - data['low']
            high_close = abs(data['high'] - data['close'].shift(1))
            low_close = abs(data['low'] - data['close'].shift(1))
            
            tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = tr.rolling(14).mean().iloc[-1]
            
            if pd.isna(atr) or atr == 0:
                atr = entry_price * 0.02  # 默认2%
            
            if action == 'buy':
                # 买入止损止盈
                stop_loss = entry_price - atr * 2  # 2倍ATR止损
                take_profits = [
                    entry_price + atr * 2,    # 第一止盈：2倍ATR
                    entry_price + atr * 4,    # 第二止盈：4倍ATR
                    entry_price + atr * 6     # 第三止盈：6倍ATR
                ]
            else:  # sell
                # 卖出止损止盈
                stop_loss = entry_price + atr * 2  # 2倍ATR止损
                take_profits = [
                    entry_price - atr * 2,    # 第一止盈：2倍ATR
                    entry_price - atr * 4,    # 第二止盈：4倍ATR
                    entry_price - atr * 6     # 第三止盈：6倍ATR
                ]
            
            return stop_loss, take_profits
            
        except Exception as e:
            logger.warning(f"计算止盈止损失败: {str(e)}")
            # 返回默认值
            if action == 'buy':
                return entry_price * 0.98, [entry_price * 1.02, entry_price * 1.04, entry_price * 1.06]
            else:
                return entry_price * 1.02, [entry_price * 0.98, entry_price * 0.96, entry_price * 0.94]
    
    def _assess_risk_level(self, confidence: float, data: pd.DataFrame) -> str:
        """评估风险等级"""
        try:
            # 基于置信度和市场波动率评估风险
            volatility = data['close'].pct_change().std() * 100  # 转换为百分比
            
            if confidence >= 0.7 and volatility <= 3:
                return 'LOW'
            elif confidence >= 0.5 and volatility <= 5:
                return 'MEDIUM'
            else:
                return 'HIGH'
                
        except Exception as e:
            logger.warning(f"评估风险等级失败: {str(e)}")
            return 'MEDIUM'

    def _simulate_trade(self, signal: BacktestSignal, full_data: pd.DataFrame,
                       entry_index: int) -> Optional[TradeResult]:
        """
        模拟交易执行

        参数:
            signal: 交易信号
            full_data: 完整的历史数据
            entry_index: 入场时间在数据中的索引

        返回:
            TradeResult: 交易结果
        """
        try:
            if signal.action not in ['buy', 'sell']:
                return None

            entry_time = signal.timestamp
            entry_price = signal.entry_price
            stop_loss = signal.stop_loss
            take_profits = signal.take_profits

            # 模拟交易执行
            max_holding_time = self.config['max_holding_time']
            end_index = min(entry_index + max_holding_time, len(full_data) - 1)

            # 检查后续价格走势
            for i in range(entry_index + 1, end_index + 1):
                current_bar = full_data.iloc[i]
                current_time = full_data.index[i]

                high = current_bar['high']
                low = current_bar['low']
                close = current_bar['close']

                # 检查止损
                if signal.action == 'buy':
                    if low <= stop_loss:
                        return TradeResult(
                            signal=signal,
                            entry_time=entry_time,
                            exit_time=current_time,
                            exit_price=stop_loss,
                            exit_reason='stop_loss',
                            profit_loss=(stop_loss - entry_price) * self.config['position_size'],
                            profit_loss_pct=(stop_loss - entry_price) / entry_price * 100,
                            duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                            success=False
                        )

                    # 检查止盈
                    for j, tp in enumerate(take_profits):
                        if high >= tp:
                            return TradeResult(
                                signal=signal,
                                entry_time=entry_time,
                                exit_time=current_time,
                                exit_price=tp,
                                exit_reason=f'take_profit_{j+1}',
                                profit_loss=(tp - entry_price) * self.config['position_size'],
                                profit_loss_pct=(tp - entry_price) / entry_price * 100,
                                duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                                success=True
                            )

                elif signal.action == 'sell':
                    if high >= stop_loss:
                        return TradeResult(
                            signal=signal,
                            entry_time=entry_time,
                            exit_time=current_time,
                            exit_price=stop_loss,
                            exit_reason='stop_loss',
                            profit_loss=(entry_price - stop_loss) * self.config['position_size'],
                            profit_loss_pct=(entry_price - stop_loss) / entry_price * 100,
                            duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                            success=False
                        )

                    # 检查止盈
                    for j, tp in enumerate(take_profits):
                        if low <= tp:
                            return TradeResult(
                                signal=signal,
                                entry_time=entry_time,
                                exit_time=current_time,
                                exit_price=tp,
                                exit_reason=f'take_profit_{j+1}',
                                profit_loss=(entry_price - tp) * self.config['position_size'],
                                profit_loss_pct=(entry_price - tp) / entry_price * 100,
                                duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                                success=True
                            )

            # 超时平仓
            final_bar = full_data.iloc[end_index]
            final_time = full_data.index[end_index]
            final_price = final_bar['close']

            if signal.action == 'buy':
                profit_loss = (final_price - entry_price) * self.config['position_size']
                profit_loss_pct = (final_price - entry_price) / entry_price * 100
            else:  # sell
                profit_loss = (entry_price - final_price) * self.config['position_size']
                profit_loss_pct = (entry_price - final_price) / entry_price * 100

            return TradeResult(
                signal=signal,
                entry_time=entry_time,
                exit_time=final_time,
                exit_price=final_price,
                exit_reason='timeout',
                profit_loss=profit_loss,
                profit_loss_pct=profit_loss_pct,
                duration_minutes=int((final_time - entry_time).total_seconds() / 60),
                success=profit_loss > 0
            )

        except Exception as e:
            logger.warning(f"模拟交易失败: {str(e)}")
            return None

    def _generate_backtest_report(self, signals: List[BacktestSignal], trades: List[TradeResult],
                                start_time: datetime, end_time: datetime) -> BacktestReport:
        """生成回测报告"""
        try:
            # 统计信号
            total_signals = len(signals)
            buy_signals = len([s for s in signals if s.action == 'buy'])
            sell_signals = len([s for s in signals if s.action == 'sell'])
            wait_signals = len([s for s in signals if s.action == 'wait'])

            # 统计交易
            total_trades = len(trades)
            successful_trades = len([t for t in trades if t.success])
            failed_trades = total_trades - successful_trades

            # 计算胜率
            win_rate = successful_trades / total_trades if total_trades > 0 else 0

            # 计算总盈亏
            total_profit_loss = sum(t.profit_loss for t in trades)
            total_profit_loss_pct = sum(t.profit_loss_pct for t in trades)

            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(trades)

            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio(trades)

            # 计算信号准确率
            signal_accuracy = self._calculate_signal_accuracy(signals, trades)

            # 计算平均持仓时间
            avg_holding_time = sum(t.duration_minutes for t in trades) / len(trades) if trades else 0

            report = BacktestReport(
                start_time=start_time,
                end_time=end_time,
                total_signals=total_signals,
                buy_signals=buy_signals,
                sell_signals=sell_signals,
                wait_signals=wait_signals,
                total_trades=total_trades,
                successful_trades=successful_trades,
                failed_trades=failed_trades,
                win_rate=win_rate,
                total_profit_loss=total_profit_loss,
                total_profit_loss_pct=total_profit_loss_pct,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                trade_results=trades,
                signal_accuracy=signal_accuracy,
                avg_holding_time=avg_holding_time
            )

            return report

        except Exception as e:
            logger.error(f"生成回测报告失败: {str(e)}")
            raise

    def _calculate_max_drawdown(self, trades: List[TradeResult]) -> float:
        """计算最大回撤"""
        try:
            if not trades:
                return 0.0

            cumulative_returns = []
            cumulative = 0

            for trade in trades:
                cumulative += trade.profit_loss_pct
                cumulative_returns.append(cumulative)

            if not cumulative_returns:
                return 0.0

            peak = cumulative_returns[0]
            max_drawdown = 0

            for return_val in cumulative_returns:
                if return_val > peak:
                    peak = return_val

                drawdown = peak - return_val
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            return max_drawdown

        except Exception as e:
            logger.warning(f"计算最大回撤失败: {str(e)}")
            return 0.0

    def _calculate_sharpe_ratio(self, trades: List[TradeResult]) -> float:
        """计算夏普比率"""
        try:
            if not trades:
                return 0.0

            returns = [t.profit_loss_pct for t in trades]

            if len(returns) < 2:
                return 0.0

            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return == 0:
                return 0.0

            # 假设无风险利率为0
            sharpe_ratio = mean_return / std_return

            return sharpe_ratio

        except Exception as e:
            logger.warning(f"计算夏普比率失败: {str(e)}")
            return 0.0

    def _calculate_signal_accuracy(self, signals: List[BacktestSignal], trades: List[TradeResult]) -> float:
        """计算信号准确率"""
        try:
            if not trades:
                return 0.0

            # 简单计算：成功交易数 / 总交易数
            successful_trades = len([t for t in trades if t.success])
            total_trades = len(trades)

            return successful_trades / total_trades if total_trades > 0 else 0.0

        except Exception as e:
            logger.warning(f"计算信号准确率失败: {str(e)}")
            return 0.0

    def print_enhanced_backtest_report(self, report: BacktestReport):
        """打印增强回测报告"""
        print("=" * 80)
        print("📊 增强回测报告 (基于缠论分析)")
        print("=" * 80)

        # 基本信息
        print(f"\n📅 回测期间: {report.start_time.strftime('%Y-%m-%d %H:%M')} 到 {report.end_time.strftime('%Y-%m-%d %H:%M')}")
        print(f"⏱️ 回测时长: {(report.end_time - report.start_time).days} 天 {(report.end_time - report.start_time).seconds // 3600} 小时")

        # 信号统计
        print(f"\n📡 缠论信号统计:")
        print(f"   总信号数: {report.total_signals}")
        print(f"   买入信号: {report.buy_signals} ({report.buy_signals/report.total_signals*100:.1f}%)")
        print(f"   卖出信号: {report.sell_signals} ({report.sell_signals/report.total_signals*100:.1f}%)")
        print(f"   等待信号: {report.wait_signals} ({report.wait_signals/report.total_signals*100:.1f}%)")

        # 交易统计
        print(f"\n💼 交易执行统计:")
        print(f"   总交易数: {report.total_trades}")
        print(f"   成功交易: {report.successful_trades}")
        print(f"   失败交易: {report.failed_trades}")
        print(f"   胜率: {report.win_rate:.1%}")
        print(f"   信号准确率: {report.signal_accuracy:.1%}")

        # 盈亏统计
        print(f"\n💰 盈亏统计:")
        print(f"   总盈亏: {report.total_profit_loss:.2f} USDT")
        print(f"   总盈亏率: {report.total_profit_loss_pct:.2f}%")
        print(f"   最大回撤: {report.max_drawdown:.2f}%")
        print(f"   夏普比率: {report.sharpe_ratio:.2f}")
        print(f"   平均持仓时间: {report.avg_holding_time:.1f} 分钟")

        # 详细交易记录（显示前10笔）
        if report.trade_results:
            print(f"\n📋 交易记录详情 (前10笔):")
            print(f"{'时间':<20} {'动作':<6} {'入场价':<10} {'出场价':<10} {'盈亏%':<8} {'持仓时间':<10} {'退出原因':<15}")
            print("-" * 90)

            for i, trade in enumerate(report.trade_results[:10]):
                action_emoji = "📈" if trade.signal.action == 'buy' else "📉"
                success_emoji = "✅" if trade.success else "❌"

                print(f"{trade.entry_time.strftime('%m-%d %H:%M'):<20} "
                      f"{action_emoji}{trade.signal.action:<5} "
                      f"{trade.signal.entry_price:<10.2f} "
                      f"{trade.exit_price:<10.2f} "
                      f"{success_emoji}{trade.profit_loss_pct:<7.2f} "
                      f"{trade.duration_minutes:<10} "
                      f"{trade.exit_reason:<15}")

            if len(report.trade_results) > 10:
                print(f"... 还有 {len(report.trade_results) - 10} 笔交易")

        # 缠论分析总结
        print(f"\n🎯 缠论回测总结:")
        if report.win_rate >= 0.6:
            print("   ✅ 缠论系统表现优秀，买卖点识别准确")
        elif report.win_rate >= 0.4:
            print("   🟡 缠论系统表现一般，需要优化参数")
        else:
            print("   ❌ 缠论系统表现较差，需要重新调整策略")

        if report.total_profit_loss_pct > 0:
            print(f"   💰 总体盈利 {report.total_profit_loss_pct:.2f}%，缠论策略有效")
        else:
            print(f"   📉 总体亏损 {abs(report.total_profit_loss_pct):.2f}%，需要优化缠论参数")

        # 改进建议
        print(f"\n💡 缠论系统改进建议:")
        if report.win_rate < 0.5:
            print("   • 提高买卖点识别的准确性")
            print("   • 增加多级别联立验证")

        if report.max_drawdown > 10:
            print("   • 优化止损策略，基于缠论结构设置止损")
            print("   • 加强中枢理论的应用")

        if report.total_trades < 5:
            print("   • 信号过少，考虑降低买卖点识别门槛")
        elif report.total_trades > 50:
            print("   • 信号过多，考虑提高买卖点质量要求")

        print("=" * 80)

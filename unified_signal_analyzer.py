#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一信号分析器 - 多级别联立买卖点分析
解决问题1: 买卖点要结合多级别联立，小周期符合大周期
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from multi_level_analysis import MultiLevelAnalysis
from chan_analysis import ChanAnalysis

class TrendDirection(Enum):
    """趋势方向"""
    BULLISH = "bullish"      # 看多
    BEARISH = "bearish"      # 看空
    NEUTRAL = "neutral"      # 中性
    CHANGING = "changing"    # 变盘中

class SignalStrength(Enum):
    """信号强度"""
    VERY_STRONG = "very_strong"  # 非常强
    STRONG = "strong"            # 强
    MEDIUM = "medium"            # 中等
    WEAK = "weak"                # 弱
    INVALID = "invalid"          # 无效

@dataclass
class UnifiedSignal:
    """统一信号"""
    action: str                    # buy/sell/wait
    signal_type: str              # 第一类/第二类/第三类买卖点
    strength: SignalStrength      # 信号强度
    confidence: float             # 置信度 0-1
    entry_price: float            # 入场价格
    stop_loss: float              # 止损价格
    take_profits: List[float]     # 止盈价格列表
    timeframe: str                # 主要时间周期
    supporting_timeframes: List[str]  # 支持的时间周期
    conflicting_timeframes: List[str] # 冲突的时间周期
    reasoning: str                # 详细理由
    risk_level: str               # 风险等级
    position_size: str            # 仓位建议
    validity_period: int          # 有效期(分钟)

class UnifiedSignalAnalyzer:
    """统一信号分析器"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        self.symbol = symbol
        self.exchange = exchange
        
        # 时间周期层级 (从小到大)
        self.timeframes = ['5m', '15m', '30m', '1h', '4h', '1d']
        self.major_timeframes = ['1h', '4h', '1d']  # 大周期
        self.minor_timeframes = ['5m', '15m', '30m']  # 小周期
        
        # 初始化多级别分析器
        self.multi_level_analyzer = MultiLevelAnalysis(
            symbol=symbol,
            exchange_id=exchange
        )
        
        # 权重配置
        self.timeframe_weights = {
            '1d': 1.0,   # 日线权重最高
            '4h': 0.8,   # 4小时次之
            '1h': 0.6,   # 1小时
            '30m': 0.4,  # 30分钟
            '15m': 0.3,  # 15分钟
            '5m': 0.2    # 5分钟权重最低
        }
        
        logger.info(f"统一信号分析器初始化完成 - {symbol}")
    
    def analyze_unified_signals(self) -> Optional[UnifiedSignal]:
        """
        分析统一信号
        
        返回:
            UnifiedSignal: 统一的买卖信号，如果没有有效信号返回None
        """
        try:
            logger.info("开始统一信号分析")
            
            # 1. 获取多级别数据和分析结果
            multi_data = self.multi_level_analyzer.get_multi_level_data(limit=200)
            if not multi_data:
                logger.error("无法获取多级别数据")
                return None
            
            # 2. 分析各级别趋势方向
            level_trends = self._analyze_level_trends(multi_data)
            
            # 3. 确定大周期主导方向
            major_direction = self._determine_major_direction(level_trends)
            
            # 4. 检查是否处于变盘期
            is_changing = self._check_trend_changing(level_trends, multi_data)
            
            # 5. 如果大周期即将变盘，建议空仓等待
            if is_changing:
                return self._create_wait_signal("大周期即将变盘，建议空仓等待")
            
            # 6. 基于大周期方向，寻找小周期入场机会
            unified_signal = self._find_entry_opportunities(
                major_direction, level_trends, multi_data
            )
            
            # 7. 验证信号合理性
            if unified_signal:
                unified_signal = self._validate_signal_quality(unified_signal, multi_data)
            
            logger.info(f"统一信号分析完成: {unified_signal.action if unified_signal else 'None'}")
            return unified_signal
            
        except Exception as e:
            logger.error(f"统一信号分析失败: {str(e)}")
            return None
    
    def _analyze_level_trends(self, multi_data: Dict[str, pd.DataFrame]) -> Dict[str, TrendDirection]:
        """分析各级别趋势方向"""
        level_trends = {}
        
        for timeframe, df in multi_data.items():
            try:
                # 创建缠论分析器
                analyzer = ChanAnalysis(df)
                
                # 执行分析
                analyzer.analyze(df)
                
                # 获取最新的笔和线段
                bi_list = analyzer.bi_list
                xd_list = analyzer.xd_list
                
                # 判断趋势方向
                trend = self._determine_trend_direction(bi_list, xd_list, df)
                level_trends[timeframe] = trend
                
                logger.debug(f"{timeframe} 级别趋势: {trend.value}")
                
            except Exception as e:
                logger.warning(f"分析 {timeframe} 级别趋势失败: {str(e)}")
                level_trends[timeframe] = TrendDirection.NEUTRAL
        
        return level_trends
    
    def _determine_trend_direction(self, bi_list: List, xd_list: List, df: pd.DataFrame) -> TrendDirection:
        """确定趋势方向"""
        try:
            # 基于最新的笔和线段方向判断
            if not bi_list or not xd_list:
                return TrendDirection.NEUTRAL
            
            # 获取最新笔和线段的方向
            latest_bi_direction = bi_list[-1][4] if len(bi_list[-1]) > 4 else 'unknown'
            latest_xd_direction = xd_list[-1][4] if len(xd_list[-1]) > 4 else 'unknown'
            
            # 获取价格趋势
            current_price = df['close'].iloc[-1]
            ma20 = df['ma20'].iloc[-1] if 'ma20' in df.columns else current_price
            ma60 = df['ma60'].iloc[-1] if 'ma60' in df.columns else current_price
            
            # 综合判断
            bullish_signals = 0
            bearish_signals = 0
            
            # 笔的方向
            if latest_bi_direction == 'up':
                bullish_signals += 1
            elif latest_bi_direction == 'down':
                bearish_signals += 1
            
            # 线段的方向
            if latest_xd_direction == 'up':
                bullish_signals += 2  # 线段权重更高
            elif latest_xd_direction == 'down':
                bearish_signals += 2
            
            # 均线关系
            if current_price > ma20 > ma60:
                bullish_signals += 1
            elif current_price < ma20 < ma60:
                bearish_signals += 1
            
            # 判断结果
            if bullish_signals > bearish_signals + 1:
                return TrendDirection.BULLISH
            elif bearish_signals > bullish_signals + 1:
                return TrendDirection.BEARISH
            else:
                return TrendDirection.NEUTRAL
                
        except Exception as e:
            logger.warning(f"确定趋势方向失败: {str(e)}")
            return TrendDirection.NEUTRAL
    
    def _determine_major_direction(self, level_trends: Dict[str, TrendDirection]) -> TrendDirection:
        """确定大周期主导方向"""
        try:
            # 计算加权趋势得分
            bullish_score = 0
            bearish_score = 0
            
            for timeframe, trend in level_trends.items():
                weight = self.timeframe_weights.get(timeframe, 0.1)
                
                if trend == TrendDirection.BULLISH:
                    bullish_score += weight
                elif trend == TrendDirection.BEARISH:
                    bearish_score += weight
            
            # 判断主导方向
            if bullish_score > bearish_score * 1.2:  # 需要明显优势
                return TrendDirection.BULLISH
            elif bearish_score > bullish_score * 1.2:
                return TrendDirection.BEARISH
            else:
                return TrendDirection.NEUTRAL
                
        except Exception as e:
            logger.warning(f"确定大周期方向失败: {str(e)}")
            return TrendDirection.NEUTRAL
    
    def _check_trend_changing(self, level_trends: Dict[str, TrendDirection], 
                            multi_data: Dict[str, pd.DataFrame]) -> bool:
        """检查是否处于变盘期"""
        try:
            # 检查大周期是否出现背离或结构破坏
            changing_signals = 0
            
            for timeframe in self.major_timeframes:
                if timeframe not in multi_data:
                    continue
                
                df = multi_data[timeframe]
                
                # 检查MACD背离
                if self._check_macd_divergence(df):
                    changing_signals += 1
                
                # 检查关键支撑阻力位
                if self._check_key_level_break(df):
                    changing_signals += 1
                
                # 检查成交量异常
                if self._check_volume_anomaly(df):
                    changing_signals += 1
            
            # 如果多个大周期出现变盘信号，认为即将变盘
            return changing_signals >= 2
            
        except Exception as e:
            logger.warning(f"检查变盘期失败: {str(e)}")
            return False
    
    def _find_entry_opportunities(self, major_direction: TrendDirection, 
                                level_trends: Dict[str, TrendDirection],
                                multi_data: Dict[str, pd.DataFrame]) -> Optional[UnifiedSignal]:
        """基于大周期方向寻找小周期入场机会"""
        try:
            if major_direction == TrendDirection.NEUTRAL:
                return self._create_wait_signal("大周期方向不明确，建议等待")
            
            # 只寻找与大周期方向一致的机会
            target_action = 'buy' if major_direction == TrendDirection.BULLISH else 'sell'
            
            # 在小周期中寻找入场机会
            best_signal = None
            best_score = 0
            
            for timeframe in self.minor_timeframes:
                if timeframe not in multi_data:
                    continue
                
                # 获取该级别的买卖点
                signals = self._get_timeframe_signals(timeframe, multi_data[timeframe], target_action)
                
                for signal in signals:
                    # 计算信号质量得分
                    score = self._calculate_signal_score(signal, level_trends, major_direction)
                    
                    if score > best_score:
                        best_score = score
                        best_signal = signal
            
            return best_signal
            
        except Exception as e:
            logger.warning(f"寻找入场机会失败: {str(e)}")
            return None
    
    def _create_wait_signal(self, reason: str) -> UnifiedSignal:
        """创建等待信号"""
        return UnifiedSignal(
            action='wait',
            signal_type='等待信号',
            strength=SignalStrength.MEDIUM,
            confidence=0.8,
            entry_price=0,
            stop_loss=0,
            take_profits=[],
            timeframe='all',
            supporting_timeframes=[],
            conflicting_timeframes=[],
            reasoning=reason,
            risk_level='LOW',
            position_size='NONE',
            validity_period=60
        )
    
    def _check_macd_divergence(self, df: pd.DataFrame) -> bool:
        """检查MACD背离"""
        try:
            if 'macd' not in df.columns or len(df) < 20:
                return False
            
            # 简化的背离检测
            recent_prices = df['close'].tail(10)
            recent_macd = df['macd'].tail(10)
            
            # 价格新高但MACD未新高，或价格新低但MACD未新低
            price_trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
            macd_trend = recent_macd.iloc[-1] - recent_macd.iloc[0]
            
            # 背离判断
            return (price_trend > 0 and macd_trend < 0) or (price_trend < 0 and macd_trend > 0)
            
        except Exception as e:
            logger.warning(f"检查MACD背离失败: {str(e)}")
            return False
    
    def _check_key_level_break(self, df: pd.DataFrame) -> bool:
        """检查关键位突破"""
        try:
            if len(df) < 50:
                return False
            
            current_price = df['close'].iloc[-1]
            
            # 计算关键支撑阻力位
            high_20 = df['high'].tail(20).max()
            low_20 = df['low'].tail(20).min()
            
            # 检查是否突破关键位
            return current_price > high_20 * 1.01 or current_price < low_20 * 0.99
            
        except Exception as e:
            logger.warning(f"检查关键位突破失败: {str(e)}")
            return False
    
    def _check_volume_anomaly(self, df: pd.DataFrame) -> bool:
        """检查成交量异常"""
        try:
            if 'volume' not in df.columns or len(df) < 20:
                return False
            
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].tail(20).mean()
            
            # 成交量异常放大
            return current_volume > avg_volume * 2
            
        except Exception as e:
            logger.warning(f"检查成交量异常失败: {str(e)}")
            return False

    def _get_timeframe_signals(self, timeframe: str, df: pd.DataFrame, target_action: str) -> List[UnifiedSignal]:
        """获取指定时间周期的信号"""
        signals = []

        try:
            # 使用多级别分析器获取买卖点
            result = self.multi_level_analyzer.get_latest_buy_sell_signal(timeframe)

            if not result or not result.get('has_signal'):
                return signals

            signal_info = result.get('signal', {})
            action = signal_info.get('action', 'wait')

            # 只返回与目标方向一致的信号
            if action != target_action:
                return signals

            # 创建统一信号
            unified_signal = UnifiedSignal(
                action=action,
                signal_type=signal_info.get('type', '未知'),
                strength=self._convert_strength(signal_info.get('strength', 5)),
                confidence=signal_info.get('confidence', 0.5),
                entry_price=signal_info.get('price', df['close'].iloc[-1]),
                stop_loss=signal_info.get('stop_loss', 0),
                take_profits=signal_info.get('take_profits', []),
                timeframe=timeframe,
                supporting_timeframes=[],
                conflicting_timeframes=[],
                reasoning=signal_info.get('description', ''),
                risk_level=signal_info.get('risk_level', 'MEDIUM'),
                position_size=signal_info.get('position_size', 'LIGHT'),
                validity_period=self._calculate_validity_period(timeframe)
            )

            signals.append(unified_signal)

        except Exception as e:
            logger.warning(f"获取 {timeframe} 信号失败: {str(e)}")

        return signals

    def _convert_strength(self, strength_value: float) -> SignalStrength:
        """转换信号强度"""
        if strength_value >= 8:
            return SignalStrength.VERY_STRONG
        elif strength_value >= 6:
            return SignalStrength.STRONG
        elif strength_value >= 4:
            return SignalStrength.MEDIUM
        elif strength_value >= 2:
            return SignalStrength.WEAK
        else:
            return SignalStrength.INVALID

    def _calculate_validity_period(self, timeframe: str) -> int:
        """计算信号有效期(分钟)"""
        timeframe_minutes = {
            '5m': 15,    # 5分钟信号有效期15分钟
            '15m': 45,   # 15分钟信号有效期45分钟
            '30m': 90,   # 30分钟信号有效期90分钟
            '1h': 180,   # 1小时信号有效期3小时
            '4h': 720,   # 4小时信号有效期12小时
            '1d': 1440   # 日线信号有效期1天
        }
        return timeframe_minutes.get(timeframe, 60)

    def _calculate_signal_score(self, signal: UnifiedSignal, level_trends: Dict[str, TrendDirection],
                              major_direction: TrendDirection) -> float:
        """计算信号质量得分"""
        try:
            score = 0.0

            # 1. 基础强度得分 (30%)
            strength_scores = {
                SignalStrength.VERY_STRONG: 30,
                SignalStrength.STRONG: 24,
                SignalStrength.MEDIUM: 18,
                SignalStrength.WEAK: 12,
                SignalStrength.INVALID: 0
            }
            score += strength_scores.get(signal.strength, 0)

            # 2. 置信度得分 (20%)
            score += signal.confidence * 20

            # 3. 多级别一致性得分 (30%)
            consistency_score = self._calculate_consistency_score(signal, level_trends, major_direction)
            score += consistency_score * 30

            # 4. 时效性得分 (10%)
            timeframe_score = self.timeframe_weights.get(signal.timeframe, 0.1)
            score += timeframe_score * 10

            # 5. 风险收益比得分 (10%)
            risk_reward_score = self._calculate_risk_reward_score(signal)
            score += risk_reward_score * 10

            return min(100.0, max(0.0, score))

        except Exception as e:
            logger.warning(f"计算信号得分失败: {str(e)}")
            return 0.0

    def _calculate_consistency_score(self, signal: UnifiedSignal, level_trends: Dict[str, TrendDirection],
                                   major_direction: TrendDirection) -> float:
        """计算多级别一致性得分"""
        try:
            if major_direction == TrendDirection.NEUTRAL:
                return 0.3  # 大周期中性，给予较低分数

            # 检查信号方向与大周期方向是否一致
            signal_bullish = signal.action == 'buy'
            major_bullish = major_direction == TrendDirection.BULLISH

            if signal_bullish != major_bullish:
                return 0.0  # 方向不一致，得分为0

            # 计算支持的级别数量
            supporting_levels = 0
            total_levels = 0

            for timeframe, trend in level_trends.items():
                total_levels += 1
                weight = self.timeframe_weights.get(timeframe, 0.1)

                if (signal_bullish and trend == TrendDirection.BULLISH) or \
                   (not signal_bullish and trend == TrendDirection.BEARISH):
                    supporting_levels += weight

            # 一致性得分
            consistency = supporting_levels / max(1, sum(self.timeframe_weights.values()))
            return min(1.0, consistency)

        except Exception as e:
            logger.warning(f"计算一致性得分失败: {str(e)}")
            return 0.5

    def _calculate_risk_reward_score(self, signal: UnifiedSignal) -> float:
        """计算风险收益比得分"""
        try:
            if signal.stop_loss == 0 or not signal.take_profits:
                return 0.3  # 没有止损止盈，给予较低分数

            # 计算风险
            risk = abs(signal.entry_price - signal.stop_loss)
            if risk == 0:
                return 0.3

            # 计算收益(使用第一个止盈目标)
            reward = abs(signal.take_profits[0] - signal.entry_price)

            # 风险收益比
            risk_reward_ratio = reward / risk

            # 评分标准
            if risk_reward_ratio >= 3:
                return 1.0
            elif risk_reward_ratio >= 2:
                return 0.8
            elif risk_reward_ratio >= 1.5:
                return 0.6
            elif risk_reward_ratio >= 1:
                return 0.4
            else:
                return 0.2

        except Exception as e:
            logger.warning(f"计算风险收益比失败: {str(e)}")
            return 0.3

    def _validate_signal_quality(self, signal: UnifiedSignal, multi_data: Dict[str, pd.DataFrame]) -> UnifiedSignal:
        """验证信号质量"""
        try:
            # 1. 验证价格合理性
            current_price = multi_data[signal.timeframe]['close'].iloc[-1]
            price_diff = abs(signal.entry_price - current_price) / current_price

            if price_diff > 0.02:  # 价格偏差超过2%
                signal.confidence *= 0.8
                signal.reasoning += " [警告: 入场价格与当前价格偏差较大]"

            # 2. 验证止损合理性
            if signal.stop_loss > 0:
                stop_loss_ratio = abs(signal.entry_price - signal.stop_loss) / signal.entry_price
                if stop_loss_ratio > 0.05:  # 止损超过5%
                    signal.risk_level = 'HIGH'
                    signal.reasoning += " [警告: 止损幅度较大]"

            # 3. 验证止盈合理性
            if signal.take_profits:
                for i, tp in enumerate(signal.take_profits):
                    tp_ratio = abs(tp - signal.entry_price) / signal.entry_price
                    if tp_ratio > 0.20:  # 止盈超过20%
                        signal.reasoning += f" [警告: 第{i+1}个止盈目标过于激进]"

            # 4. 更新支持和冲突的时间周期
            signal.supporting_timeframes = self._find_supporting_timeframes(signal, multi_data)
            signal.conflicting_timeframes = self._find_conflicting_timeframes(signal, multi_data)

            return signal

        except Exception as e:
            logger.warning(f"验证信号质量失败: {str(e)}")
            return signal

    def _find_supporting_timeframes(self, signal: UnifiedSignal, multi_data: Dict[str, pd.DataFrame]) -> List[str]:
        """找到支持的时间周期"""
        supporting = []

        for timeframe, df in multi_data.items():
            if timeframe == signal.timeframe:
                continue

            try:
                # 简化的支持判断逻辑
                current_price = df['close'].iloc[-1]
                ma20 = df['ma20'].iloc[-1] if 'ma20' in df.columns else current_price

                if signal.action == 'buy' and current_price > ma20:
                    supporting.append(timeframe)
                elif signal.action == 'sell' and current_price < ma20:
                    supporting.append(timeframe)

            except Exception as e:
                logger.warning(f"检查 {timeframe} 支持失败: {str(e)}")

        return supporting

    def _find_conflicting_timeframes(self, signal: UnifiedSignal, multi_data: Dict[str, pd.DataFrame]) -> List[str]:
        """找到冲突的时间周期"""
        conflicting = []

        for timeframe, df in multi_data.items():
            if timeframe == signal.timeframe:
                continue

            try:
                # 简化的冲突判断逻辑
                current_price = df['close'].iloc[-1]
                ma20 = df['ma20'].iloc[-1] if 'ma20' in df.columns else current_price

                if signal.action == 'buy' and current_price < ma20 * 0.98:
                    conflicting.append(timeframe)
                elif signal.action == 'sell' and current_price > ma20 * 1.02:
                    conflicting.append(timeframe)

            except Exception as e:
                logger.warning(f"检查 {timeframe} 冲突失败: {str(e)}")

        return conflicting

    def get_major_trend_status(self) -> Dict[str, Any]:
        """获取大周期趋势状态"""
        try:
            # 获取多级别数据
            multi_data = self.multi_level_analyzer.get_multi_level_data(limit=100)
            if not multi_data:
                return {'status': 'unknown', 'confidence': 0, 'reason': '无法获取数据'}

            # 分析各级别趋势
            level_trends = self._analyze_level_trends(multi_data)

            # 确定大周期方向
            major_direction = self._determine_major_direction(level_trends)

            # 检查变盘状态
            is_changing = self._check_trend_changing(level_trends, multi_data)

            # 计算置信度
            confidence = self._calculate_trend_confidence(level_trends, major_direction)

            return {
                'status': major_direction.value,
                'confidence': confidence,
                'is_changing': is_changing,
                'level_trends': {tf: trend.value for tf, trend in level_trends.items()},
                'reason': self._generate_trend_reason(level_trends, major_direction, is_changing)
            }

        except Exception as e:
            logger.error(f"获取大周期趋势状态失败: {str(e)}")
            return {'status': 'unknown', 'confidence': 0, 'reason': f'分析失败: {str(e)}'}

    def _calculate_trend_confidence(self, level_trends: Dict[str, TrendDirection],
                                  major_direction: TrendDirection) -> float:
        """计算趋势置信度"""
        try:
            if major_direction == TrendDirection.NEUTRAL:
                return 0.3

            # 计算一致性
            consistent_levels = 0
            total_weight = 0

            for timeframe, trend in level_trends.items():
                weight = self.timeframe_weights.get(timeframe, 0.1)
                total_weight += weight

                if trend == major_direction:
                    consistent_levels += weight

            confidence = consistent_levels / max(total_weight, 1)
            return min(1.0, max(0.0, confidence))

        except Exception as e:
            logger.warning(f"计算趋势置信度失败: {str(e)}")
            return 0.5

    def _generate_trend_reason(self, level_trends: Dict[str, TrendDirection],
                             major_direction: TrendDirection, is_changing: bool) -> str:
        """生成趋势判断理由"""
        try:
            reasons = []

            if is_changing:
                reasons.append("检测到变盘信号")

            # 统计各方向的级别
            bullish_levels = [tf for tf, trend in level_trends.items() if trend == TrendDirection.BULLISH]
            bearish_levels = [tf for tf, trend in level_trends.items() if trend == TrendDirection.BEARISH]
            neutral_levels = [tf for tf, trend in level_trends.items() if trend == TrendDirection.NEUTRAL]

            if bullish_levels:
                reasons.append(f"看多级别: {', '.join(bullish_levels)}")
            if bearish_levels:
                reasons.append(f"看空级别: {', '.join(bearish_levels)}")
            if neutral_levels:
                reasons.append(f"中性级别: {', '.join(neutral_levels)}")

            reasons.append(f"综合判断: {major_direction.value}")

            return "; ".join(reasons)

        except Exception as e:
            logger.warning(f"生成趋势理由失败: {str(e)}")
            return "分析失败"

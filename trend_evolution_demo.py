#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
走势推演引擎演示脚本
展示如何使用走势推演引擎进行线上数据分析和图表生成
"""

import sys
import os
from loguru import logger
from trend_evolution_engine import TrendEvolutionEngine

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 走势推演引擎演示")
    print("=" * 80)
    
    # 配置日志
    logger.add("logs/trend_evolution_{time}.log", rotation="100 MB")
    
    try:
        # 1. 创建走势推演引擎
        print("\n📊 初始化走势推演引擎...")
        engine = TrendEvolutionEngine(
            symbol="BTC/USDT",
            exchange="gate",
            chart_dir="charts/trend_evolution"
        )
        print("✅ 引擎初始化完成")
        
        # 2. 执行走势推演分析
        print("\n📡 开始获取线上数据并执行走势推演...")
        result = engine.evolve_trend_live(
            timeframes=['5m', '15m', '30m', '1h'],  # 多级别分析
            limit=200,                              # 获取200根K线
            generate_chart=True                     # 生成分析图表
        )
        
        # 3. 显示分析结果
        print("\n" + "=" * 60)
        print("📈 走势推演分析结果")
        print("=" * 60)
        
        # 主要情景
        primary = result.primary_scenario
        print(f"🎯 主要情景: {primary.trend_type.value.upper()}")
        print(f"📊 概率: {primary.probability:.1%}")
        print(f"🔍 置信度: {primary.confidence_level}")
        print(f"⏰ 时间预期: {primary.time_horizon}分钟" if primary.time_horizon else "⏰ 时间预期: 未知")
        print(f"📝 描述: {primary.description}")
        
        # 支持和风险因素
        if primary.supporting_factors:
            print(f"\n✅ 支持因素:")
            for factor in primary.supporting_factors:
                print(f"   • {factor}")
                
        if primary.risk_factors:
            print(f"\n⚠️ 风险因素:")
            for factor in primary.risk_factors:
                print(f"   • {factor}")
        
        # 操作建议
        print(f"\n💡 推荐操作: {result.recommended_action.upper()}")
        print(f"💰 仓位建议: {result.position_size.upper()}")
        
        # 价格目标
        if result.profit_target:
            print(f"🎯 止盈目标: ${result.profit_target:,.2f}")
        if result.stop_loss:
            print(f"🛡️ 止损位: ${result.stop_loss:,.2f}")
            
        # 图表信息
        if result.chart_path:
            print(f"\n📈 分析图表已生成: {result.chart_path}")
            print("💡 提示: 可以打开图表文件查看详细的技术分析")
        
        # 备选情景
        if result.alternative_scenarios:
            print(f"\n🔄 备选情景:")
            for i, scenario in enumerate(result.alternative_scenarios, 1):
                print(f"   {i}. {scenario.trend_type.value.upper()} "
                      f"(概率: {scenario.probability:.1%}, "
                      f"置信度: {scenario.confidence_level})")
        
        # 详细判断理由
        if hasattr(primary, 'detailed_reasoning') and primary.detailed_reasoning:
            print(f"\n🔍 详细判断理由:")
            print(primary.detailed_reasoning)

        # 风险评估
        risk = result.risk_assessment
        print(f"\n⚠️ 风险评估:")
        print(f"   • 整体风险等级: {risk.get('risk_level', 'UNKNOWN').upper()}")
        max_drawdown_display = risk.get('max_drawdown_percent', f"{risk.get('max_drawdown_estimate', 0.05):.1%}")
        print(f"   • 最大回撤预期: {max_drawdown_display}")

        # 显示主要风险
        if 'primary_risks' in risk and risk['primary_risks']:
            print(f"   • 主要风险: {', '.join(risk['primary_risks'])}")
        
        print(f"\n📅 分析时间: {result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 数据来源: {result.data_source.upper()}")
        
        print("\n" + "=" * 60)
        print("✅ 走势推演分析完成")
        print("=" * 60)
        
        # 4. 提供后续操作建议
        print("\n💡 后续建议:")
        print("1. 定期重新运行分析以获取最新市场状态")
        print("2. 结合其他技术指标进行综合判断")
        print("3. 严格执行风险管理策略")
        print("4. 关注市场突发事件对分析结果的影响")
        
        return result
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        return None
        
    except Exception as e:
        logger.error(f"走势推演演示失败: {str(e)}")
        print(f"\n❌ 演示失败: {str(e)}")
        print("💡 请检查网络连接和数据源配置")
        return None

def quick_analysis(symbol: str = "BTC/USDT", exchange: str = "gate"):
    """快速分析函数"""
    try:
        print(f"🔍 快速分析 {symbol}...")
        
        engine = TrendEvolutionEngine(symbol=symbol, exchange=exchange)
        result = engine.evolve_trend_live(generate_chart=False)
        
        primary = result.primary_scenario
        print(f"📊 {symbol} 主要情景: {primary.trend_type.value.upper()} "
              f"(概率: {primary.probability:.1%})")
        print(f"💡 建议: {result.recommended_action.upper()}")
        
        return result
        
    except Exception as e:
        print(f"❌ 快速分析失败: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            # 快速分析模式
            symbol = sys.argv[2] if len(sys.argv) > 2 else "BTC/USDT"
            exchange = sys.argv[3] if len(sys.argv) > 3 else "gate"
            quick_analysis(symbol, exchange)
        else:
            print("用法: python trend_evolution_demo.py [quick] [symbol] [exchange]")
    else:
        # 完整演示模式
        main()

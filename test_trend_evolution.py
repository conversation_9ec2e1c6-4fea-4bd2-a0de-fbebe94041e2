#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
走势推演引擎集成测试
演示如何使用走势推演引擎进行最大概率走势类型判断
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from trend_evolution_engine import TrendEvolutionEngine, TrendType

def create_realistic_test_data():
    """创建更真实的测试数据"""
    print("📊 创建真实测试数据...")
    
    # 创建时间序列
    dates = pd.date_range(start='2024-01-01', periods=200, freq='15min')
    
    # 模拟比特币价格走势
    np.random.seed(42)
    base_price = 50000
    prices = []
    
    # 模拟一个完整的上涨-盘整-下跌周期
    for i in range(200):
        if i < 60:  # 上涨阶段
            trend = 0.001
            volatility = 0.008
        elif i < 140:  # 盘整阶段
            trend = 0.0002
            volatility = 0.005
        else:  # 下跌阶段
            trend = -0.0008
            volatility = 0.01
        
        # 添加随机波动
        change = trend + np.random.normal(0, volatility)
        if i > 0:
            base_price = prices[-1]['close'] * (1 + change)
        
        # 生成OHLC
        high = base_price * (1 + abs(np.random.normal(0, 0.003)))
        low = base_price * (1 - abs(np.random.normal(0, 0.003)))
        open_price = base_price
        close_price = base_price
        
        # 确保OHLC关系正确
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        prices.append({
            'timestamp': dates[i],
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': np.random.randint(5000, 20000)
        })
    
    df = pd.DataFrame(prices)
    print(f"✅ 生成了{len(df)}根K线数据，价格范围: {df['low'].min():.0f} - {df['high'].max():.0f}")
    return df

def analyze_with_chan_theory(df):
    """使用缠论分析获取结构数据"""
    print("🔍 模拟缠论分析...")

    # 直接返回模拟的结构数据，避免依赖问题
    current_price = float(df['close'].iloc[-1])
    high_price = df['high'].tail(50).max()
    low_price = df['low'].tail(50).min()

    structure_data = {
        'bi_list': [
            (len(df)-40, len(df)-30, low_price*1.01, high_price*0.98, 'up'),
            (len(df)-30, len(df)-20, high_price*0.98, low_price*1.02, 'down'),
            (len(df)-20, len(df)-10, low_price*1.02, current_price*1.01, 'up'),
            (len(df)-10, len(df)-5, current_price*1.01, current_price*0.99, 'down'),
            (len(df)-5, len(df)-1, current_price*0.99, current_price, 'up')
        ],
        'xd_list': [
            (len(df)-40, len(df)-10, low_price*1.01, current_price*1.01, 'up'),
            (len(df)-10, len(df)-1, current_price*1.01, current_price, 'down')
        ],
        'pivot_zones': [
            {
                'high': (high_price + current_price) / 2,
                'low': (low_price + current_price) / 2,
                'level': '15m',
                'oscillation_count': 4,
                'duration': 60
            }
        ],
        'fx_list': []
    }

    print(f"✅ 模拟缠论分析完成:")
    print(f"   - 分型数量: {len(structure_data['fx_list'])}")
    print(f"   - 笔数量: {len(structure_data['bi_list'])}")
    print(f"   - 线段数量: {len(structure_data['xd_list'])}")
    print(f"   - 中枢数量: {len(structure_data['pivot_zones'])}")

    return structure_data

def create_multi_level_data(base_df):
    """创建多级别数据"""
    print("📈 创建多级别数据...")
    
    # 简化处理：使用相同数据模拟不同级别
    # 实际应用中应该从不同时间周期获取真实数据
    market_data = {}
    
    timeframes = ['1m', '5m', '15m', '30m', '1h']
    for tf in timeframes:
        # 为不同级别添加一些变化
        df_copy = base_df.copy()
        
        # 模拟不同级别的价格差异
        if tf == '1m':
            df_copy = df_copy.tail(50)  # 1分钟级别数据较少
        elif tf == '5m':
            df_copy = df_copy.tail(100)
        elif tf == '30m':
            # 30分钟级别，价格波动更大
            df_copy['high'] = df_copy['high'] * 1.001
            df_copy['low'] = df_copy['low'] * 0.999
        elif tf == '1h':
            # 1小时级别，趋势更明显
            df_copy['close'] = df_copy['close'].rolling(window=5).mean().fillna(df_copy['close'])
        
        market_data[tf] = df_copy
    
    print(f"✅ 创建了{len(timeframes)}个级别的数据")
    return market_data

def test_comprehensive_evolution():
    """综合测试走势推演功能"""
    print("\n" + "="*80)
    print("🚀 开始综合走势推演测试")
    print("="*80)
    
    try:
        # 1. 创建测试数据
        df = create_realistic_test_data()
        
        # 2. 执行缠论分析
        structure_data = analyze_with_chan_theory(df)
        
        # 3. 创建多级别数据
        market_data = create_multi_level_data(df)
        
        # 4. 创建走势推演引擎
        print("\n🔧 初始化走势推演引擎...")
        engine = TrendEvolutionEngine()
        
        # 5. 执行走势推演
        print("\n🎯 执行走势推演分析...")
        result = engine.evolve_trend(market_data, structure_data, '15m')
        
        # 6. 详细分析结果
        print_detailed_result(result, df)
        
        # 7. 验证结果合理性
        validate_result(result, df)
        
        return result
        
    except Exception as e:
        print(f"❌ 综合测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def print_detailed_result(result, df):
    """打印详细的推演结果"""
    print("\n" + "="*80)
    print("📊 详细走势推演结果")
    print("="*80)
    
    current_price = float(df['close'].iloc[-1])
    
    # 主要情景
    primary = result.primary_scenario
    print(f"\n🎯 主要情景 (概率: {primary.probability:.2%})")
    print(f"   走势类型: {primary.trend_type.value}")
    print(f"   置信度: {primary.confidence_level}")
    print(f"   描述: {primary.description}")
    
    if primary.target_price:
        change_pct = (primary.target_price - current_price) / current_price * 100
        print(f"   目标价格: {primary.target_price:.2f} ({change_pct:+.2f}%)")
    
    if primary.time_horizon:
        print(f"   时间预期: {primary.time_horizon}分钟")
    
    print(f"   支持因素: {', '.join(primary.supporting_factors)}")
    if primary.risk_factors:
        print(f"   风险因素: {', '.join(primary.risk_factors)}")
    
    # 备选情景
    if result.alternative_scenarios:
        print(f"\n🔄 备选情景:")
        for i, scenario in enumerate(result.alternative_scenarios, 1):
            print(f"   {i}. {scenario.trend_type.value} (概率: {scenario.probability:.2%}) - {scenario.description}")
    
    # 操作建议
    print(f"\n💡 操作建议:")
    print(f"   推荐操作: {result.recommended_action}")
    print(f"   仓位建议: {result.position_size}")
    
    if result.profit_target:
        profit_pct = (result.profit_target - current_price) / current_price * 100
        print(f"   止盈目标: {result.profit_target:.2f} ({profit_pct:+.2f}%)")
    
    if result.stop_loss:
        loss_pct = (result.stop_loss - current_price) / current_price * 100
        print(f"   止损位: {result.stop_loss:.2f} ({loss_pct:+.2f}%)")
    
    # 风险评估
    print(f"\n⚠️ 风险评估:")
    risk = result.risk_assessment
    if risk.get('primary_risks'):
        print(f"   主要风险: {', '.join(risk['primary_risks'][:3])}")
    
    if risk.get('max_drawdown_estimate'):
        print(f"   最大回撤估计: {risk['max_drawdown_estimate']:.2%}")
    
    print(f"\n📅 分析时间: {result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

def validate_result(result, df):
    """验证结果的合理性"""
    print(f"\n🔍 结果验证:")
    
    # 检查概率总和
    total_prob = result.primary_scenario.probability
    for scenario in result.alternative_scenarios:
        total_prob += scenario.probability
    
    print(f"   概率总和: {total_prob:.2%} {'✅' if 0.95 <= total_prob <= 1.05 else '⚠️'}")
    
    # 检查主要情景概率
    main_prob = result.primary_scenario.probability
    print(f"   主要情景概率: {main_prob:.2%} {'✅' if main_prob >= 0.3 else '⚠️'}")
    
    # 检查操作建议合理性
    action = result.recommended_action
    trend_type = result.primary_scenario.trend_type
    
    action_reasonable = (
        (action == 'buy' and trend_type == TrendType.UP) or
        (action == 'sell' and trend_type == TrendType.DOWN) or
        (action == 'wait' and trend_type == TrendType.CONSOLIDATION) or
        (action == 'wait' and main_prob < 0.6)
    )
    
    print(f"   操作建议合理性: {'✅' if action_reasonable else '⚠️'}")
    
    # 检查止盈止损合理性
    current_price = float(df['close'].iloc[-1])
    if result.profit_target and result.stop_loss:
        profit_ratio = abs(result.profit_target - current_price) / current_price
        loss_ratio = abs(result.stop_loss - current_price) / current_price
        risk_reward = profit_ratio / loss_ratio if loss_ratio > 0 else 0
        
        print(f"   风险收益比: {risk_reward:.2f} {'✅' if risk_reward >= 1.5 else '⚠️'}")

if __name__ == "__main__":
    # 运行综合测试
    result = test_comprehensive_evolution()
    
    if result:
        print(f"\n🎉 走势推演引擎测试成功完成！")
        print(f"主要结论: {result.primary_scenario.trend_type.value}走势，概率{result.primary_scenario.probability:.1%}")
    else:
        print(f"\n❌ 走势推演引擎测试失败")

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版走势推演测试
测试数据获取和基本功能
"""

import ccxt
from loguru import logger
from datetime import datetime
import os

def test_data_fetcher():
    """测试数据获取功能"""
    print("🚀 测试数据获取功能...")
    
    try:
        # 初始化交易所
        exchange = ccxt.gate({
            'timeout': 30000,
            'enableRateLimit': True,
        })
        
        # 加载市场
        exchange.load_markets()
        print("✅ 交易所连接成功")
        
        # 获取BTC/USDT的K线数据
        symbol = "BTC/USDT"
        timeframe = "15m"
        limit = 50
        
        print(f"📊 获取 {symbol} {timeframe} K线数据...")
        
        ohlcv = exchange.fetch_ohlcv(
            symbol=symbol,
            timeframe=timeframe,
            limit=limit
        )
        
        if ohlcv and len(ohlcv) > 0:
            print(f"✅ 成功获取 {len(ohlcv)} 条K线数据")
            
            # 显示最新几条数据
            print("\n📈 最新K线数据:")
            print("时间戳\t\t开盘\t最高\t最低\t收盘\t成交量")
            print("-" * 80)
            
            for i, candle in enumerate(ohlcv[-5:]):  # 显示最新5条
                timestamp = datetime.fromtimestamp(candle[0] / 1000)
                print(f"{timestamp.strftime('%m-%d %H:%M')}\t"
                      f"{candle[1]:.2f}\t{candle[2]:.2f}\t{candle[3]:.2f}\t"
                      f"{candle[4]:.2f}\t{candle[5]:.0f}")
            
            # 计算基本统计信息
            prices = [candle[4] for candle in ohlcv]  # 收盘价
            current_price = prices[-1]
            price_change = prices[-1] - prices[0]
            price_change_pct = (price_change / prices[0]) * 100
            
            print(f"\n📊 基本统计:")
            print(f"当前价格: ${current_price:,.2f}")
            print(f"价格变化: ${price_change:+,.2f} ({price_change_pct:+.2f}%)")
            print(f"最高价: ${max([c[2] for c in ohlcv]):,.2f}")
            print(f"最低价: ${min([c[3] for c in ohlcv]):,.2f}")
            
            return True
            
        else:
            print("❌ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取失败: {str(e)}")
        return False

def test_chart_directory():
    """测试图表目录创建"""
    print("\n🗂️ 测试图表目录创建...")
    
    try:
        chart_dir = "charts/trend_evolution"
        os.makedirs(chart_dir, exist_ok=True)
        
        if os.path.exists(chart_dir):
            print(f"✅ 图表目录创建成功: {chart_dir}")
            return True
        else:
            print(f"❌ 图表目录创建失败: {chart_dir}")
            return False
            
    except Exception as e:
        print(f"❌ 目录创建失败: {str(e)}")
        return False

def simple_trend_analysis(ohlcv_data):
    """简单的趋势分析"""
    print("\n📈 执行简单趋势分析...")
    
    try:
        if not ohlcv_data or len(ohlcv_data) < 10:
            print("❌ 数据不足，无法进行分析")
            return None
        
        # 提取收盘价
        prices = [candle[4] for candle in ohlcv_data]
        
        # 计算移动平均线
        ma_5 = sum(prices[-5:]) / 5
        ma_10 = sum(prices[-10:]) / 10
        ma_20 = sum(prices[-20:]) / 20 if len(prices) >= 20 else sum(prices) / len(prices)
        
        current_price = prices[-1]
        
        # 简单趋势判断
        trend = "横盘"
        if current_price > ma_5 > ma_10:
            trend = "上涨"
        elif current_price < ma_5 < ma_10:
            trend = "下跌"
        
        # 计算价格动量
        momentum = (prices[-1] - prices[-5]) / prices[-5] * 100 if len(prices) >= 5 else 0
        
        # 分析结果
        analysis = {
            'current_price': current_price,
            'ma_5': ma_5,
            'ma_10': ma_10,
            'ma_20': ma_20,
            'trend': trend,
            'momentum': momentum,
            'analysis_time': datetime.now()
        }
        
        print("📊 分析结果:")
        print(f"当前价格: ${current_price:,.2f}")
        print(f"MA5: ${ma_5:,.2f}")
        print(f"MA10: ${ma_10:,.2f}")
        print(f"MA20: ${ma_20:,.2f}")
        print(f"趋势判断: {trend}")
        print(f"价格动量: {momentum:+.2f}%")
        
        # 简单的操作建议
        if trend == "上涨" and momentum > 1:
            recommendation = "考虑买入"
        elif trend == "下跌" and momentum < -1:
            recommendation = "考虑卖出"
        else:
            recommendation = "观望等待"
        
        print(f"操作建议: {recommendation}")
        
        return analysis
        
    except Exception as e:
        print(f"❌ 趋势分析失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 走势推演引擎 - 简化测试")
    print("=" * 60)
    
    # 配置日志
    logger.add("logs/simple_test_{time}.log", rotation="10 MB")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 数据获取
    if test_data_fetcher():
        success_count += 1
        
        # 如果数据获取成功，进行趋势分析
        try:
            exchange = ccxt.gate({'timeout': 30000, 'enableRateLimit': True})
            exchange.load_markets()
            ohlcv = exchange.fetch_ohlcv("BTC/USDT", "15m", 50)
            
            if simple_trend_analysis(ohlcv):
                print("✅ 趋势分析测试通过")
            else:
                print("❌ 趋势分析测试失败")
                
        except Exception as e:
            print(f"❌ 趋势分析测试异常: {str(e)}")
    
    # 测试2: 图表目录
    if test_chart_directory():
        success_count += 1
    
    # 测试3: 基本功能验证
    try:
        print("\n🔧 测试基本功能...")
        
        # 测试时间戳转换
        timestamp = datetime.now()
        formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        print(f"时间格式化: {formatted_time}")
        
        # 测试数学计算
        test_prices = [100, 102, 101, 103, 105]
        avg_price = sum(test_prices) / len(test_prices)
        print(f"平均价格计算: {avg_price:.2f}")
        
        print("✅ 基本功能测试通过")
        success_count += 1
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！走势推演引擎基础功能正常")
        print("💡 下一步可以安装完整依赖并测试完整功能")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()

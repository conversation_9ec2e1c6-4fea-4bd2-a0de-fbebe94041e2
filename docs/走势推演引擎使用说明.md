# 走势推演引擎使用说明

## 概述

走势推演引擎是基于缠论理论和走势推演文章理解开发的核心功能模块，用于找出最大概率的走势类型。该引擎综合运用结构思维、多空博弈、联立思维、时空思维等多个维度进行分析，为交易决策提供科学依据。

## 核心功能

### 1. 结构思维分析
- **笔的状态分析**：分析当前笔的方向、强度、完成状态和延续概率
- **线段状态分析**：评估线段的完整性、强度和突破概率
- **中枢状态分析**：判断中枢的级别、强度、扩展和突破可能性
- **结构完整性评分**：量化当前结构的完整程度（0-10分）

### 2. 多空博弈分析
- **力量对比判断**：通过价格行为和成交量分析多空优势方
- **动量变化分析**：识别市场动量的增强、减弱或稳定状态
- **关键位置争夺**：分析在重要支撑阻力位的多空表现
- **反转信号识别**：及时发现可能的趋势反转迹象

### 3. 多级别联立分析
- **趋势一致性检查**：验证不同时间级别的趋势方向是否一致
- **共振强度计算**：量化多级别共振的强度（0-10分）
- **主导级别确定**：识别当前起主导作用的时间级别
- **冲突级别处理**：处理不同级别间的趋势矛盾

### 4. 时空思维分析
- **时间周期分析**：识别当前所处的时间周期阶段
- **空间测算**：计算支撑阻力位和目标价位
- **时空共振检查**：判断时间和空间因素是否形成共振
- **关键时间点识别**：预测重要的时间转折点

## 使用方法

### 基本用法

```python
from trend_evolution_engine import TrendEvolutionEngine

# 1. 创建推演引擎
engine = TrendEvolutionEngine()

# 2. 准备数据
market_data = {
    '1m': df_1m,    # 1分钟K线数据
    '5m': df_5m,    # 5分钟K线数据
    '15m': df_15m,  # 15分钟K线数据
    '30m': df_30m,  # 30分钟K线数据
    '1h': df_1h     # 1小时K线数据
}

structure_data = {
    'bi_list': [...],      # 笔列表
    'xd_list': [...],      # 线段列表
    'pivot_zones': [...],  # 中枢列表
    'fx_list': [...]       # 分型列表
}

# 3. 执行推演
result = engine.evolve_trend(market_data, structure_data, '15m')

# 4. 获取结果
print(f"主要走势: {result.primary_scenario.trend_type.value}")
print(f"概率: {result.primary_scenario.probability:.2%}")
print(f"推荐操作: {result.recommended_action}")
```

### 数据格式要求

#### K线数据格式
```python
df = pd.DataFrame({
    'timestamp': [...],  # 时间戳
    'open': [...],       # 开盘价
    'high': [...],       # 最高价
    'low': [...],        # 最低价
    'close': [...],      # 收盘价
    'volume': [...]      # 成交量
})
```

#### 结构数据格式
```python
structure_data = {
    'bi_list': [
        (start_idx, end_idx, start_price, end_price, direction),
        # 例如: (0, 10, 49000, 51000, 'up')
    ],
    'xd_list': [
        (start_idx, end_idx, start_price, end_price, direction),
        # 例如: (0, 30, 49000, 52000, 'up')
    ],
    'pivot_zones': [
        {
            'high': 51500,
            'low': 49500,
            'level': '15m',
            'oscillation_count': 3,
            'duration': 60
        }
    ]
}
```

## 输出结果说明

### 推演结果结构
```python
class EvolutionResult:
    primary_scenario: TrendScenario      # 主要情景
    alternative_scenarios: List[TrendScenario]  # 备选情景
    risk_assessment: Dict[str, Any]      # 风险评估
    recommended_action: str              # 推荐操作
    profit_target: Optional[float]       # 止盈目标
    stop_loss: Optional[float]          # 止损位
    position_size: str                   # 仓位建议
    analysis_timestamp: datetime         # 分析时间
```

### 走势情景结构
```python
class TrendScenario:
    trend_type: TrendType               # 走势类型 (up/down/consolidation)
    probability: float                  # 概率 (0-1)
    target_price: Optional[float]       # 目标价格
    time_horizon: Optional[int]         # 时间预期(分钟)
    confidence_level: str               # 置信度 (high/medium/low)
    supporting_factors: List[str]       # 支持因素
    risk_factors: List[str]            # 风险因素
    description: str                    # 情景描述
```

## 配置参数

### 权重配置
```python
weight_config = {
    'structure_analysis': 0.35,    # 结构分析权重
    'multi_space_game': 0.25,      # 多空博弈权重
    'multi_level_joint': 0.20,     # 多级别联立权重
    'time_space_analysis': 0.15,   # 时空分析权重
    'technical_indicators': 0.05   # 技术指标权重
}
```

### 概率阈值
```python
probability_thresholds = {
    'high_confidence': 0.75,    # 高置信度阈值
    'medium_confidence': 0.60,  # 中等置信度阈值
    'low_confidence': 0.45      # 低置信度阈值
}
```

## 实际应用示例

### 示例1：多级别共振上涨
```python
# 当多个级别都显示上涨趋势时
result = engine.evolve_trend(market_data, structure_data)

# 可能的输出：
# 主要情景: up (概率: 78%)
# 置信度: high
# 支持因素: ['多级别共振向上', '多方占优', '中枢向上突破']
# 推荐操作: buy
# 仓位建议: heavy
```

### 示例2：结构不明确时
```python
# 当结构信号不明确时
result = engine.evolve_trend(market_data, structure_data)

# 可能的输出：
# 主要情景: consolidation (概率: 45%)
# 置信度: low
# 风险因素: ['结构不完整', '多空平衡']
# 推荐操作: wait
# 仓位建议: light
```

## 注意事项

1. **数据质量**：确保输入的K线数据和结构数据准确完整
2. **时间同步**：多级别数据的时间要保持同步
3. **参数调整**：根据不同市场特性调整权重配置
4. **风险控制**：始终关注风险评估结果，严格执行止损
5. **动态更新**：随着新数据的到来及时更新推演结果

## 性能优化建议

1. **数据缓存**：对重复计算的结果进行缓存
2. **并行计算**：多级别分析可以并行执行
3. **增量更新**：只对新增的K线进行增量分析
4. **内存管理**：及时清理不需要的历史数据

## 扩展功能

### 自定义评估函数
```python
def custom_structure_evaluator(scenario, structure_analysis):
    # 自定义结构评估逻辑
    return score

engine.set_custom_evaluator('structure', custom_structure_evaluator)
```

### 添加新的分析维度
```python
def custom_analysis(market_data, structure_data):
    # 自定义分析逻辑
    return analysis_result

engine.add_analysis_dimension('custom', custom_analysis, weight=0.1)
```

## 总结

走势推演引擎通过多维度综合分析，能够相对客观地评估各种走势类型的概率，为交易决策提供科学依据。关键是要理解其分析逻辑，合理设置参数，并结合实际市场情况灵活运用。

记住走势推演的核心理念：
- **坚持大概率**：重点关注概率最高的走势类型
- **未胜先言败**：为预期外的走势做好准备
- **求缺思维**：根据认知水平获取相应利润
- **完全分类**：对所有可能的走势都要有应对方案

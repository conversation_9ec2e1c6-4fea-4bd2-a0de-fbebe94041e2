#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
对比回测验证
同时运行原始和优化版本的回测，对比效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_backtest import EnhancedBacktest
from datetime import datetime, timedelta
from loguru import logger
import time

def run_comparison_backtest():
    """运行对比回测"""
    print("=" * 80)
    print("🔄 对比回测验证")
    print("=" * 80)
    print("📋 目标: 验证优化信号生成器的实际效果")
    print("🔬 方法: 同时运行原始版本和优化版本，对比结果")
    
    try:
        # 设置回测参数
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        print(f"\n📅 回测期间: {start_date_str} 到 {end_date_str}")
        print(f"⏰ 时间周期: 15分钟")
        print(f"💰 交易品种: BTC/USDT")
        
        # 1. 运行原始版本回测
        print("\n" + "="*50)
        print("1️⃣ 运行原始版本回测")
        print("="*50)
        
        original_backtest = EnhancedBacktest(
            symbol="BTC/USDT", 
            exchange="gate", 
            use_optimized_signals=False
        )
        
        print("🔄 开始原始版本回测...")
        start_time = time.time()
        
        original_report = original_backtest.run_enhanced_backtest(
            start_date=start_date_str,
            end_date=end_date_str,
            primary_timeframe='15m'
        )
        
        original_time = time.time() - start_time
        print(f"✅ 原始版本回测完成，耗时: {original_time:.1f}秒")
        
        # 2. 运行优化版本回测
        print("\n" + "="*50)
        print("2️⃣ 运行优化版本回测")
        print("="*50)
        
        optimized_backtest = EnhancedBacktest(
            symbol="BTC/USDT", 
            exchange="gate", 
            use_optimized_signals=True
        )
        
        print("🔄 开始优化版本回测...")
        start_time = time.time()
        
        optimized_report = optimized_backtest.run_enhanced_backtest(
            start_date=start_date_str,
            end_date=end_date_str,
            primary_timeframe='15m'
        )
        
        optimized_time = time.time() - start_time
        print(f"✅ 优化版本回测完成，耗时: {optimized_time:.1f}秒")
        
        # 3. 详细对比分析
        print("\n" + "="*50)
        print("3️⃣ 详细对比分析")
        print("="*50)
        
        compare_backtest_results(original_report, optimized_report)
        
        # 4. 生成改进报告
        print("\n" + "="*50)
        print("4️⃣ 改进效果报告")
        print("="*50)
        
        generate_improvement_report(original_report, optimized_report)
        
        return original_report, optimized_report
        
    except Exception as e:
        logger.error(f"对比回测失败: {str(e)}")
        print(f"❌ 对比回测失败: {str(e)}")
        return None, None

def compare_backtest_results(original_report, optimized_report):
    """对比回测结果"""
    print("\n📊 核心指标对比:")
    
    # 创建对比表格
    metrics = [
        ("总信号数", original_report.total_signals, optimized_report.total_signals),
        ("买入信号", original_report.buy_signals, optimized_report.buy_signals),
        ("卖出信号", original_report.sell_signals, optimized_report.sell_signals),
        ("等待信号", original_report.wait_signals, optimized_report.wait_signals),
        ("总交易数", original_report.total_trades, optimized_report.total_trades),
        ("成功交易", original_report.successful_trades, optimized_report.successful_trades),
        ("失败交易", original_report.failed_trades, optimized_report.failed_trades),
        ("胜率", f"{original_report.win_rate:.1%}", f"{optimized_report.win_rate:.1%}"),
        ("总收益率", f"{original_report.total_profit_loss_pct:.2f}%", f"{optimized_report.total_profit_loss_pct:.2f}%"),
        ("最大回撤", f"{original_report.max_drawdown:.2f}%", f"{optimized_report.max_drawdown:.2f}%"),
        ("夏普比率", f"{original_report.sharpe_ratio:.2f}", f"{optimized_report.sharpe_ratio:.2f}"),
        ("平均持仓时间", f"{original_report.avg_holding_time:.1f}分钟", f"{optimized_report.avg_holding_time:.1f}分钟")
    ]
    
    print(f"{'指标':<12} {'原始版本':<15} {'优化版本':<15} {'改进':<10}")
    print("-" * 60)
    
    for metric_name, original_val, optimized_val in metrics:
        # 计算改进
        if metric_name in ["胜率", "总收益率", "夏普比率"]:
            try:
                orig_num = float(str(original_val).replace('%', ''))
                opt_num = float(str(optimized_val).replace('%', ''))
                improvement = opt_num - orig_num
                if improvement > 0:
                    improvement_str = f"+{improvement:.1f}"
                    improvement_emoji = "📈"
                elif improvement < 0:
                    improvement_str = f"{improvement:.1f}"
                    improvement_emoji = "📉"
                else:
                    improvement_str = "0.0"
                    improvement_emoji = "➡️"
                improvement_display = f"{improvement_emoji}{improvement_str}"
            except:
                improvement_display = "N/A"
        elif metric_name == "最大回撤":
            try:
                orig_num = float(str(original_val).replace('%', ''))
                opt_num = float(str(optimized_val).replace('%', ''))
                improvement = orig_num - opt_num  # 回撤减少是好事
                if improvement > 0:
                    improvement_str = f"-{improvement:.1f}"
                    improvement_emoji = "📈"
                elif improvement < 0:
                    improvement_str = f"+{abs(improvement):.1f}"
                    improvement_emoji = "📉"
                else:
                    improvement_str = "0.0"
                    improvement_emoji = "➡️"
                improvement_display = f"{improvement_emoji}{improvement_str}"
            except:
                improvement_display = "N/A"
        else:
            try:
                if isinstance(original_val, (int, float)) and isinstance(optimized_val, (int, float)):
                    improvement = optimized_val - original_val
                    if improvement > 0:
                        improvement_display = f"📈+{improvement}"
                    elif improvement < 0:
                        improvement_display = f"📉{improvement}"
                    else:
                        improvement_display = "➡️0"
                else:
                    improvement_display = "N/A"
            except:
                improvement_display = "N/A"
        
        print(f"{metric_name:<12} {str(original_val):<15} {str(optimized_val):<15} {improvement_display:<10}")

def analyze_signal_quality_improvement(original_report, optimized_report):
    """分析信号质量改进"""
    print("\n🔍 信号质量改进分析:")
    
    # 信号频率分析
    original_frequency = original_report.total_signals / ((original_report.end_time - original_report.start_time).total_seconds() / (15 * 60))
    optimized_frequency = optimized_report.total_signals / ((optimized_report.end_time - optimized_report.start_time).total_seconds() / (15 * 60))
    
    print(f"\n📡 信号频率对比:")
    print(f"   原始版本: {original_frequency:.1%}")
    print(f"   优化版本: {optimized_frequency:.1%}")
    print(f"   频率降低: {(original_frequency - optimized_frequency)/original_frequency*100:.1f}%")
    
    # 信号转化率分析
    original_conversion = original_report.total_trades / original_report.total_signals if original_report.total_signals > 0 else 0
    optimized_conversion = optimized_report.total_trades / optimized_report.total_signals if optimized_report.total_signals > 0 else 0
    
    print(f"\n💼 信号转化率对比:")
    print(f"   原始版本: {original_conversion:.1%}")
    print(f"   优化版本: {optimized_conversion:.1%}")
    print(f"   转化率变化: {(optimized_conversion - original_conversion)*100:.1f}个百分点")

def analyze_trade_quality_improvement(original_report, optimized_report):
    """分析交易质量改进"""
    print("\n💎 交易质量改进分析:")
    
    # 买卖信号成功率对比
    def calculate_action_success_rate(trades, action):
        action_trades = [t for t in trades if t.signal.action == action]
        if not action_trades:
            return 0, 0, 0
        successful = len([t for t in action_trades if t.success])
        return len(action_trades), successful, successful/len(action_trades)*100
    
    # 原始版本
    orig_buy_total, orig_buy_success, orig_buy_rate = calculate_action_success_rate(original_report.trade_results, 'buy')
    orig_sell_total, orig_sell_success, orig_sell_rate = calculate_action_success_rate(original_report.trade_results, 'sell')
    
    # 优化版本
    opt_buy_total, opt_buy_success, opt_buy_rate = calculate_action_success_rate(optimized_report.trade_results, 'buy')
    opt_sell_total, opt_sell_success, opt_sell_rate = calculate_action_success_rate(optimized_report.trade_results, 'sell')
    
    print(f"\n📈 买入信号成功率:")
    print(f"   原始版本: {orig_buy_rate:.1f}% ({orig_buy_success}/{orig_buy_total})")
    print(f"   优化版本: {opt_buy_rate:.1f}% ({opt_buy_success}/{opt_buy_total})")
    print(f"   改进: {opt_buy_rate - orig_buy_rate:+.1f}个百分点")
    
    print(f"\n📉 卖出信号成功率:")
    print(f"   原始版本: {orig_sell_rate:.1f}% ({orig_sell_success}/{orig_sell_total})")
    print(f"   优化版本: {opt_sell_rate:.1f}% ({opt_sell_success}/{opt_sell_total})")
    print(f"   改进: {opt_sell_rate - orig_sell_rate:+.1f}个百分点")
    
    # 止损分析
    def calculate_stop_loss_rate(trades):
        if not trades:
            return 0
        stop_loss_trades = len([t for t in trades if t.exit_reason == 'stop_loss'])
        return stop_loss_trades / len(trades) * 100
    
    orig_stop_loss_rate = calculate_stop_loss_rate(original_report.trade_results)
    opt_stop_loss_rate = calculate_stop_loss_rate(optimized_report.trade_results)
    
    print(f"\n🛑 止损比例对比:")
    print(f"   原始版本: {orig_stop_loss_rate:.1f}%")
    print(f"   优化版本: {opt_stop_loss_rate:.1f}%")
    print(f"   改进: {orig_stop_loss_rate - opt_stop_loss_rate:+.1f}个百分点")

def generate_improvement_report(original_report, optimized_report):
    """生成改进效果报告"""
    print("\n📋 改进效果总结报告:")
    
    # 计算关键改进指标
    win_rate_improvement = optimized_report.win_rate - original_report.win_rate
    profit_improvement = optimized_report.total_profit_loss_pct - original_report.total_profit_loss_pct
    drawdown_improvement = original_report.max_drawdown - optimized_report.max_drawdown
    sharpe_improvement = optimized_report.sharpe_ratio - original_report.sharpe_ratio
    
    print(f"\n🎯 核心改进指标:")
    print(f"   胜率改进: {win_rate_improvement:+.1%}")
    print(f"   收益改进: {profit_improvement:+.2f}个百分点")
    print(f"   回撤改善: {drawdown_improvement:+.2f}个百分点")
    print(f"   夏普比率改进: {sharpe_improvement:+.2f}")
    
    # 评估改进效果
    print(f"\n📊 改进效果评估:")
    
    improvements = []
    if win_rate_improvement >= 0.1:  # 胜率提升10%+
        improvements.append("✅ 胜率显著提升")
    elif win_rate_improvement > 0:
        improvements.append("🟡 胜率有所提升")
    else:
        improvements.append("❌ 胜率未改善")
    
    if profit_improvement >= 2:  # 收益提升2%+
        improvements.append("✅ 收益显著提升")
    elif profit_improvement > 0:
        improvements.append("🟡 收益有所提升")
    else:
        improvements.append("❌ 收益未改善")
    
    if drawdown_improvement >= 2:  # 回撤减少2%+
        improvements.append("✅ 风险控制显著改善")
    elif drawdown_improvement > 0:
        improvements.append("🟡 风险控制有所改善")
    else:
        improvements.append("❌ 风险控制未改善")
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    # 总体评估
    success_count = len([imp for imp in improvements if "✅" in imp])
    partial_count = len([imp for imp in improvements if "🟡" in imp])
    
    print(f"\n🏆 总体评估:")
    if success_count >= 2:
        print("   🟢 优化效果显著，建议采用优化版本")
    elif success_count + partial_count >= 2:
        print("   🟡 优化效果一般，可考虑进一步调优")
    else:
        print("   🔴 优化效果不明显，需要重新设计优化策略")
    
    # 具体建议
    print(f"\n💡 后续建议:")
    if win_rate_improvement < 0.05:  # 胜率提升不足5%
        print("   • 进一步优化信号质量门槛")
        print("   • 加强卖出信号验证条件")
    
    if profit_improvement < 1:  # 收益提升不足1%
        print("   • 优化止盈止损策略")
        print("   • 调整仓位管理")
    
    if drawdown_improvement < 1:  # 回撤改善不足1%
        print("   • 加强风险控制机制")
        print("   • 优化入场时机选择")
    
    print("   • 扩展回测时间周期验证")
    print("   • 测试不同市场条件")
    print("   • 考虑多品种验证")

if __name__ == "__main__":
    # 运行对比回测
    original_report, optimized_report = run_comparison_backtest()
    
    if original_report and optimized_report:
        print("\n" + "="*80)
        print("🎯 对比回测验证完成")
        print("="*80)
        
        # 额外分析
        analyze_signal_quality_improvement(original_report, optimized_report)
        analyze_trade_quality_improvement(original_report, optimized_report)
        
        print(f"\n🔄 验证结论:")
        if optimized_report.win_rate > original_report.win_rate:
            print(f"   ✅ 优化版本胜率更高: {optimized_report.win_rate:.1%} vs {original_report.win_rate:.1%}")
        else:
            print(f"   ❌ 优化版本胜率未提升: {optimized_report.win_rate:.1%} vs {original_report.win_rate:.1%}")
        
        if optimized_report.total_profit_loss_pct > original_report.total_profit_loss_pct:
            print(f"   ✅ 优化版本收益更高: {optimized_report.total_profit_loss_pct:.2f}% vs {original_report.total_profit_loss_pct:.2f}%")
        else:
            print(f"   ❌ 优化版本收益未提升: {optimized_report.total_profit_loss_pct:.2f}% vs {original_report.total_profit_loss_pct:.2f}%")
        
    else:
        print("❌ 对比回测失败，请检查系统配置")

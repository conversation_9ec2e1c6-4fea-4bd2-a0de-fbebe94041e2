#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史回测功能
严格按照时间顺序，不倒果为因地测试系统的买卖点判断能力
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from loguru import logger
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_trading_system import EnhancedTradingSystem
from data_fetcher import DataFetcher

@dataclass
class BacktestSignal:
    """回测信号记录"""
    timestamp: datetime
    action: str                    # buy/sell/wait
    entry_price: float
    stop_loss: float
    take_profits: List[float]
    confidence: float
    reasoning: str
    risk_level: str
    major_trend_status: Dict[str, Any]

@dataclass
class TradeResult:
    """交易结果"""
    signal: BacktestSignal
    entry_time: datetime
    exit_time: Optional[datetime]
    exit_price: Optional[float]
    exit_reason: str               # stop_loss/take_profit_1/take_profit_2/take_profit_3/timeout
    profit_loss: float             # 盈亏金额
    profit_loss_pct: float         # 盈亏百分比
    duration_minutes: int          # 持仓时间(分钟)
    success: bool                  # 是否成功

@dataclass
class BacktestReport:
    """回测报告"""
    start_time: datetime
    end_time: datetime
    total_signals: int
    buy_signals: int
    sell_signals: int
    wait_signals: int
    total_trades: int
    successful_trades: int
    failed_trades: int
    win_rate: float
    total_profit_loss: float
    total_profit_loss_pct: float
    max_drawdown: float
    sharpe_ratio: float
    trade_results: List[TradeResult]
    signal_accuracy: float         # 信号准确率
    avg_holding_time: float        # 平均持仓时间

class HistoricalBacktest:
    """历史回测器"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        self.symbol = symbol
        self.exchange = exchange
        
        # 初始化数据获取器
        self.data_fetcher = DataFetcher(exchange_id=exchange, symbols=[symbol])
        
        # 回测配置
        self.config = {
            'max_holding_time': 240,      # 最大持仓时间(分钟)
            'initial_capital': 10000,     # 初始资金
            'position_size': 0.1,         # 每次交易仓位大小
            'commission_rate': 0.001,     # 手续费率
            'slippage': 0.0005,          # 滑点
        }
        
        logger.info(f"历史回测器初始化完成 - {symbol}")
    
    def run_backtest(self, start_date: str, end_date: str, timeframe: str = '15m') -> BacktestReport:
        """
        运行历史回测
        
        参数:
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            timeframe: 时间周期
            
        返回:
            BacktestReport: 回测报告
        """
        try:
            logger.info(f"开始历史回测: {start_date} 到 {end_date}")
            
            # 1. 获取历史数据
            historical_data = self._get_historical_data(start_date, end_date, timeframe)
            if historical_data.empty:
                raise ValueError("无法获取历史数据")
            
            logger.info(f"获取历史数据成功: {len(historical_data)} 条记录")
            
            # 2. 按时间顺序逐个分析
            signals = []
            trades = []
            
            # 需要足够的历史数据来进行分析，所以从第200个数据点开始
            start_index = 200
            
            for i in range(start_index, len(historical_data)):
                current_time = historical_data.index[i]
                
                # 3. 获取当前时刻之前的数据（严格不倒果为因）
                available_data = historical_data.iloc[:i+1]  # 包含当前时刻，但不包含未来数据
                
                # 4. 基于可用数据进行分析
                signal = self._analyze_at_timestamp(available_data, current_time)
                
                if signal:
                    signals.append(signal)
                    
                    # 5. 如果是买卖信号，模拟交易
                    if signal.action in ['buy', 'sell']:
                        trade_result = self._simulate_trade(signal, historical_data, i)
                        if trade_result:
                            trades.append(trade_result)
                
                # 每100个点输出一次进度
                if i % 100 == 0:
                    progress = (i - start_index) / (len(historical_data) - start_index) * 100
                    logger.info(f"回测进度: {progress:.1f}% ({i}/{len(historical_data)})")
            
            # 6. 生成回测报告
            report = self._generate_backtest_report(signals, trades, 
                                                  historical_data.index[start_index], 
                                                  historical_data.index[-1])
            
            logger.info(f"历史回测完成: 总信号{len(signals)}个, 交易{len(trades)}笔")
            return report
            
        except Exception as e:
            logger.error(f"历史回测失败: {str(e)}")
            raise
    
    def _get_historical_data(self, start_date: str, end_date: str, timeframe: str) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 计算需要的数据量
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            days = (end_dt - start_dt).days + 1
            
            # 根据时间周期计算K线数量
            timeframe_minutes = {
                '1m': 1, '5m': 5, '15m': 15, '30m': 30, 
                '1h': 60, '4h': 240, '1d': 1440
            }
            
            minutes_per_day = 1440
            period_minutes = timeframe_minutes.get(timeframe, 15)
            limit = int(days * minutes_per_day / period_minutes) + 500  # 多获取一些数据
            
            # 获取数据
            df = self.data_fetcher.get_klines(symbol=self.symbol, limit=limit)
            
            if not df.empty:
                # 过滤到指定日期范围
                df.index = pd.to_datetime(df.index)
                start_filter = pd.to_datetime(start_date)
                end_filter = pd.to_datetime(end_date) + timedelta(days=1)
                
                df = df[(df.index >= start_filter) & (df.index < end_filter)]
                
                logger.info(f"获取历史数据成功: {len(df)} 条 {timeframe} 数据")
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _analyze_at_timestamp(self, available_data: pd.DataFrame, current_time: datetime) -> Optional[BacktestSignal]:
        """
        在指定时间点进行分析（严格不使用未来数据）
        
        参数:
            available_data: 当前时刻及之前的所有可用数据
            current_time: 当前分析时间点
            
        返回:
            BacktestSignal: 分析信号
        """
        try:
            # 创建一个临时的交易系统实例
            # 注意：这里我们需要模拟系统在历史时刻的状态
            
            # 由于我们的系统需要多级别数据，我们需要为每个级别准备数据
            # 但只能使用当前时刻之前的数据
            
            # 简化处理：使用可用数据的最后200个点进行分析
            analysis_data = available_data.tail(200) if len(available_data) >= 200 else available_data
            
            if len(analysis_data) < 50:  # 数据不足，无法分析
                return None
            
            # 模拟系统分析（这里需要修改系统以支持历史数据分析）
            signal = self._simulate_system_analysis(analysis_data, current_time)
            
            return signal
            
        except Exception as e:
            logger.warning(f"在时间点 {current_time} 分析失败: {str(e)}")
            return None
    
    def _simulate_system_analysis(self, data: pd.DataFrame, timestamp: datetime) -> Optional[BacktestSignal]:
        """
        模拟系统在历史时刻的分析
        
        这里我们需要创建一个特殊版本的系统，它只使用历史数据
        """
        try:
            # 为了简化，我们先实现一个基础版本
            # 基于简单的技术指标进行判断
            
            if len(data) < 20:
                return None
            
            # 计算技术指标
            data = data.copy()
            
            # 移动平均线
            data['ma5'] = data['close'].rolling(5).mean()
            data['ma20'] = data['close'].rolling(20).mean()
            
            # MACD
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            data['macd'] = exp1 - exp2
            data['macd_signal'] = data['macd'].ewm(span=9).mean()
            
            # RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # 获取最新值
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) >= 2 else latest
            
            # 简单的买卖判断逻辑
            action = 'wait'
            confidence = 0.5
            reasoning = "基于技术指标分析"
            
            # 买入条件
            if (latest['close'] > latest['ma5'] > latest['ma20'] and  # 价格在均线之上
                latest['macd'] > latest['macd_signal'] and           # MACD金叉
                latest['rsi'] > 30 and latest['rsi'] < 70 and        # RSI在合理区间
                latest['close'] > prev['close']):                    # 价格上涨
                
                action = 'buy'
                confidence = 0.7
                reasoning = "技术指标显示买入信号：价格突破均线，MACD金叉，RSI合理"
                
            # 卖出条件
            elif (latest['close'] < latest['ma5'] < latest['ma20'] and  # 价格在均线之下
                  latest['macd'] < latest['macd_signal'] and            # MACD死叉
                  latest['rsi'] > 30 and latest['rsi'] < 70 and         # RSI在合理区间
                  latest['close'] < prev['close']):                     # 价格下跌
                
                action = 'sell'
                confidence = 0.7
                reasoning = "技术指标显示卖出信号：价格跌破均线，MACD死叉，RSI合理"
            
            # 计算止盈止损
            entry_price = latest['close']
            
            if action == 'buy':
                stop_loss = entry_price * 0.98  # 2%止损
                take_profits = [
                    entry_price * 1.02,  # 2%止盈
                    entry_price * 1.04,  # 4%止盈
                    entry_price * 1.06   # 6%止盈
                ]
            elif action == 'sell':
                stop_loss = entry_price * 1.02  # 2%止损
                take_profits = [
                    entry_price * 0.98,  # 2%止盈
                    entry_price * 0.96,  # 4%止盈
                    entry_price * 0.94   # 6%止盈
                ]
            else:
                stop_loss = 0
                take_profits = []
            
            # 创建信号
            signal = BacktestSignal(
                timestamp=timestamp,
                action=action,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profits=take_profits,
                confidence=confidence,
                reasoning=reasoning,
                risk_level='MEDIUM',
                major_trend_status={'status': 'unknown', 'confidence': 0.5}
            )
            
            return signal
            
        except Exception as e:
            logger.warning(f"模拟系统分析失败: {str(e)}")
            return None
    
    def _simulate_trade(self, signal: BacktestSignal, full_data: pd.DataFrame, 
                       entry_index: int) -> Optional[TradeResult]:
        """
        模拟交易执行
        
        参数:
            signal: 交易信号
            full_data: 完整的历史数据
            entry_index: 入场时间在数据中的索引
            
        返回:
            TradeResult: 交易结果
        """
        try:
            if signal.action not in ['buy', 'sell']:
                return None
            
            entry_time = signal.timestamp
            entry_price = signal.entry_price
            stop_loss = signal.stop_loss
            take_profits = signal.take_profits
            
            # 模拟交易执行
            max_holding_time = self.config['max_holding_time']
            end_index = min(entry_index + max_holding_time, len(full_data) - 1)
            
            # 检查后续价格走势
            for i in range(entry_index + 1, end_index + 1):
                current_bar = full_data.iloc[i]
                current_time = full_data.index[i]
                
                high = current_bar['high']
                low = current_bar['low']
                close = current_bar['close']
                
                # 检查止损
                if signal.action == 'buy':
                    if low <= stop_loss:
                        return TradeResult(
                            signal=signal,
                            entry_time=entry_time,
                            exit_time=current_time,
                            exit_price=stop_loss,
                            exit_reason='stop_loss',
                            profit_loss=(stop_loss - entry_price) * self.config['position_size'],
                            profit_loss_pct=(stop_loss - entry_price) / entry_price * 100,
                            duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                            success=False
                        )
                    
                    # 检查止盈
                    for j, tp in enumerate(take_profits):
                        if high >= tp:
                            return TradeResult(
                                signal=signal,
                                entry_time=entry_time,
                                exit_time=current_time,
                                exit_price=tp,
                                exit_reason=f'take_profit_{j+1}',
                                profit_loss=(tp - entry_price) * self.config['position_size'],
                                profit_loss_pct=(tp - entry_price) / entry_price * 100,
                                duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                                success=True
                            )
                
                elif signal.action == 'sell':
                    if high >= stop_loss:
                        return TradeResult(
                            signal=signal,
                            entry_time=entry_time,
                            exit_time=current_time,
                            exit_price=stop_loss,
                            exit_reason='stop_loss',
                            profit_loss=(entry_price - stop_loss) * self.config['position_size'],
                            profit_loss_pct=(entry_price - stop_loss) / entry_price * 100,
                            duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                            success=False
                        )
                    
                    # 检查止盈
                    for j, tp in enumerate(take_profits):
                        if low <= tp:
                            return TradeResult(
                                signal=signal,
                                entry_time=entry_time,
                                exit_time=current_time,
                                exit_price=tp,
                                exit_reason=f'take_profit_{j+1}',
                                profit_loss=(entry_price - tp) * self.config['position_size'],
                                profit_loss_pct=(entry_price - tp) / entry_price * 100,
                                duration_minutes=int((current_time - entry_time).total_seconds() / 60),
                                success=True
                            )
            
            # 超时平仓
            final_bar = full_data.iloc[end_index]
            final_time = full_data.index[end_index]
            final_price = final_bar['close']
            
            if signal.action == 'buy':
                profit_loss = (final_price - entry_price) * self.config['position_size']
                profit_loss_pct = (final_price - entry_price) / entry_price * 100
            else:  # sell
                profit_loss = (entry_price - final_price) * self.config['position_size']
                profit_loss_pct = (entry_price - final_price) / entry_price * 100
            
            return TradeResult(
                signal=signal,
                entry_time=entry_time,
                exit_time=final_time,
                exit_price=final_price,
                exit_reason='timeout',
                profit_loss=profit_loss,
                profit_loss_pct=profit_loss_pct,
                duration_minutes=int((final_time - entry_time).total_seconds() / 60),
                success=profit_loss > 0
            )
            
        except Exception as e:
            logger.warning(f"模拟交易失败: {str(e)}")
            return None

    def _generate_backtest_report(self, signals: List[BacktestSignal], trades: List[TradeResult],
                                start_time: datetime, end_time: datetime) -> BacktestReport:
        """生成回测报告"""
        try:
            # 统计信号
            total_signals = len(signals)
            buy_signals = len([s for s in signals if s.action == 'buy'])
            sell_signals = len([s for s in signals if s.action == 'sell'])
            wait_signals = len([s for s in signals if s.action == 'wait'])

            # 统计交易
            total_trades = len(trades)
            successful_trades = len([t for t in trades if t.success])
            failed_trades = total_trades - successful_trades

            # 计算胜率
            win_rate = successful_trades / total_trades if total_trades > 0 else 0

            # 计算总盈亏
            total_profit_loss = sum(t.profit_loss for t in trades)
            total_profit_loss_pct = sum(t.profit_loss_pct for t in trades)

            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(trades)

            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio(trades)

            # 计算信号准确率
            signal_accuracy = self._calculate_signal_accuracy(signals, trades)

            # 计算平均持仓时间
            avg_holding_time = sum(t.duration_minutes for t in trades) / len(trades) if trades else 0

            report = BacktestReport(
                start_time=start_time,
                end_time=end_time,
                total_signals=total_signals,
                buy_signals=buy_signals,
                sell_signals=sell_signals,
                wait_signals=wait_signals,
                total_trades=total_trades,
                successful_trades=successful_trades,
                failed_trades=failed_trades,
                win_rate=win_rate,
                total_profit_loss=total_profit_loss,
                total_profit_loss_pct=total_profit_loss_pct,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                trade_results=trades,
                signal_accuracy=signal_accuracy,
                avg_holding_time=avg_holding_time
            )

            return report

        except Exception as e:
            logger.error(f"生成回测报告失败: {str(e)}")
            raise

    def _calculate_max_drawdown(self, trades: List[TradeResult]) -> float:
        """计算最大回撤"""
        try:
            if not trades:
                return 0.0

            cumulative_returns = []
            cumulative = 0

            for trade in trades:
                cumulative += trade.profit_loss_pct
                cumulative_returns.append(cumulative)

            if not cumulative_returns:
                return 0.0

            peak = cumulative_returns[0]
            max_drawdown = 0

            for return_val in cumulative_returns:
                if return_val > peak:
                    peak = return_val

                drawdown = peak - return_val
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            return max_drawdown

        except Exception as e:
            logger.warning(f"计算最大回撤失败: {str(e)}")
            return 0.0

    def _calculate_sharpe_ratio(self, trades: List[TradeResult]) -> float:
        """计算夏普比率"""
        try:
            if not trades:
                return 0.0

            returns = [t.profit_loss_pct for t in trades]

            if len(returns) < 2:
                return 0.0

            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return == 0:
                return 0.0

            # 假设无风险利率为0
            sharpe_ratio = mean_return / std_return

            return sharpe_ratio

        except Exception as e:
            logger.warning(f"计算夏普比率失败: {str(e)}")
            return 0.0

    def _calculate_signal_accuracy(self, signals: List[BacktestSignal], trades: List[TradeResult]) -> float:
        """计算信号准确率"""
        try:
            if not trades:
                return 0.0

            # 简单计算：成功交易数 / 总交易数
            successful_trades = len([t for t in trades if t.success])
            total_trades = len(trades)

            return successful_trades / total_trades if total_trades > 0 else 0.0

        except Exception as e:
            logger.warning(f"计算信号准确率失败: {str(e)}")
            return 0.0

    def print_backtest_report(self, report: BacktestReport):
        """打印回测报告"""
        print("=" * 80)
        print("📊 历史回测报告")
        print("=" * 80)

        # 基本信息
        print(f"\n📅 回测期间: {report.start_time.strftime('%Y-%m-%d')} 到 {report.end_time.strftime('%Y-%m-%d')}")
        print(f"⏱️ 回测天数: {(report.end_time - report.start_time).days} 天")

        # 信号统计
        print(f"\n📡 信号统计:")
        print(f"   总信号数: {report.total_signals}")
        print(f"   买入信号: {report.buy_signals} ({report.buy_signals/report.total_signals*100:.1f}%)")
        print(f"   卖出信号: {report.sell_signals} ({report.sell_signals/report.total_signals*100:.1f}%)")
        print(f"   等待信号: {report.wait_signals} ({report.wait_signals/report.total_signals*100:.1f}%)")

        # 交易统计
        print(f"\n💼 交易统计:")
        print(f"   总交易数: {report.total_trades}")
        print(f"   成功交易: {report.successful_trades}")
        print(f"   失败交易: {report.failed_trades}")
        print(f"   胜率: {report.win_rate:.1%}")
        print(f"   信号准确率: {report.signal_accuracy:.1%}")

        # 盈亏统计
        print(f"\n💰 盈亏统计:")
        print(f"   总盈亏: {report.total_profit_loss:.2f} USDT")
        print(f"   总盈亏率: {report.total_profit_loss_pct:.2f}%")
        print(f"   最大回撤: {report.max_drawdown:.2f}%")
        print(f"   夏普比率: {report.sharpe_ratio:.2f}")
        print(f"   平均持仓时间: {report.avg_holding_time:.1f} 分钟")

        # 详细交易记录（显示前10笔）
        if report.trade_results:
            print(f"\n📋 交易记录 (前10笔):")
            print(f"{'时间':<20} {'动作':<6} {'入场价':<10} {'出场价':<10} {'盈亏%':<8} {'原因':<15}")
            print("-" * 80)

            for i, trade in enumerate(report.trade_results[:10]):
                action_emoji = "📈" if trade.signal.action == 'buy' else "📉"
                success_emoji = "✅" if trade.success else "❌"

                print(f"{trade.entry_time.strftime('%Y-%m-%d %H:%M'):<20} "
                      f"{action_emoji}{trade.signal.action:<5} "
                      f"{trade.signal.entry_price:<10.2f} "
                      f"{trade.exit_price:<10.2f} "
                      f"{success_emoji}{trade.profit_loss_pct:<7.2f} "
                      f"{trade.exit_reason:<15}")

            if len(report.trade_results) > 10:
                print(f"... 还有 {len(report.trade_results) - 10} 笔交易")

        # 总结
        print(f"\n🎯 回测总结:")
        if report.win_rate >= 0.6:
            print("   ✅ 系统表现优秀，胜率较高")
        elif report.win_rate >= 0.4:
            print("   🟡 系统表现一般，有改进空间")
        else:
            print("   ❌ 系统表现较差，需要优化")

        if report.total_profit_loss_pct > 0:
            print(f"   💰 总体盈利 {report.total_profit_loss_pct:.2f}%")
        else:
            print(f"   📉 总体亏损 {abs(report.total_profit_loss_pct):.2f}%")

        print("=" * 80)

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速验证调优版本
测试调优后的信号生成器能否平衡信号质量和数量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_backtest import EnhancedBacktest
from 调优信号生成器 import TunedSignalGenerator
from datetime import datetime, timedelta
from loguru import logger

class TunedBacktest(EnhancedBacktest):
    """调优版回测系统"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        super().__init__(symbol, exchange, use_optimized_signals=False)
        self.tuned_signal_generator = TunedSignalGenerator()
        logger.info("调优版回测系统初始化完成")
    
    def _generate_signal_from_chan_analysis(self, chan_analyzer, data, timestamp, timeframe):
        """使用调优信号生成器"""
        try:
            # 使用调优信号生成器
            tuned_signal = self.tuned_signal_generator.generate_optimized_signal(
                data, chan_analyzer, timestamp
            )
            
            if not tuned_signal or tuned_signal.action == 'wait':
                return None
            
            # 转换为原始信号格式
            from enhanced_backtest import BacktestSignal
            
            signal = BacktestSignal(
                timestamp=timestamp,
                action=tuned_signal.action,
                entry_price=tuned_signal.entry_price,
                stop_loss=tuned_signal.stop_loss,
                take_profits=tuned_signal.take_profits,
                confidence=tuned_signal.confidence,
                reasoning=tuned_signal.reasoning,
                risk_level=tuned_signal.risk_level,
                timeframe=timeframe,
                major_trend_status={'status': 'tuned', 'confidence': tuned_signal.confidence}
            )
            
            return signal
            
        except Exception as e:
            logger.warning(f"调优信号生成失败: {str(e)}")
            return super()._generate_signal_from_chan_analysis(chan_analyzer, data, timestamp, timeframe)

def run_tuned_validation():
    """运行调优验证"""
    print("=" * 80)
    print("🔧 调优版本快速验证")
    print("=" * 80)
    print("📋 目标: 验证调优后的参数能否平衡信号质量和数量")
    print("🎯 期望: 胜率55%+，交易数量25-35笔")
    
    try:
        # 设置回测参数
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        print(f"\n📅 回测期间: {start_date_str} 到 {end_date_str}")
        print(f"⏰ 时间周期: 15分钟")
        print(f"💰 交易品种: BTC/USDT")
        
        # 运行调优版本回测
        print("\n🔧 运行调优版本回测...")
        tuned_backtest = TunedBacktest(symbol="BTC/USDT", exchange="gate")
        
        tuned_report = tuned_backtest.run_enhanced_backtest(
            start_date=start_date_str,
            end_date=end_date_str,
            primary_timeframe='15m'
        )
        
        print("✅ 调优版本回测完成")
        
        # 快速分析结果
        print("\n📊 调优版本结果分析:")
        analyze_tuned_results(tuned_report)
        
        # 与之前版本对比
        print("\n📈 与之前版本对比:")
        compare_with_previous_versions(tuned_report)
        
        # 给出调优建议
        print("\n💡 进一步调优建议:")
        suggest_further_tuning(tuned_report)
        
        return tuned_report
        
    except Exception as e:
        logger.error(f"调优验证失败: {str(e)}")
        print(f"❌ 调优验证失败: {str(e)}")
        return None

def analyze_tuned_results(report):
    """分析调优结果"""
    print(f"   总信号数: {report.total_signals}")
    print(f"   买入信号: {report.buy_signals}")
    print(f"   卖出信号: {report.sell_signals}")
    print(f"   等待信号: {report.wait_signals}")
    print(f"   总交易数: {report.total_trades}")
    print(f"   成功交易: {report.successful_trades}")
    print(f"   失败交易: {report.failed_trades}")
    print(f"   胜率: {report.win_rate:.1%}")
    print(f"   总收益率: {report.total_profit_loss_pct:.2f}%")
    print(f"   最大回撤: {report.max_drawdown:.2f}%")
    print(f"   夏普比率: {report.sharpe_ratio:.2f}")
    
    # 评估调优效果
    print(f"\n🎯 调优效果评估:")
    
    # 信号数量评估
    if 25 <= report.total_trades <= 35:
        print("   ✅ 交易数量适中 (25-35笔)")
    elif report.total_trades < 25:
        print("   ⚠️ 交易数量偏少，可以进一步放宽条件")
    else:
        print("   ⚠️ 交易数量偏多，可能需要提高门槛")
    
    # 胜率评估
    if report.win_rate >= 0.55:
        print("   ✅ 胜率达标 (≥55%)")
    elif report.win_rate >= 0.50:
        print("   🟡 胜率接近目标 (50-55%)")
    else:
        print("   ❌ 胜率偏低 (<50%)")
    
    # 收益评估
    if report.total_profit_loss_pct > 2:
        print("   ✅ 收益良好 (>2%)")
    elif report.total_profit_loss_pct > 0:
        print("   🟡 收益一般 (0-2%)")
    else:
        print("   ❌ 收益为负")
    
    # 风险评估
    if report.max_drawdown <= 3:
        print("   ✅ 风险控制良好 (≤3%)")
    elif report.max_drawdown <= 5:
        print("   🟡 风险控制一般 (3-5%)")
    else:
        print("   ❌ 风险控制需要改进 (>5%)")

def compare_with_previous_versions(tuned_report):
    """与之前版本对比"""
    # 这里使用之前回测的结果进行对比
    original_stats = {
        'total_trades': 45,
        'win_rate': 0.556,
        'total_profit_loss_pct': 4.46,
        'max_drawdown': 3.17
    }
    
    optimized_stats = {
        'total_trades': 19,
        'win_rate': 0.474,
        'total_profit_loss_pct': 1.55,
        'max_drawdown': 1.83
    }
    
    print(f"   版本对比:")
    print(f"   {'指标':<12} {'原始版本':<12} {'优化版本':<12} {'调优版本':<12}")
    print(f"   {'-'*50}")
    print(f"   {'交易数量':<12} {original_stats['total_trades']:<12} {optimized_stats['total_trades']:<12} {tuned_report.total_trades:<12}")
    print(f"   {'胜率':<12} {original_stats['win_rate']:.1%:<12} {optimized_stats['win_rate']:.1%:<12} {tuned_report.win_rate:.1%:<12}")
    print(f"   {'收益率':<12} {original_stats['total_profit_loss_pct']:.2f}%<12 {optimized_stats['total_profit_loss_pct']:.2f}%<12 {tuned_report.total_profit_loss_pct:.2f}%<12")
    print(f"   {'最大回撤':<12} {original_stats['max_drawdown']:.2f}%<12 {optimized_stats['max_drawdown']:.2f}%<12 {tuned_report.max_drawdown:.2f}%<12")
    
    # 评估调优是否成功
    print(f"\n   调优成功度评估:")
    
    # 交易数量是否在合理范围
    if optimized_stats['total_trades'] < tuned_report.total_trades < original_stats['total_trades']:
        print("   ✅ 交易数量平衡良好")
    else:
        print("   ⚠️ 交易数量需要进一步调整")
    
    # 胜率是否有改善
    if tuned_report.win_rate > optimized_stats['win_rate']:
        print("   ✅ 胜率相比优化版本有提升")
    else:
        print("   ❌ 胜率仍需改善")
    
    # 是否找到了平衡点
    balance_score = 0
    if 25 <= tuned_report.total_trades <= 35:
        balance_score += 1
    if tuned_report.win_rate >= 0.5:
        balance_score += 1
    if tuned_report.total_profit_loss_pct > 0:
        balance_score += 1
    if tuned_report.max_drawdown <= 4:
        balance_score += 1
    
    print(f"   平衡度评分: {balance_score}/4")
    if balance_score >= 3:
        print("   ✅ 调优基本成功")
    elif balance_score >= 2:
        print("   🟡 调优部分成功")
    else:
        print("   ❌ 调优需要重新设计")

def suggest_further_tuning(report):
    """建议进一步调优"""
    suggestions = []
    
    # 基于结果给出具体建议
    if report.total_trades < 25:
        suggestions.append("• 进一步降低置信度门槛 (0.58 → 0.55)")
        suggestions.append("• 扩大分型信号时间窗口 (5 → 7个K线)")
        suggestions.append("• 降低成交量阈值 (1.3 → 1.2)")
    
    if report.total_trades > 35:
        suggestions.append("• 适当提高置信度门槛 (0.58 → 0.62)")
        suggestions.append("• 增加最少条件数 (1 → 2)")
        suggestions.append("• 提高成交量阈值 (1.3 → 1.4)")
    
    if report.win_rate < 0.5:
        suggestions.append("• 加强卖出信号验证条件")
        suggestions.append("• 优化止损策略 (ATR倍数 2.0 → 1.8)")
        suggestions.append("• 增加趋势过滤强度")
    
    if report.max_drawdown > 4:
        suggestions.append("• 降低ATR止损倍数 (2.0 → 1.5)")
        suggestions.append("• 增加风险控制条件")
        suggestions.append("• 缩小止盈目标")
    
    if report.total_profit_loss_pct < 1:
        suggestions.append("• 优化止盈策略")
        suggestions.append("• 调整仓位管理")
        suggestions.append("• 延长持仓时间")
    
    if suggestions:
        for suggestion in suggestions:
            print(f"   {suggestion}")
    else:
        print("   ✅ 当前参数设置较为合理，建议进行更长时间的回测验证")
    
    # 下一步行动建议
    print(f"\n🔄 下一步行动建议:")
    print(f"   1. 根据上述建议微调参数")
    print(f"   2. 进行7天回测验证")
    print(f"   3. 测试不同市场条件")
    print(f"   4. 多品种验证")
    print(f"   5. 准备实盘小资金测试")

if __name__ == "__main__":
    # 运行调优验证
    report = run_tuned_validation()
    
    if report:
        print("\n" + "=" * 80)
        print("🎯 调优验证完成")
        print("=" * 80)
        
        # 最终评估
        if report.win_rate >= 0.5 and 25 <= report.total_trades <= 35:
            print("🟢 调优成功！找到了信号质量和数量的平衡点")
        elif report.win_rate >= 0.45 and report.total_trades >= 20:
            print("🟡 调优部分成功，需要进一步微调")
        else:
            print("🔴 调优需要重新设计策略")
        
        print(f"\n📊 最终指标:")
        print(f"   胜率: {report.win_rate:.1%}")
        print(f"   交易数: {report.total_trades}笔")
        print(f"   收益: {report.total_profit_loss_pct:.2f}%")
        print(f"   回撤: {report.max_drawdown:.2f}%")
        
    else:
        print("❌ 调优验证失败，请检查系统配置")

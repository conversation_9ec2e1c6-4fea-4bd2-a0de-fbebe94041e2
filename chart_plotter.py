#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缠论画线可视化模块：
绘制K线图，并标注分型、笔、线段等信息，方便人工判断
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.gridspec import GridSpec
from mplfinance.original_flavor import candlestick_ohlc
import matplotlib.ticker as ticker
from loguru import logger
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import math

class ChartPlotter:
    """
    缠论画线可视化工具
    绘制K线图，并标注分型、笔、线段等信息
    """
    def __init__(self, save_dir='charts'):
        """
        初始化绘图工具
        
        参数:
            save_dir: 图表保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置绘图样式 - 修改为深色背景，类似参考图片
        plt.style.use('dark_background')
        plt.rcParams['figure.facecolor'] = '#161a2b'  # 深蓝色背景
        plt.rcParams['axes.facecolor'] = '#161a2b'    # 深蓝色背景
        plt.rcParams['savefig.facecolor'] = '#161a2b'
        plt.rcParams['axes.grid'] = True
        plt.rcParams['grid.color'] = '#2a2e3e'
        plt.rcParams['grid.linestyle'] = ':'
        plt.rcParams['axes.edgecolor'] = '#2a2e3e'
        plt.rcParams['text.color'] = 'white'
        plt.rcParams['axes.labelcolor'] = 'white'
        plt.rcParams['xtick.color'] = 'white'
        plt.rcParams['ytick.color'] = 'white'
        
        self.colors = {
            'up_candle': '#ff4d4d',      # 上涨K线颜色（红色）
            'down_candle': '#33cc33',    # 下跌K线颜色（绿色）
            'price_line': '#1a73e8',     # 价格线颜色（蓝色）
            'top_fractal': '#ff0000',    # 顶分型颜色（红色）
            'bottom_fractal': '#00ff00', # 底分型颜色（绿色）
            'up_stroke': '#00ff00',      # 上升笔颜色（绿色）- 修改为绿色以区分
            'down_stroke': '#ff0000',    # 下降笔颜色（红色）- 修改为红色以区分
            'up_segment': '#ffff00',     # 上升线段颜色（黄色）- 修改为黄色以区分
            'down_segment': '#00ffff',   # 下降线段颜色（青色）- 修改为青色以区分
            'ma5': '#ff00ff',            # MA5均线颜色（紫色）
            'ma10': '#ffa500',           # MA10均线颜色（橙色）
            'ma20': '#ffffff',           # MA20均线颜色（白色）
            'macd': '#ffa500',           # MACD颜色（橙色）
            'signal': '#ffffff',         # MACD信号线颜色（白色）
            'histogram_pos': '#33cc33',  # MACD柱状图(正)颜色（绿色）
            'histogram_neg': '#ff4d4d',  # MACD柱状图(负)颜色（红色）
            'pivot_zone': '#ffa500',     # 中枢区域颜色（橙色）
            'trend_line': '#ff6600',     # 主趋势线颜色（橙红色）- 修改为橙红色以区分
            'evolution_line': '#ff00ff', # 走势推演线颜色（紫色）- 新增
            'evolution_zone': '#ff00ff', # 走势推演区域颜色（紫色半透明）- 新增
            'buy_point': '#33cc33',      # 买点颜色（绿色）
            'sell_point': '#ff4d4d'      # 卖点颜色（红色）
        }
        
        # 设置字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']  # 中文字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    def identify_pivot_zones(self, bi_list, df):
        """
        识别中枢区域
        
        参数:
            bi_list: 笔列表，每个笔为(start_idx, end_idx, start_val, end_val, direction)元组
            df: 数据DataFrame
            
        返回:
            list: 中枢列表，每个中枢为(start_idx, end_idx, high, low, start_date, end_date)元组
        """
        if not bi_list or len(bi_list) < 5:
            return []
        
        pivot_zones = []
        
        # 至少需要三笔才能形成中枢
        for i in range(len(bi_list) - 3):
            # 取连续的三笔
            bi1 = bi_list[i]
            bi2 = bi_list[i+1]
            bi3 = bi_list[i+2]
            
            # 计算这三笔的最高点和最低点
            if bi1[4] == 'up':
                high1, low1 = bi1[3], bi1[2]
            else:
                high1, low1 = bi1[2], bi1[3]
                
            if bi2[4] == 'up':
                high2, low2 = bi2[3], bi2[2]
            else:
                high2, low2 = bi2[2], bi2[3]
                
            if bi3[4] == 'up':
                high3, low3 = bi3[3], bi3[2]
            else:
                high3, low3 = bi3[2], bi3[3]
            
            # 计算中枢区间
            zg = min(high1, high2, high3)  # 中枢上沿
            zd = max(low1, low2, low3)     # 中枢下沿
            
            # 判断是否构成中枢
            if zg > zd:
                # 中枢区间有效
                start_idx = bi1[0]
                end_idx = bi3[1]
                
                # 获取中枢的起止日期
                start_date = df['timestamp'].iloc[start_idx]
                end_date = df['timestamp'].iloc[end_idx]
                
                # 添加到中枢列表
                pivot_zones.append((start_idx, end_idx, zg, zd, start_date, end_date))
        
        # 合并相邻的中枢
        merged_pivot_zones = []
        if pivot_zones:
            current_pivot = pivot_zones[0]
            
            for i in range(1, len(pivot_zones)):
                next_pivot = pivot_zones[i]
                
                # 如果下一个中枢与当前中枢相邻或重叠
                if next_pivot[0] <= current_pivot[1]:
                    # 合并中枢
                    zg = min(current_pivot[2], next_pivot[2])
                    zd = max(current_pivot[3], next_pivot[3])
                    
                    # 如果合并后的区间有效
                    if zg > zd:
                        current_pivot = (
                            current_pivot[0],
                            next_pivot[1],
                            zg,
                            zd,
                            current_pivot[4],
                            next_pivot[5]
                        )
                    else:
                        # 如果合并后区间无效，保留当前中枢，开始新的中枢
                        merged_pivot_zones.append(current_pivot)
                        current_pivot = next_pivot
                else:
                    # 如果不相邻，保存当前中枢，开始新的中枢
                    merged_pivot_zones.append(current_pivot)
                    current_pivot = next_pivot
            
            # 添加最后一个中枢
            merged_pivot_zones.append(current_pivot)
        
        return merged_pivot_zones
    
    def identify_first_buy_sell_points(self, bi_list, df, pivot_zones=None):
        """
        识别第一类买卖点，结合中枢和MACD指标进行判断
        
        第一类买点(1B)条件:
        1. 下跌笔后接上涨笔
        2. 上涨笔的底不破下跌笔的底
        3. 上涨笔的顶突破下跌笔的顶
        4. 最好在中枢下方或刚突破中枢
        5. MACD指标底背离或金叉确认
        
        第一类卖点(1S)条件:
        1. 上涨笔后接下跌笔
        2. 下跌笔的顶不破上涨笔的顶
        3. 下跌笔的底跌破上涨笔的底
        4. 最好在中枢上方或刚跌破中枢
        5. MACD指标顶背离或死叉确认
        
        参数:
            bi_list: 笔列表，每个笔为(start_idx, end_idx, start_val, end_val, direction)元组
            df: 数据DataFrame，需要包含MACD指标
            pivot_zones: 中枢列表，如果为None则自动计算
            
        返回:
            tuple: (买点列表, 卖点列表)，每个点为(index, price, strength)元组，strength表示信号强度(1-10)
        """
        if not bi_list or len(bi_list) < 2 or df is None or df.empty:
            return [], []
            
        # 如果未提供中枢，则计算中枢
        if pivot_zones is None:
            pivot_zones = self.identify_pivot_zones(bi_list, df)
            
        buy_points = []  # 第一类买点
        sell_points = [] # 第一类卖点
        
        # 检查每一对相邻的笔，寻找买卖点
        for i in range(1, len(bi_list)):
            prev_bi = bi_list[i-1]  # 前一笔
            curr_bi = bi_list[i]    # 当前笔
            
            prev_direction = prev_bi[4]  # 前一笔方向
            curr_direction = curr_bi[4]  # 当前笔方向
            
            # 获取笔的关键价格点
            if prev_direction == 'up':
                prev_high, prev_low = prev_bi[3], prev_bi[2]  # 顶点和底点
            else:
                prev_high, prev_low = prev_bi[2], prev_bi[3]  # 顶点和底点
                
            if curr_direction == 'up':
                curr_high, curr_low = curr_bi[3], curr_bi[2]  # 顶点和底点
            else:
                curr_high, curr_low = curr_bi[2], curr_bi[3]  # 顶点和底点
                
            # 判断是否为第一类买点 (1B)
            if prev_direction == 'down' and curr_direction == 'up':
                if curr_low >= prev_low and curr_high > prev_high:
                    # 基本条件满足，找到买点位置索引（上涨笔的起点）
                    buy_idx = curr_bi[0]
                    buy_price = curr_low
                    
                    # 计算信号强度 (初始为5分)
                    strength = 5
                    
                    # 加分项1: MACD底背离或金叉
                    if 'macd' in df.columns and 'macd_signal' in df.columns:
                        # 检查是否有MACD金叉
                        if buy_idx > 0 and buy_idx < len(df) - 1:
                            if df['macd'].iloc[buy_idx-1] < df['macd_signal'].iloc[buy_idx-1] and \
                               df['macd'].iloc[buy_idx] >= df['macd_signal'].iloc[buy_idx]:
                                strength += 2  # MACD金叉加2分
                            
                            # 检查是否有底背离
                            price_lower = df['low'].iloc[buy_idx] < df['low'].iloc[prev_bi[1]]
                            macd_higher = df['macd'].iloc[buy_idx] > df['macd'].iloc[prev_bi[1]]
                            if price_lower and macd_higher:
                                strength += 3  # 底背离加3分
                    
                    # 加分项2: 与中枢的关系
                    if pivot_zones:
                        # 检查买点是否在中枢下方或刚突破中枢
                        for _, _, zg, zd, _, _ in pivot_zones:
                            # 在中枢下方
                            if buy_price < zd:
                                strength += 1
                            # 刚突破中枢
                            elif prev_low < zd and curr_high > zd:
                                strength += 2
                                
                    # 将买点添加到列表
                    buy_points.append((buy_idx, buy_price, min(strength, 10)))
            
            # 判断是否为第一类卖点 (1S)
            if prev_direction == 'up' and curr_direction == 'down':
                if curr_high <= prev_high and curr_low < prev_low:
                    # 基本条件满足，找到卖点位置索引（下跌笔的起点）
                    sell_idx = curr_bi[0]
                    sell_price = curr_high
                    
                    # 计算信号强度 (初始为5分)
                    strength = 5
                    
                    # 加分项1: MACD顶背离或死叉
                    if 'macd' in df.columns and 'macd_signal' in df.columns:
                        # 检查是否有MACD死叉
                        if sell_idx > 0 and sell_idx < len(df) - 1:
                            if df['macd'].iloc[sell_idx-1] > df['macd_signal'].iloc[sell_idx-1] and \
                               df['macd'].iloc[sell_idx] <= df['macd_signal'].iloc[sell_idx]:
                                strength += 2  # MACD死叉加2分
                            
                            # 检查是否有顶背离
                            price_higher = df['high'].iloc[sell_idx] > df['high'].iloc[prev_bi[1]]
                            macd_lower = df['macd'].iloc[sell_idx] < df['macd'].iloc[prev_bi[1]]
                            if price_higher and macd_lower:
                                strength += 3  # 顶背离加3分
                    
                    # 加分项2: 与中枢的关系
                    if pivot_zones:
                        # 检查卖点是否在中枢上方或刚跌破中枢
                        for _, _, zg, zd, _, _ in pivot_zones:
                            # 在中枢上方
                            if sell_price > zg:
                                strength += 1
                            # 刚跌破中枢
                            elif prev_high > zg and curr_low < zg:
                                strength += 2
                    
                    # 将卖点添加到列表
                    sell_points.append((sell_idx, sell_price, min(strength, 10)))
        
        return buy_points, sell_points
    
    def identify_second_buy_sell_points(self, bi_list, df, pivot_zones=None):
        """
        识别第二类买卖点，结合中枢和MACD指标进行判断
        
        第二类买点(2B)条件（修改后更实用）:
        1. 在中枢形成后，价格回调接近中枢区域（允许略微跌破下沿）
        2. 随后再次向上突破中枢上沿
        3. MACD指标金叉或底背离增加信号强度
        
        第二类卖点(2S)条件（修改后更实用）:
        1. 在中枢形成后，价格反弹接近中枢区域（允许略微突破上沿）
        2. 随后再次向下跌破中枢下沿
        3. MACD指标死叉或顶背离增加信号强度
        
        参数:
            bi_list: 笔列表，每个笔为(start_idx, end_idx, start_val, end_val, direction)元组
            df: 数据DataFrame，需要包含MACD指标
            pivot_zones: 中枢列表，如果为None则自动计算
            
        返回:
            tuple: (买点列表, 卖点列表)，每个点为(index, price, strength, pivot_idx)元组
        """
        if not bi_list or len(bi_list) < 3 or df is None or df.empty:
            return [], []
            
        # 如果未提供中枢，则计算中枢
        if pivot_zones is None:
            pivot_zones = self.identify_pivot_zones(bi_list, df)
            
        if not pivot_zones:
            return [], []
            
        buy_points = []  # 第二类买点
        sell_points = [] # 第二类卖点
        
        # 遍历每个中枢，寻找第二类买卖点
        for pivot_idx, (pivot_start_idx, pivot_end_idx, zg, zd, pivot_start_date, pivot_end_date) in enumerate(pivot_zones):
            # 查找中枢形成后的笔
            post_pivot_bi = [bi for bi in bi_list if bi[0] >= pivot_end_idx]
            
            if len(post_pivot_bi) < 2:
                continue  # 中枢后至少需要两笔才能形成第二类买卖点
                
            # 计算中枢高度，用于判断回调/反弹的合理范围
            pivot_height = zg - zd
            tolerance = pivot_height * 0.3  # 允许30%的容差
            
            # 分析中枢后的连续两笔，检查是否形成第二类买卖点
            for i in range(len(post_pivot_bi) - 1):
                first_bi = post_pivot_bi[i]      # 第一笔
                second_bi = post_pivot_bi[i+1]   # 第二笔
                
                first_direction = first_bi[4]
                second_direction = second_bi[4]
                
                # 提取笔的价格
                if first_direction == 'up':
                    first_high, first_low = first_bi[3], first_bi[2]
                else:
                    first_high, first_low = first_bi[2], first_bi[3]
                    
                if second_direction == 'up':
                    second_high, second_low = second_bi[3], second_bi[2]
                else:
                    second_high, second_low = second_bi[2], second_bi[3]
                
                # 第二类买点检测：下跌笔回调接近中枢区域，然后上涨笔突破中枢上沿
                if first_direction == 'down' and second_direction == 'up':
                    # 修改后的条件：
                    # 1. 第一笔的低点接近或进入中枢区域（允许略微跌破下沿）
                    # 2. 第二笔突破中枢上沿
                    first_touches_pivot = (first_low >= zd - tolerance and first_low <= zg + tolerance)
                    second_breaks_upper = second_high > zg
                    
                    if first_touches_pivot and second_breaks_upper:
                        # 找到第二类买点：第二笔的起点
                        buy_idx = second_bi[0]
                        buy_price = second_low
                        
                        # 计算信号强度 (初始为5分)
                        strength = 5
                        
                        # 加分项1: 回调深度合理（越接近中枢下沿越好）
                        if first_low >= zd:
                            strength += 2  # 未跌破下沿加2分
                        elif first_low >= zd - tolerance * 0.5:
                            strength += 1  # 轻微跌破加1分
                        
                        # 加分项2: MACD底背离或金叉
                        if 'macd' in df.columns and 'macd_signal' in df.columns:
                            # 检查是否有MACD金叉
                            if buy_idx > 0 and buy_idx < len(df) - 1:
                                if df['macd'].iloc[buy_idx-1] < df['macd_signal'].iloc[buy_idx-1] and \
                                   df['macd'].iloc[buy_idx] >= df['macd_signal'].iloc[buy_idx]:
                                    strength += 2  # MACD金叉加2分
                                
                                # 检查是否有底背离
                                if first_bi[1] < len(df) and buy_idx < len(df):
                                    price_lower = df['low'].iloc[buy_idx] < df['low'].iloc[first_bi[1]]
                                    macd_higher = df['macd'].iloc[buy_idx] > df['macd'].iloc[first_bi[1]]
                                    if price_lower and macd_higher:
                                        strength += 3  # 底背离加3分
                        
                        # 加分项3: DIF由负转正
                        if 'macd' in df.columns and buy_idx > 0:
                            if df['macd'].iloc[buy_idx-1] < 0 and df['macd'].iloc[buy_idx] >= 0:
                                strength += 2  # DIF由负转正加2分
                        
                        # 加分项4: 突破幅度
                        breakthrough_ratio = (second_high - zg) / pivot_height
                        if breakthrough_ratio > 0.2:
                            strength += min(int(breakthrough_ratio * 2), 2)  # 最多加2分
                            
                        # 将买点添加到列表
                        buy_points.append((buy_idx, buy_price, min(strength, 10), pivot_idx))
                
                # 第二类卖点检测：上涨笔反弹接近中枢区域，然后下跌笔跌破中枢下沿
                if first_direction == 'up' and second_direction == 'down':
                    # 修改后的条件：
                    # 1. 第一笔的高点接近或进入中枢区域（允许略微突破上沿）
                    # 2. 第二笔跌破中枢下沿
                    first_touches_pivot = (first_high <= zg + tolerance and first_high >= zd - tolerance)
                    second_breaks_lower = second_low < zd
                    
                    if first_touches_pivot and second_breaks_lower:
                        # 找到第二类卖点：第二笔的起点
                        sell_idx = second_bi[0]
                        sell_price = second_high
                        
                        # 计算信号强度 (初始为5分)
                        strength = 5
                        
                        # 加分项1: 反弹高度合理（越接近中枢上沿越好）
                        if first_high <= zg:
                            strength += 2  # 未突破上沿加2分
                        elif first_high <= zg + tolerance * 0.5:
                            strength += 1  # 轻微突破加1分
                        
                        # 加分项2: MACD顶背离或死叉
                        if 'macd' in df.columns and 'macd_signal' in df.columns:
                            # 检查是否有MACD死叉
                            if sell_idx > 0 and sell_idx < len(df) - 1:
                                if df['macd'].iloc[sell_idx-1] > df['macd_signal'].iloc[sell_idx-1] and \
                                   df['macd'].iloc[sell_idx] <= df['macd_signal'].iloc[sell_idx]:
                                    strength += 2  # MACD死叉加2分
                                
                                # 检查是否有顶背离
                                if first_bi[1] < len(df) and sell_idx < len(df):
                                    price_higher = df['high'].iloc[sell_idx] > df['high'].iloc[first_bi[1]]
                                    macd_lower = df['macd'].iloc[sell_idx] < df['macd'].iloc[first_bi[1]]
                                    if price_higher and macd_lower:
                                        strength += 3  # 顶背离加3分
                        
                        # 加分项3: DIF由正转负
                        if 'macd' in df.columns and sell_idx > 0:
                            if df['macd'].iloc[sell_idx-1] > 0 and df['macd'].iloc[sell_idx] <= 0:
                                strength += 2  # DIF由正转负加2分
                        
                        # 加分项4: 跌破幅度
                        breakdown_ratio = (zd - second_low) / pivot_height
                        if breakdown_ratio > 0.2:
                            strength += min(int(breakdown_ratio * 2), 2)  # 最多加2分
                        
                        # 将卖点添加到列表
                        sell_points.append((sell_idx, sell_price, min(strength, 10), pivot_idx))
        
        return buy_points, sell_points
    
    def identify_third_buy_sell_points(self, bi_list, df, pivot_zones=None):
        """
        识别第三类买卖点，结合中枢和MACD指标进行判断
        
        第三类买点(3B)条件:
        1. 在中枢上方运行，价格回调但未跌破中枢上沿
        2. 回调笔的低点不破前一个向下笔的低点（不创新低）
        3. MACD在零轴上方形成底背离或二次金叉
        
        第三类卖点(3S)条件:
        1. 在中枢下方运行，价格反弹但未突破中枢下沿
        2. 反弹笔的高点不破前一个向上笔的高点（不创新高）
        3. MACD在零轴下方形成顶背离或二次死叉
        
        参数:
            bi_list: 笔列表，每个笔为(start_idx, end_idx, start_val, end_val, direction)元组
            df: 数据DataFrame，需要包含MACD指标
            pivot_zones: 中枢列表，如果为None则自动计算
            
        返回:
            tuple: (买点列表, 卖点列表)，每个点为(index, price, strength, pivot_idx, reason)元组
        """
        if not bi_list or len(bi_list) < 4 or df is None or df.empty:
            return [], []
            
        # 如果未提供中枢，则计算中枢
        if pivot_zones is None:
            pivot_zones = self.identify_pivot_zones(bi_list, df)
            
        if not pivot_zones:
            return [], []
            
        buy_points = []  # 第三类买点
        sell_points = [] # 第三类卖点
        
        # 遍历每个中枢，寻找第三类买卖点
        for pivot_idx, (pivot_start_idx, pivot_end_idx, zg, zd, pivot_start_date, pivot_end_date) in enumerate(pivot_zones):
            # 查找中枢形成后的笔
            post_pivot_bi = [bi for bi in bi_list if bi[0] >= pivot_end_idx]
            
            if len(post_pivot_bi) < 3:
                continue  # 中枢后至少需要三笔才能形成第三类买卖点
            
            # 分析中枢后的笔序列，寻找第三类买卖点模式
            for i in range(len(post_pivot_bi) - 2):
                # 取连续三笔进行分析
                bi1 = post_pivot_bi[i]      # 第一笔
                bi2 = post_pivot_bi[i+1]    # 第二笔  
                bi3 = post_pivot_bi[i+2]    # 第三笔
                
                # 提取笔的价格和方向
                dir1, dir2, dir3 = bi1[4], bi2[4], bi3[4]
                
                if dir1 == 'up':
                    high1, low1 = bi1[3], bi1[2]
                else:
                    high1, low1 = bi1[2], bi1[3]
                    
                if dir2 == 'up':
                    high2, low2 = bi2[3], bi2[2]
                else:
                    high2, low2 = bi2[2], bi2[3]
                    
                if dir3 == 'up':
                    high3, low3 = bi3[3], bi3[2]
                else:
                    high3, low3 = bi3[2], bi3[3]
                
                # 第三类买点检测：上-下-上的模式，且在中枢上方
                if dir1 == 'up' and dir2 == 'down' and dir3 == 'up':
                    # 检查是否在中枢上方运行
                    if low1 > zg and low2 > zg and low3 > zg:
                        # 检查不创新低条件：第二笔的低点高于第一笔的低点
                        if low2 >= low1:
                            # 检查第三笔是否有效突破第二笔的高点
                            if high3 > high2:
                                # 找到第三类买点：第三笔的起点
                                buy_idx = bi3[0]
                                buy_price = low3
                                
                                # 计算信号强度 (初始为6分)
                                strength = 6
                                reason = "中枢上方不创新低"
                                
                                # 加分项1: 回调幅度适中
                                pullback_ratio = (high1 - low2) / (high1 - zg)
                                if 0.3 <= pullback_ratio <= 0.7:
                                    strength += 2  # 回调幅度合理加2分
                                
                                # 加分项2: MACD在零轴上方且形成底背离
                                if 'macd' in df.columns and 'macd_signal' in df.columns:
                                    if buy_idx < len(df) and bi2[1] < len(df):
                                        # 检查MACD是否在零轴上方
                                        if df['macd'].iloc[buy_idx] > 0:
                                            strength += 1  # MACD在零轴上方加1分
                                            
                                            # 检查是否有底背离
                                            price_higher = low2 >= low1  # 价格不创新低
                                            macd_higher = df['macd'].iloc[buy_idx] > df['macd'].iloc[bi2[1]]
                                            if price_higher and macd_higher:
                                                strength += 3  # 底背离加3分
                                                reason += "+MACD底背离"
                                        
                                        # 检查是否有二次金叉
                                        if buy_idx > 0:
                                            if df['macd'].iloc[buy_idx-1] < df['macd_signal'].iloc[buy_idx-1] and \
                                               df['macd'].iloc[buy_idx] >= df['macd_signal'].iloc[buy_idx]:
                                                strength += 2  # 二次金叉加2分
                                                reason += "+二次金叉"
                                
                                # 加分项3: 量能配合
                                if 'volume' in df.columns and buy_idx > 0:
                                    # 检查成交量是否放大
                                    if df['volume'].iloc[buy_idx] > df['volume'].iloc[buy_idx-1] * 1.2:
                                        strength += 1  # 量能放大加1分
                                
                                # 将买点添加到列表
                                buy_points.append((buy_idx, buy_price, min(strength, 10), pivot_idx, reason))
                
                # 第三类卖点检测：下-上-下的模式，且在中枢下方
                if dir1 == 'down' and dir2 == 'up' and dir3 == 'down':
                    # 检查是否在中枢下方运行
                    if high1 < zd and high2 < zd and high3 < zd:
                        # 检查不创新高条件：第二笔的高点低于第一笔的高点
                        if high2 <= high1:
                            # 检查第三笔是否有效跌破第二笔的低点
                            if low3 < low2:
                                # 找到第三类卖点：第三笔的起点
                                sell_idx = bi3[0]
                                sell_price = high3
                                
                                # 计算信号强度 (初始为6分)
                                strength = 6
                                reason = "中枢下方不创新高"
                                
                                # 加分项1: 反弹幅度适中
                                rebound_ratio = (high2 - low1) / (zd - low1)
                                if 0.3 <= rebound_ratio <= 0.7:
                                    strength += 2  # 反弹幅度合理加2分
                                
                                # 加分项2: MACD在零轴下方且形成顶背离
                                if 'macd' in df.columns and 'macd_signal' in df.columns:
                                    if sell_idx < len(df) and bi2[1] < len(df):
                                        # 检查MACD是否在零轴下方
                                        if df['macd'].iloc[sell_idx] < 0:
                                            strength += 1  # MACD在零轴下方加1分
                                            
                                            # 检查是否有顶背离
                                            price_lower = high2 <= high1  # 价格不创新高
                                            macd_lower = df['macd'].iloc[sell_idx] < df['macd'].iloc[bi2[1]]
                                            if price_lower and macd_lower:
                                                strength += 3  # 顶背离加3分
                                                reason += "+MACD顶背离"
                                        
                                        # 检查是否有二次死叉
                                        if sell_idx > 0:
                                            if df['macd'].iloc[sell_idx-1] > df['macd_signal'].iloc[sell_idx-1] and \
                                               df['macd'].iloc[sell_idx] <= df['macd_signal'].iloc[sell_idx]:
                                                strength += 2  # 二次死叉加2分
                                                reason += "+二次死叉"
                                
                                # 加分项3: 量能配合
                                if 'volume' in df.columns and sell_idx > 0:
                                    # 检查成交量是否放大
                                    if df['volume'].iloc[sell_idx] > df['volume'].iloc[sell_idx-1] * 1.2:
                                        strength += 1  # 量能放大加1分
                                
                                # 将卖点添加到列表
                                sell_points.append((sell_idx, sell_price, min(strength, 10), pivot_idx, reason))
        
        return buy_points, sell_points
    
    def plot_chan_analysis(self, df, symbol, timeframe, fx_list=None, bi_list=None, xd_list=None, 
                          show_ma=True, show_macd=True, show_pivots=True, show_line=True, 
                          show_buy_sell_points=True, show_second_buy_sell_points=True, 
                          show_third_buy_sell_points=True, save_path=None, title=None):
        """
        绘制带有缠论分析结果的K线图
        
        参数:
            df: 包含K线数据的DataFrame，必须包含'timestamp', 'open', 'high', 'low', 'close'列
            symbol: 交易对符号，如 'BTC/USDT'
            timeframe: 时间周期，如 '5m', '15m', '1h'
            fx_list: 分型列表，每个分型为(index, type, price)元组
            bi_list: 笔列表，每个笔为(start_idx, end_idx, start_val, end_val, direction)元组
            xd_list: 线段列表，每个线段为(start_idx, end_idx, start_val, end_val, direction)元组
            show_ma: 是否显示移动平均线
            show_macd: 是否显示MACD指标
            show_pivots: 是否显示中枢区域
            show_line: 是否显示价格线图而不是K线图
            show_buy_sell_points: 是否显示第一类买卖点
            show_second_buy_sell_points: 是否显示第二类买卖点
            show_third_buy_sell_points: 是否显示第三类买卖点
            save_path: 保存路径，如果为None则使用默认路径
            title: 图表标题，如果为None则使用默认标题
        
        返回:
            str: 图表保存路径
        """
        if df is None or df.empty:
            logger.error("无法绘制图表：数据为空")
            return None
        
        # 处理时间戳
        df = df.copy()
        if 'timestamp' in df.columns:
            if isinstance(df['timestamp'].iloc[0], str):
                df['timestamp'] = pd.to_datetime(df['timestamp'])
        else:
            df['timestamp'] = pd.to_datetime(df.index)
        
        # 为绘图准备数据
        ohlc_data = []
        for i, row in df.iterrows():
            date_val = mdates.date2num(row['timestamp'])
            ohlc_data.append([date_val, row['open'], row['high'], row['low'], row['close']])
        
        # 创建图表
        if show_macd:
            fig = plt.figure(figsize=(14, 10))
            gs = GridSpec(4, 1, figure=fig, height_ratios=[3, 1, 1, 1])
            ax1 = fig.add_subplot(gs[0:3, 0])
            ax2 = fig.add_subplot(gs[3, 0], sharex=ax1)
        else:
            fig, ax1 = plt.subplots(figsize=(14, 8))
            ax2 = None
        
        # 设置标题
        if title is None:
            title = "{} {} 缠论分析 - {}".format(symbol, timeframe, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        ax1.set_title(title, fontsize=14)
        
        # 绘制K线图或线图
        if show_line:
            # 绘制价格线图
            ax1.plot(mdates.date2num(df['timestamp']), df['close'], 
                    color=self.colors['price_line'], linewidth=1.5, label='价格')
        else:
            # 绘制K线图
            candlestick_ohlc(ax1, ohlc_data, width=0.6, 
                            colorup=self.colors['up_candle'], 
                            colordown=self.colors['down_candle'])
        
        # 识别中枢区域
        pivot_zones = []
        if show_pivots and bi_list:
            pivot_zones = self.identify_pivot_zones(bi_list, df)
            
            for i, (start_idx, end_idx, zg, zd, start_date, end_date) in enumerate(pivot_zones):
                # 转换日期为matplotlib格式
                start_date_num = mdates.date2num(start_date)
                end_date_num = mdates.date2num(end_date)
                
                # 绘制中枢区域为带边框的橙色矩形，类似参考图片
                rect = plt.Rectangle(
                    (start_date_num, zd),  # 左下角坐标
                    end_date_num - start_date_num,  # 宽度
                    zg - zd,  # 高度
                    facecolor=self.colors['pivot_zone'],
                    alpha=0.4,
                    edgecolor='#ffaa00',  # 橙色边框
                    linewidth=2,
                    zorder=3
                )
                ax1.add_patch(rect)
                
                # 标注中枢编号，使用数字标记，放在中枢中央
                pivot_num = i + 1  # 从1开始编号
                
                # 在中枢中心添加标注
                center_x = (start_date_num + end_date_num) / 2
                center_y = (zg + zd) / 2
                ax1.text(
                    center_x, center_y,
                    f"{pivot_num}",
                    ha='center', va='center',
                    fontsize=18, fontweight='bold',
                    color='white'
                )
        
        # 识别并绘制第一类买卖点
        if show_buy_sell_points and bi_list:
            buy_points, sell_points = self.identify_first_buy_sell_points(bi_list, df, pivot_zones)
            
            # 绘制买点
            for idx, price, strength in buy_points:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    
                    # 根据信号强度调整标记大小
                    marker_size = 80 + strength * 10
                    
                    # 买点使用绿色上三角
                    ax1.scatter(x, price, marker='^', color=self.colors['buy_point'], s=marker_size, zorder=6)
                    
                    # 添加买点标注
                    ax1.annotate(f"1B({strength})", (x, price), 
                                xytext=(0, -20), textcoords='offset points',
                                fontsize=9, fontweight='bold', ha='center', color=self.colors['buy_point'])
            
            # 绘制卖点
            for idx, price, strength in sell_points:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    
                    # 根据信号强度调整标记大小
                    marker_size = 80 + strength * 10
                    
                    # 卖点使用红色下三角
                    ax1.scatter(x, price, marker='v', color=self.colors['sell_point'], s=marker_size, zorder=6)
                    
                    # 添加卖点标注
                    ax1.annotate(f"1S({strength})", (x, price), 
                                xytext=(0, 20), textcoords='offset points',
                                fontsize=9, fontweight='bold', ha='center', color=self.colors['sell_point'])
        
        # 识别并绘制第二类买卖点
        if show_second_buy_sell_points and bi_list and pivot_zones:
            buy_points2, sell_points2 = self.identify_second_buy_sell_points(bi_list, df, pivot_zones)
            
            # 绘制第二类买点
            for idx, price, strength, pivot_idx in buy_points2:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    
                    # 根据信号强度调整标记大小
                    marker_size = 80 + strength * 10
                    
                    # 第二类买点使用紫色上三角
                    ax1.scatter(x, price, marker='^', color='#9900cc', s=marker_size, zorder=6)
                    
                    # 添加第二类买点标注，标注中包含对应的中枢编号
                    pivot_num = pivot_idx + 1
                    ax1.annotate(f"2B({strength})[中枢{pivot_num}]", (x, price), 
                                xytext=(0, -20), textcoords='offset points',
                                fontsize=9, fontweight='bold', ha='center', color='#9900cc')
            
            # 绘制第二类卖点
            for idx, price, strength, pivot_idx in sell_points2:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    
                    # 根据信号强度调整标记大小
                    marker_size = 80 + strength * 10
                    
                    # 第二类卖点使用橙色下三角
                    ax1.scatter(x, price, marker='v', color='#ff9900', s=marker_size, zorder=6)
                    
                    # 添加第二类卖点标注，标注中包含对应的中枢编号
                    pivot_num = pivot_idx + 1
                    ax1.annotate(f"2S({strength})[中枢{pivot_num}]", (x, price), 
                                xytext=(0, 20), textcoords='offset points',
                                fontsize=9, fontweight='bold', ha='center', color='#ff9900')
        
        # 识别并绘制第三类买卖点
        if show_third_buy_sell_points and bi_list and pivot_zones:
            buy_points3, sell_points3 = self.identify_third_buy_sell_points(bi_list, df, pivot_zones)
            
            # 绘制第三类买点
            for idx, price, strength, pivot_idx, reason in buy_points3:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    
                    # 根据信号强度调整标记大小
                    marker_size = 80 + strength * 10
                    
                    # 第三类买点使用青色上三角
                    ax1.scatter(x, price, marker='^', color='#00cccc', s=marker_size, zorder=6)
                    
                    # 添加第三类买点标注，标注中包含对应的中枢编号和原因
                    pivot_num = pivot_idx + 1
                    ax1.annotate(f"3B({strength})[中枢{pivot_num}]", (x, price), 
                                xytext=(0, -30), textcoords='offset points',
                                fontsize=9, fontweight='bold', ha='center', color='#00cccc')
                    
                    # 添加详细原因标注（较小字体）
                    ax1.annotate(reason, (x, price), 
                                xytext=(0, -45), textcoords='offset points',
                                fontsize=7, ha='center', color='#00cccc', alpha=0.8)
            
            # 绘制第三类卖点
            for idx, price, strength, pivot_idx, reason in sell_points3:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    
                    # 根据信号强度调整标记大小
                    marker_size = 80 + strength * 10
                    
                    # 第三类卖点使用黄色下三角
                    ax1.scatter(x, price, marker='v', color='#ffcc00', s=marker_size, zorder=6)
                    
                    # 添加第三类卖点标注，标注中包含对应的中枢编号和原因
                    pivot_num = pivot_idx + 1
                    ax1.annotate(f"3S({strength})[中枢{pivot_num}]", (x, price), 
                                xytext=(0, 30), textcoords='offset points',
                                fontsize=9, fontweight='bold', ha='center', color='#ffcc00')
                    
                    # 添加详细原因标注（较小字体）
                    ax1.annotate(reason, (x, price), 
                                xytext=(0, 45), textcoords='offset points',
                                fontsize=7, ha='center', color='#ffcc00', alpha=0.8)
        
        # 绘制移动平均线
        if show_ma and 'ma5' in df.columns and 'ma10' in df.columns and 'ma20' in df.columns:
            ax1.plot(mdates.date2num(df['timestamp']), df['ma5'], 
                    color=self.colors['ma5'], linewidth=1, label='MA5')
            ax1.plot(mdates.date2num(df['timestamp']), df['ma10'], 
                    color=self.colors['ma10'], linewidth=1, label='MA10')
            ax1.plot(mdates.date2num(df['timestamp']), df['ma20'], 
                    color=self.colors['ma20'], linewidth=1, label='MA20')
        
        # 绘制分型，使用红圈标记
        if fx_list:
            # 创建红圈标记
            for idx, fx_type, price in fx_list:
                if idx < len(df):
                    x = mdates.date2num(df['timestamp'].iloc[idx])
                    if fx_type == 'top':
                        # 顶分型使用红色圆圈标记
                        circle = plt.Circle((x, price), radius=0.0001, 
                                          transform=ax1.get_xaxis_transform(), 
                                          color='red', fill=False, linewidth=1.5,
                                          zorder=5)
                        ax1.add_patch(circle)
                    else:  # bottom
                        # 底分型使用红色圆圈标记
                        circle = plt.Circle((x, price), radius=0.0001, 
                                          transform=ax1.get_xaxis_transform(), 
                                          color='red', fill=False, linewidth=1.5,
                                          zorder=5)
                        ax1.add_patch(circle)
        
        # 绘制笔
        if bi_list:
            for start_idx, end_idx, start_val, end_val, direction in bi_list:
                if start_idx < len(df) and end_idx < len(df):
                    x_start = mdates.date2num(df['timestamp'].iloc[start_idx])
                    x_end = mdates.date2num(df['timestamp'].iloc[end_idx])
                    if direction == 'up':
                        color = self.colors['up_stroke']
                        label = '笔(上升)'
                    else:
                        color = self.colors['down_stroke']
                        label = '笔(下降)'
                    
                    # 仅为第一个元素添加图例标签，避免重复
                    if start_idx == bi_list[0][0] and end_idx == bi_list[0][1]:
                        ax1.plot([x_start, x_end], [start_val, end_val], 
                                color=color, linewidth=1.2, linestyle='-', label=label)
                    else:
                        ax1.plot([x_start, x_end], [start_val, end_val], 
                                color=color, linewidth=1.2, linestyle='-')
        
        # 绘制线段（主趋势线）
        if xd_list and len(xd_list) >= 2:
            # 首先绘制各个线段
            for start_idx, end_idx, start_val, end_val, direction in xd_list:
                if start_idx < len(df) and end_idx < len(df):
                    x_start = mdates.date2num(df['timestamp'].iloc[start_idx])
                    x_end = mdates.date2num(df['timestamp'].iloc[end_idx])
                    if direction == 'up':
                        color = self.colors['up_segment']
                        label = '线段(上升)'
                    else:
                        color = self.colors['down_segment']
                        label = '线段(下降)'
                    
                    # 仅为第一个元素添加图例标签
                    if start_idx == xd_list[0][0] and end_idx == xd_list[0][1]:
                        ax1.plot([x_start, x_end], [start_val, end_val], 
                                color=color, linewidth=2.0, linestyle='-', label=label)
                    else:
                        ax1.plot([x_start, x_end], [start_val, end_val], 
                                color=color, linewidth=2.0, linestyle='-')
            
            # 绘制主趋势线（连接第一个和最后一个线段的端点）
            first_seg = xd_list[0]
            last_seg = xd_list[-1]
            
            x_start = mdates.date2num(df['timestamp'].iloc[first_seg[0]])
            y_start = first_seg[2]  # 起始价格
            
            x_end = mdates.date2num(df['timestamp'].iloc[last_seg[1]])
            y_end = last_seg[3]  # 结束价格
            
            # 计算趋势方向
            trend_direction = 'up' if y_end > y_start else 'down'
            color = self.colors['trend_line']  # 红色
            
            # 绘制主趋势线
            ax1.plot([x_start, x_end], [y_start, y_end], 
                    color=color, linewidth=2.5, linestyle='-', 
                    label='主趋势线', zorder=4)
        
        # 绘制MACD
        if show_macd and ax2 is not None and 'macd' in df.columns and 'macd_signal' in df.columns and 'macd_diff' in df.columns:
            dates = mdates.date2num(df['timestamp'])
            ax2.plot(dates, df['macd'], color=self.colors['macd'], linewidth=1.5, label='MACD')
            ax2.plot(dates, df['macd_signal'], color=self.colors['signal'], linewidth=1.5, label='Signal')
            
            # 绘制MACD柱状图
            for i in range(len(df)):
                if i < len(df):
                    if df['macd_diff'].iloc[i] >= 0:
                        ax2.bar(dates[i], df['macd_diff'].iloc[i], width=0.6, color=self.colors['histogram_pos'])
                    else:
                        ax2.bar(dates[i], df['macd_diff'].iloc[i], width=0.6, color=self.colors['histogram_neg'])
            
            # 在MACD图表上也绘制趋势线，类似参考图片
            if xd_list and len(xd_list) >= 2:
                first_seg = xd_list[0]
                last_seg = xd_list[-1]
                
                # 获取MACD值范围作为趋势线的垂直位置基础
                macd_max = df['macd'].max()
                macd_min = df['macd'].min()
                macd_range = macd_max - macd_min
                
                # 取MACD范围的60%位置绘制趋势线
                macd_level = macd_max - 0.4 * macd_range
                
                x_start = mdates.date2num(df['timestamp'].iloc[first_seg[0]])
                x_end = mdates.date2num(df['timestamp'].iloc[last_seg[1]])
                
                # 绘制水平趋势线
                ax2.plot([x_start, x_end], [macd_level, macd_level], 
                        color=color, linewidth=2.0, linestyle='-', zorder=4)
            
            ax2.axhline(y=0, color='gray', linestyle='-', linewidth=0.5)
            ax2.set_ylabel('MACD', fontsize=10)
            ax2.legend(loc='upper left', fontsize=8)
        
        # 设置x轴格式
        if ax2 is not None:
            plt.setp(ax1.get_xticklabels(), visible=False)  # 隐藏上图的x轴标签
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            fig.autofmt_xdate()  # 自动格式化x轴日期
        else:
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            fig.autofmt_xdate()
        
        # 添加网格和图例
        ax1.grid(True, linestyle=':', alpha=0.7)
        ax1.set_ylabel('价格', fontsize=12)
        ax1.legend(loc='upper left', fontsize=10)
        
        # 添加最新价格标注
        last_price = df['close'].iloc[-1]
        ax1.axhline(y=last_price, color='red', linestyle='--', alpha=0.5)
        ax1.text(mdates.date2num(df['timestamp'].iloc[-1]), last_price, 
                f' 最新: {last_price:.2f}', verticalalignment='bottom', 
                horizontalalignment='right', color='red', fontsize=10)
        
        # 优化布局
        plt.tight_layout()
        
        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            symbol_str = symbol.replace('/', '_')
            save_path = os.path.join(self.save_dir, f"{symbol_str}_{timeframe}_{timestamp}.png")
        
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"缠论分析图表已保存: {save_path}")
        return save_path

    def plot_multi_timeframe(self, data_dict, symbol, fx_dict=None, bi_dict=None, xd_dict=None, 
                            show_pivots=True, show_line=True, show_buy_sell_points=True, 
                            add_analysis_text=True, save_path=None, title=None):
        """
        绘制多时间周期的缠论分析图表
        
        参数:
            data_dict: 包含多个时间周期K线数据的字典，格式为 {timeframe: df}
            symbol: 交易对符号
            fx_dict: 包含多个时间周期分型的字典，格式为 {timeframe: fx_list}
            bi_dict: 包含多个时间周期笔的字典，格式为 {timeframe: bi_list}
            xd_dict: 包含多个时间周期线段的字典，格式为 {timeframe: xd_list}
            show_pivots: 是否显示中枢区域
            show_line: 是否显示价格线图而不是K线图
            show_buy_sell_points: 是否显示第一类买卖点
            add_analysis_text: 是否添加分析文字说明
            save_path: 保存路径
            title: 图表标题
        
        返回:
            str: 图表保存路径
        """
        if not data_dict:
            logger.error("无法绘制多时间周期图表：数据为空")
            return None
        
        # 确定时间周期顺序 (从小到大)
        timeframe_order = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
        timeframes = [tf for tf in timeframe_order if tf in data_dict]
        
        # 如果没有找到有效的时间周期，使用字典的键
        if not timeframes:
            timeframes = list(data_dict.keys())
        
        n_timeframes = len(timeframes)
        if n_timeframes == 0:
            return None
        
        # 创建图表
        fig = plt.figure(figsize=(16, 6 * n_timeframes))
        
        # 计算网格的行数和高度比例
        n_rows = n_timeframes * 2  # 每个时间周期有2行（K线图和MACD）
        height_ratios = []
        for _ in range(n_timeframes):
            height_ratios.extend([3, 1])  # K线图占3份高度，MACD占1份高度
        
        # 创建网格布局
        gs = GridSpec(n_rows, 1, figure=fig, height_ratios=height_ratios)
        
        if title is None:
            title = "{} {} 缠论分析 - {}".format(symbol, timeframe, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        fig.suptitle(title, fontsize=16)
        
        for i, tf in enumerate(timeframes):
            df = data_dict[tf]
            
            # 跳过空数据
            if df is None or df.empty:
                continue
            
            # 处理时间戳
            df = df.copy()
            if 'timestamp' in df.columns:
                if isinstance(df['timestamp'].iloc[0], str):
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
            else:
                df['timestamp'] = pd.to_datetime(df.index)
            
            # 为绘图准备数据
            ohlc_data = []
            for _, row in df.iterrows():
                date_val = mdates.date2num(row['timestamp'])
                ohlc_data.append([date_val, row['open'], row['high'], row['low'], row['close']])
            
            # 创建K线子图和MACD子图（注意这里的索引是基于2*i，而不是4*i）
            ax_candle = fig.add_subplot(gs[i*2, 0])
            ax_macd = fig.add_subplot(gs[i*2+1, 0], sharex=ax_candle)
            
            # 设置标题
            ax_candle.set_title(f"{tf} 周期", fontsize=12)
            
            # 绘制K线图或线图
            if show_line:
                # 绘制价格线图
                ax_candle.plot(mdates.date2num(df['timestamp']), df['close'], 
                                color=self.colors['price_line'], linewidth=1.5, label='价格')
            else:
                # 绘制K线图
                candlestick_ohlc(ax_candle, ohlc_data, width=0.6, 
                                colorup=self.colors['up_candle'], 
                                colordown=self.colors['down_candle'])
            
            # 识别和绘制中枢区域
            pivot_zones = []
            if show_pivots and bi_dict and tf in bi_dict:
                bi_list = bi_dict[tf]
                pivot_zones = self.identify_pivot_zones(bi_list, df)
                
                for j, (start_idx, end_idx, zg, zd, start_date, end_date) in enumerate(pivot_zones):
                    # 转换日期为matplotlib格式
                    start_date_num = mdates.date2num(start_date)
                    end_date_num = mdates.date2num(end_date)
                    
                    # 绘制中枢区域为带边框的橙色矩形，类似参考图片
                    rect = plt.Rectangle(
                        (start_date_num, zd),  # 左下角坐标
                        end_date_num - start_date_num,  # 宽度
                        zg - zd,  # 高度
                        facecolor=self.colors['pivot_zone'],
                        alpha=0.4,
                        edgecolor='#ffaa00',  # 橙色边框
                        linewidth=2,
                        zorder=3
                    )
                    ax_candle.add_patch(rect)
                    
                    # 标注中枢编号，使用数字标记，放在中枢中央
                    pivot_num = j + 1  # 从1开始编号
                    
                    # 在中枢中心添加标注
                    center_x = (start_date_num + end_date_num) / 2
                    center_y = (zg + zd) / 2
                    ax_candle.text(
                        center_x, center_y,
                        f"{pivot_num}",
                        ha='center', va='center',
                        fontsize=18, fontweight='bold',
                        color='white'
                    )
            
            # 识别并绘制第一类买卖点
            if show_buy_sell_points and bi_dict and tf in bi_dict:
                bi_list = bi_dict[tf]
                buy_points, sell_points = self.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                
                # 绘制买点
                for idx, price, strength in buy_points:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 买点使用绿色上三角
                        ax_candle.scatter(x, price, marker='^', color=self.colors['buy_point'], s=marker_size, zorder=6)
                        
                        # 添加买点标注
                        ax_candle.annotate(f"1B({strength})", (x, price), 
                                        xytext=(0, -20), textcoords='offset points',
                                        fontsize=9, fontweight='bold', ha='center', color=self.colors['buy_point'])
            
                # 绘制卖点
                for idx, price, strength in sell_points:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 卖点使用红色下三角
                        ax_candle.scatter(x, price, marker='v', color=self.colors['sell_point'], s=marker_size, zorder=6)
                        
                        # 添加卖点标注
                        ax_candle.annotate(f"1S({strength})", (x, price), 
                                        xytext=(0, 20), textcoords='offset points',
                                        fontsize=9, fontweight='bold', ha='center', color=self.colors['sell_point'])
            
            # 绘制移动平均线
            if 'ma5' in df.columns and 'ma10' in df.columns and 'ma20' in df.columns:
                ax_candle.plot(mdates.date2num(df['timestamp']), df['ma5'], 
                             color=self.colors['ma5'], linewidth=1, label='MA5')
                ax_candle.plot(mdates.date2num(df['timestamp']), df['ma10'], 
                             color=self.colors['ma10'], linewidth=1, label='MA10')
                ax_candle.plot(mdates.date2num(df['timestamp']), df['ma20'], 
                             color=self.colors['ma20'], linewidth=1, label='MA20')
            
            # 绘制分型
            if fx_dict and tf in fx_dict:
                fx_list = fx_dict[tf]
                # 创建红圈标记
                for idx, fx_type, price in fx_list:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        if fx_type == 'top':
                            # 顶分型使用红色圆圈标记
                            circle = plt.Circle((x, price), radius=0.0001, 
                                              transform=ax_candle.get_xaxis_transform(), 
                                              color='red', fill=False, linewidth=1.5,
                                              zorder=5)
                            ax_candle.add_patch(circle)
                        else:  # bottom
                            # 底分型使用红色圆圈标记
                            circle = plt.Circle((x, price), radius=0.0001, 
                                              transform=ax_candle.get_xaxis_transform(), 
                                              color='red', fill=False, linewidth=1.5,
                                              zorder=5)
                            ax_candle.add_patch(circle)
            
            # 绘制笔
            if bi_dict and tf in bi_dict:
                bi_list = bi_dict[tf]
                for start_idx, end_idx, start_val, end_val, direction in bi_list:
                    if start_idx < len(df) and end_idx < len(df):
                        x_start = mdates.date2num(df['timestamp'].iloc[start_idx])
                        x_end = mdates.date2num(df['timestamp'].iloc[end_idx])
                        if direction == 'up':
                            color = self.colors['up_stroke']
                            label = '笔(上升)'
                        else:
                            color = self.colors['down_stroke']
                            label = '笔(下降)'
                        
                        # 仅为第一个元素添加图例标签
                        if start_idx == bi_list[0][0] and end_idx == bi_list[0][1]:
                            ax_candle.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=1.2, linestyle='-', label=label)
                        else:
                            ax_candle.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=1.2, linestyle='-')
            
            # 绘制线段和趋势线
            if xd_dict and tf in xd_dict and len(xd_dict[tf]) >= 2:
                xd_list = xd_dict[tf]
                # 绘制各个线段
                for start_idx, end_idx, start_val, end_val, direction in xd_list:
                    if start_idx < len(df) and end_idx < len(df):
                        x_start = mdates.date2num(df['timestamp'].iloc[start_idx])
                        x_end = mdates.date2num(df['timestamp'].iloc[end_idx])
                        if direction == 'up':
                            color = self.colors['up_segment']
                            label = '线段(上升)'
                        else:
                            color = self.colors['down_segment']
                            label = '线段(下降)'
                        
                        # 仅为第一个元素添加图例标签
                        if start_idx == xd_list[0][0] and end_idx == xd_list[0][1]:
                            ax_candle.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=2.0, linestyle='-', label=label)
                        else:
                            ax_candle.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=2.0, linestyle='-')
                
                # 绘制趋势线
                first_seg = xd_list[0]
                last_seg = xd_list[-1]
                
                x_start = mdates.date2num(df['timestamp'].iloc[first_seg[0]])
                y_start = first_seg[2]  # 起始价格
                
                x_end = mdates.date2num(df['timestamp'].iloc[last_seg[1]])
                y_end = last_seg[3]  # 结束价格
                
                # 趋势线颜色
                color = self.colors['trend_line']  # 红色
                
                # 绘制主趋势线
                ax_candle.plot([x_start, x_end], [y_start, y_end], 
                             color=color, linewidth=2.5, linestyle='-', 
                             label='主趋势线', zorder=4)
            
            # 添加分析文字标注
            if add_analysis_text and pivot_zones and len(pivot_zones) >= 2:
                # 获取图表区域尺寸
                x_min, x_max = ax_candle.get_xlim()
                y_min, y_max = ax_candle.get_ylim()
                
                # 在图表底部添加分析文字
                text_x = x_min + (x_max - x_min) * 0.5  # 水平居中
                text_y = y_min + (y_max - y_min) * 0.1  # 靠近底部
                
                # 构建分析文字
                pivot_count = len(pivot_zones)
                analysis_text = f"{pivot_count}个黄色中枢上下重叠，形成趋势通道。"
                
                # 判断是否有买卖点
                if show_buy_sell_points and bi_dict and tf in bi_dict:
                    bi_list = bi_dict[tf]
                    buy_points, sell_points = self.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                    
                    if buy_points:
                        # 查找最高强度的买点
                        best_buy = max(buy_points, key=lambda x: x[2])
                        analysis_text += f" 蓝色圆圈处有强度为{best_buy[2]}的第一类买点。"
                    
                    if sell_points:
                        # 查找最高强度的卖点
                        best_sell = max(sell_points, key=lambda x: x[2])
                        analysis_text += f" 红色圆圈处有强度为{best_sell[2]}的第一类卖点。"
                
                # 添加MACD相关分析
                if 'macd' in df.columns and 'macd_signal' in df.columns:
                    # 检查MACD是否有金叉或死叉
                    last_idx = len(df) - 1
                    if last_idx > 0:
                        if df['macd'].iloc[last_idx] > df['macd_signal'].iloc[last_idx] and \
                           df['macd'].iloc[last_idx-1] <= df['macd_signal'].iloc[last_idx-1]:
                            analysis_text += " MACD金叉形成，看涨信号。"
                        elif df['macd'].iloc[last_idx] < df['macd_signal'].iloc[last_idx] and \
                             df['macd'].iloc[last_idx-1] >= df['macd_signal'].iloc[last_idx-1]:
                            analysis_text += " MACD死叉形成，看跌信号。"
                
                # 绘制分析文字，使用半透明背景
                ax_candle.text(text_x, text_y, analysis_text, 
                              ha='center', va='center', fontsize=10, 
                              color='white', fontweight='bold',
                              bbox=dict(facecolor='#333333', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.5'))
            
            # 绘制MACD
            if 'macd' in df.columns and 'macd_signal' in df.columns and 'macd_diff' in df.columns:
                dates = mdates.date2num(df['timestamp'])
                ax_macd.plot(dates, df['macd'], color=self.colors['macd'], linewidth=1.5, label='MACD')
                ax_macd.plot(dates, df['macd_signal'], color=self.colors['signal'], linewidth=1.5, label='Signal')
                
                # 绘制MACD柱状图
                for j in range(len(df)):
                    if j < len(df):
                        if df['macd_diff'].iloc[j] >= 0:
                            ax_macd.bar(dates[j], df['macd_diff'].iloc[j], width=0.6, color=self.colors['histogram_pos'])
                        else:
                            ax_macd.bar(dates[j], df['macd_diff'].iloc[j], width=0.6, color=self.colors['histogram_neg'])
                
                # 在MACD图表上也绘制趋势线
                if xd_dict and tf in xd_dict and len(xd_dict[tf]) >= 2:
                    xd_list = xd_dict[tf]
                    first_seg = xd_list[0]
                    last_seg = xd_list[-1]
                    
                    # 获取MACD值范围作为趋势线的垂直位置基础
                    macd_max = df['macd'].max()
                    macd_min = df['macd'].min()
                    macd_range = macd_max - macd_min
                    
                    # 取MACD范围的60%位置绘制趋势线
                    macd_level = macd_max - 0.4 * macd_range
                    
                    x_start = mdates.date2num(df['timestamp'].iloc[first_seg[0]])
                    x_end = mdates.date2num(df['timestamp'].iloc[last_seg[1]])
                    
                    # 绘制水平趋势线
                    ax_macd.plot([x_start, x_end], [macd_level, macd_level], 
                               color=self.colors['trend_line'], linewidth=2.0, linestyle='-', zorder=4)
                
                ax_macd.axhline(y=0, color='gray', linestyle='-', linewidth=0.5)
                ax_macd.set_ylabel('MACD', fontsize=10)
                ax_macd.legend(loc='upper left', fontsize=8)
            
            # 设置x轴格式
            if i == n_timeframes - 1:  # 仅对最后一个图表显示详细的x轴标签
                ax_macd.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            else:
                plt.setp(ax_macd.get_xticklabels(), visible=False)
            
            # 添加网格和图例
            ax_candle.grid(True, linestyle=':', alpha=0.7)
            ax_candle.set_ylabel('价格', fontsize=12)
            ax_candle.legend(loc='upper left', fontsize=9)
            
            # 添加最新价格标注
            last_price = df['close'].iloc[-1]
            ax_candle.axhline(y=last_price, color='red', linestyle='--', alpha=0.5)
            ax_candle.text(mdates.date2num(df['timestamp'].iloc[-1]), last_price, 
                         f' 最新: {last_price:.2f}', verticalalignment='bottom', 
                         horizontalalignment='right', color='red', fontsize=9)
        
        # 优化布局
        plt.tight_layout()
        fig.subplots_adjust(top=0.95)  # 为主标题留出空间
        
        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            symbol_str = symbol.replace('/', '_')
            save_path = os.path.join(self.save_dir, f"{symbol_str}_multi_timeframe_{timestamp}.png")
        
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"多时间周期缠论分析图表已保存: {save_path}")
        return save_path

    def plot_chan_analysis_multi(self, symbol, data_dict, fx_lists, bi_lists, xd_lists=None,
                                 show_ma=True, show_macd=True, show_pivots=True, show_line=True,
                                 show_buy_sell_points=True, show_second_buy_sell_points=True, 
                                 save_path=None, title=None, num_rows=None):
        """
        绘制多个时间周期的缠论分析结果
        
        参数:
            symbol: 交易对符号，如 'BTC/USDT'
            data_dict: 字典，键为时间周期（如'5m'），值为对应的DataFrame
            fx_lists: 字典，键为时间周期，值为分型列表
            bi_lists: 字典，键为时间周期，值为笔列表
            xd_lists: 字典，键为时间周期，值为线段列表
            show_ma: 是否显示移动平均线
            show_macd: 是否显示MACD指标
            show_pivots: 是否显示中枢区域
            show_line: 是否显示价格线图而不是K线图
            show_buy_sell_points: 是否显示第一类买卖点
            show_second_buy_sell_points: 是否显示第二类买卖点
            save_path: 保存路径，如果为None则使用默认路径
            title: 图表标题，如果为None则使用默认标题
            num_rows: 每行显示的子图数量，默认为1
            
        返回:
            str: 图表保存路径
        """
        if not data_dict:
            logger.error("无法绘制多周期图表：数据为空")
            return None
        
        # 确定子图排列
        num_timeframes = len(data_dict)
        if num_rows is None:
            if num_timeframes <= 2:
                num_rows = 1
            else:
                num_rows = 2
        
        num_cols = math.ceil(num_timeframes / num_rows)
        
        # 创建图表
        if show_macd:
            fig = plt.figure(figsize=(14 * num_cols, 10 * num_rows))
        else:
            fig = plt.figure(figsize=(14 * num_cols, 8 * num_rows))
        
        # 设置标题
        if title is None:
            title = "{} {} 缠论分析 - {}".format(symbol, timeframe, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        fig.suptitle(title, fontsize=16)
        
        # 按时间周期排序
        tf_order = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '8h', '12h', '1d', '3d', '1w', '1M']
        timeframes = sorted(data_dict.keys(), key=lambda x: tf_order.index(x) if x in tf_order else 999)
        
        # 绘制每个时间周期的子图
        for i, tf in enumerate(timeframes):
            df = data_dict[tf]
            fx_list = fx_lists.get(tf, [])
            bi_list = bi_lists.get(tf, [])
            xd_list = xd_lists.get(tf, []) if xd_lists else []
            
            # 创建子图
            if show_macd:
                gs = fig.add_gridspec(4, num_cols, height_ratios=[3, 1, 1, 1], 
                                    top=0.95, bottom=0.05, left=0.05, right=0.95, hspace=0.05, wspace=0.15)
                row_idx = i // num_cols
                col_idx = i % num_cols
                ax1 = fig.add_subplot(gs[4*row_idx:4*row_idx+3, col_idx])
                ax2 = fig.add_subplot(gs[4*row_idx+3, col_idx], sharex=ax1)
            else:
                ax1 = fig.add_subplot(num_rows, num_cols, i+1)
                ax2 = None
            
            # 设置子图标题
            ax1.set_title(f"{symbol} {tf} 缠论分析", fontsize=12)
            
            # 处理时间戳
            df = df.copy()
            if 'timestamp' in df.columns:
                if isinstance(df['timestamp'].iloc[0], str):
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
            else:
                df['timestamp'] = pd.to_datetime(df.index)
            
            # 为绘图准备数据
            ohlc_data = []
            for j, row in df.iterrows():
                date_val = mdates.date2num(row['timestamp'])
                ohlc_data.append([date_val, row['open'], row['high'], row['low'], row['close']])
            
            # 绘制K线图或线图
            if show_line:
                # 绘制价格线图
                ax1.plot(mdates.date2num(df['timestamp']), df['close'], 
                        color=self.colors['price_line'], linewidth=1.5, label='价格')
            else:
                # 绘制K线图
                candlestick_ohlc(ax1, ohlc_data, width=0.6, 
                                colorup=self.colors['up_candle'], 
                                colordown=self.colors['down_candle'])
            
            # 识别中枢区域
            pivot_zones = []
            if show_pivots and bi_list:
                pivot_zones = self.identify_pivot_zones(bi_list, df)
                
                for j, (start_idx, end_idx, zg, zd, start_date, end_date) in enumerate(pivot_zones):
                    # 转换日期为matplotlib格式
                    start_date_num = mdates.date2num(start_date)
                    end_date_num = mdates.date2num(end_date)
                    
                    # 绘制中枢区域为带边框的橙色矩形
                    rect = plt.Rectangle(
                        (start_date_num, zd),  # 左下角坐标
                        end_date_num - start_date_num,  # 宽度
                        zg - zd,  # 高度
                        facecolor=self.colors['pivot_zone'],
                        alpha=0.4,
                        edgecolor='#ffaa00',  # 橙色边框
                        linewidth=2,
                        zorder=3
                    )
                    ax1.add_patch(rect)
                    
                    # 标注中枢编号
                    pivot_num = j + 1  # 从1开始编号
                    center_x = (start_date_num + end_date_num) / 2
                    center_y = (zg + zd) / 2
                    ax1.text(
                        center_x, center_y,
                        f"{pivot_num}",
                        ha='center', va='center',
                        fontsize=16, fontweight='bold',
                        color='white'
                    )
            
            # 识别并绘制第一类买卖点
            if show_buy_sell_points and bi_list:
                buy_points, sell_points = self.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                
                # 绘制买点
                for idx, price, strength in buy_points:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 买点使用绿色上三角
                        ax1.scatter(x, price, marker='^', color=self.colors['buy_point'], s=marker_size, zorder=6)
                        
                        # 添加买点标注
                        ax1.annotate(f"1B({strength})", (x, price), 
                                    xytext=(0, -20), textcoords='offset points',
                                    fontsize=9, fontweight='bold', ha='center', color=self.colors['buy_point'])
                
                # 绘制卖点
                for idx, price, strength in sell_points:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 卖点使用红色下三角
                        ax1.scatter(x, price, marker='v', color=self.colors['sell_point'], s=marker_size, zorder=6)
                        
                        # 添加卖点标注
                        ax1.annotate(f"1S({strength})", (x, price), 
                                    xytext=(0, 20), textcoords='offset points',
                                    fontsize=9, fontweight='bold', ha='center', color=self.colors['sell_point'])
            
            # 识别并绘制第二类买卖点
            if show_second_buy_sell_points and bi_list and pivot_zones:
                buy_points2, sell_points2 = self.identify_second_buy_sell_points(bi_list, df, pivot_zones)
                
                # 绘制第二类买点
                for idx, price, strength, pivot_idx in buy_points2:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 第二类买点使用紫色上三角
                        ax1.scatter(x, price, marker='^', color='#9900cc', s=marker_size, zorder=6)
                        
                        # 添加第二类买点标注，标注中包含对应的中枢编号
                        pivot_num = pivot_idx + 1
                        ax1.annotate(f"2B({strength})[中枢{pivot_num}]", (x, price), 
                                    xytext=(0, -20), textcoords='offset points',
                                    fontsize=9, fontweight='bold', ha='center', color='#9900cc')
                
                # 绘制第二类卖点
                for idx, price, strength, pivot_idx in sell_points2:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 第二类卖点使用橙色下三角
                        ax1.scatter(x, price, marker='v', color='#ff9900', s=marker_size, zorder=6)
                        
                        # 添加第二类卖点标注，标注中包含对应的中枢编号
                        pivot_num = pivot_idx + 1
                        ax1.annotate(f"2S({strength})[中枢{pivot_num}]", (x, price), 
                                    xytext=(0, 20), textcoords='offset points',
                                    fontsize=9, fontweight='bold', ha='center', color='#ff9900')
            
            # 识别并绘制第三类买卖点
            if show_third_buy_sell_points and bi_list and pivot_zones:
                buy_points3, sell_points3 = self.identify_third_buy_sell_points(bi_list, df, pivot_zones)
                
                # 绘制第三类买点
                for idx, price, strength, pivot_idx, reason in buy_points3:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 第三类买点使用青色上三角
                        ax1.scatter(x, price, marker='^', color='#00cccc', s=marker_size, zorder=6)
                        
                        # 添加第三类买点标注，标注中包含对应的中枢编号和原因
                        pivot_num = pivot_idx + 1
                        ax1.annotate(f"3B({strength})[中枢{pivot_num}]", (x, price), 
                                    xytext=(0, -30), textcoords='offset points',
                                    fontsize=9, fontweight='bold', ha='center', color='#00cccc')
                        
                        # 添加详细原因标注（较小字体）
                        ax1.annotate(reason, (x, price), 
                                    xytext=(0, -45), textcoords='offset points',
                                    fontsize=7, ha='center', color='#00cccc', alpha=0.8)
                
                # 绘制第三类卖点
                for idx, price, strength, pivot_idx, reason in sell_points3:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        
                        # 根据信号强度调整标记大小
                        marker_size = 80 + strength * 10
                        
                        # 第三类卖点使用黄色下三角
                        ax1.scatter(x, price, marker='v', color='#ffcc00', s=marker_size, zorder=6)
                        
                        # 添加第三类卖点标注，标注中包含对应的中枢编号和原因
                        pivot_num = pivot_idx + 1
                        ax1.annotate(f"3S({strength})[中枢{pivot_num}]", (x, price), 
                                    xytext=(0, 30), textcoords='offset points',
                                    fontsize=9, fontweight='bold', ha='center', color='#ffcc00')
                        
                        # 添加详细原因标注（较小字体）
                        ax1.annotate(reason, (x, price), 
                                    xytext=(0, 45), textcoords='offset points',
                                    fontsize=7, ha='center', color='#ffcc00', alpha=0.8)
            
            # 绘制移动平均线
            if show_ma and 'ma5' in df.columns and 'ma10' in df.columns and 'ma20' in df.columns:
                ax1.plot(mdates.date2num(df['timestamp']), df['ma5'], 
                        color=self.colors['ma5'], linewidth=1, label='MA5')
                ax1.plot(mdates.date2num(df['timestamp']), df['ma10'], 
                        color=self.colors['ma10'], linewidth=1, label='MA10')
                ax1.plot(mdates.date2num(df['timestamp']), df['ma20'], 
                        color=self.colors['ma20'], linewidth=1, label='MA20')
            
            # 绘制分型
            if fx_list:
                # 创建红圈标记
                for idx, fx_type, price in fx_list:
                    if idx < len(df):
                        x = mdates.date2num(df['timestamp'].iloc[idx])
                        if fx_type == 'top':
                            # 顶分型使用红色圆圈标记
                            circle = plt.Circle((x, price), radius=0.0001, 
                                              transform=ax1.get_xaxis_transform(), 
                                              color='red', fill=False, linewidth=1.5,
                                              zorder=5)
                            ax1.add_patch(circle)
                        else:  # bottom
                            # 底分型使用红色圆圈标记
                            circle = plt.Circle((x, price), radius=0.0001, 
                                              transform=ax1.get_xaxis_transform(), 
                                              color='red', fill=False, linewidth=1.5,
                                              zorder=5)
                            ax1.add_patch(circle)
            
            # 绘制笔
            if bi_list:
                for start_idx, end_idx, start_val, end_val, direction in bi_list:
                    if start_idx < len(df) and end_idx < len(df):
                        x_start = mdates.date2num(df['timestamp'].iloc[start_idx])
                        x_end = mdates.date2num(df['timestamp'].iloc[end_idx])
                        if direction == 'up':
                            color = self.colors['up_stroke']
                            label = '笔(上升)'
                        else:
                            color = self.colors['down_stroke']
                            label = '笔(下降)'
                        
                        # 仅为第一个元素添加图例标签
                        if start_idx == bi_list[0][0] and end_idx == bi_list[0][1]:
                            ax1.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=1.2, linestyle='-', label=label)
                        else:
                            ax1.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=1.2, linestyle='-')
            
            # 绘制线段和趋势线
            if xd_list and len(xd_list) >= 2:
                # 绘制各个线段
                for start_idx, end_idx, start_val, end_val, direction in xd_list:
                    if start_idx < len(df) and end_idx < len(df):
                        x_start = mdates.date2num(df['timestamp'].iloc[start_idx])
                        x_end = mdates.date2num(df['timestamp'].iloc[end_idx])
                        if direction == 'up':
                            color = self.colors['up_segment']
                            label = '线段(上升)'
                        else:
                            color = self.colors['down_segment']
                            label = '线段(下降)'
                        
                        # 仅为第一个元素添加图例标签
                        if start_idx == xd_list[0][0] and end_idx == xd_list[0][1]:
                            ax1.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=2.0, linestyle='-', label=label)
                        else:
                            ax1.plot([x_start, x_end], [start_val, end_val], 
                                        color=color, linewidth=2.0, linestyle='-')
                
                # 绘制趋势线
                first_seg = xd_list[0]
                last_seg = xd_list[-1]
                
                x_start = mdates.date2num(df['timestamp'].iloc[first_seg[0]])
                y_start = first_seg[2]  # 起始价格
                
                x_end = mdates.date2num(df['timestamp'].iloc[last_seg[1]])
                y_end = last_seg[3]  # 结束价格
                
                # 趋势线颜色
                color = self.colors['trend_line']  # 红色
                
                # 绘制主趋势线
                ax1.plot([x_start, x_end], [y_start, y_end], 
                             color=color, linewidth=2.5, linestyle='-', 
                             label='主趋势线', zorder=4)
            
            # 添加分析文字标注
            if add_analysis_text and pivot_zones and len(pivot_zones) >= 2:
                # 获取图表区域尺寸
                x_min, x_max = ax1.get_xlim()
                y_min, y_max = ax1.get_ylim()
                
                # 在图表底部添加分析文字
                text_x = x_min + (x_max - x_min) * 0.5  # 水平居中
                text_y = y_min + (y_max - y_min) * 0.1  # 靠近底部
                
                # 构建分析文字
                pivot_count = len(pivot_zones)
                analysis_text = f"{pivot_count}个黄色中枢上下重叠，形成趋势通道。"
                
                # 判断是否有买卖点
                if show_buy_sell_points and bi_list:
                    bi_list = bi_list
                    buy_points, sell_points = self.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                    
                    if buy_points:
                        # 查找最高强度的买点
                        best_buy = max(buy_points, key=lambda x: x[2])
                        analysis_text += f" 蓝色圆圈处有强度为{best_buy[2]}的第一类买点。"
                    
                    if sell_points:
                        # 查找最高强度的卖点
                        best_sell = max(sell_points, key=lambda x: x[2])
                        analysis_text += f" 红色圆圈处有强度为{best_sell[2]}的第一类卖点。"
                
                # 添加MACD相关分析
                if 'macd' in df.columns and 'macd_signal' in df.columns:
                    # 检查MACD是否有金叉或死叉
                    last_idx = len(df) - 1
                    if last_idx > 0:
                        if df['macd'].iloc[last_idx] > df['macd_signal'].iloc[last_idx] and \
                           df['macd'].iloc[last_idx-1] <= df['macd_signal'].iloc[last_idx-1]:
                            analysis_text += " MACD金叉形成，看涨信号。"
                        elif df['macd'].iloc[last_idx] < df['macd_signal'].iloc[last_idx] and \
                             df['macd'].iloc[last_idx-1] >= df['macd_signal'].iloc[last_idx-1]:
                            analysis_text += " MACD死叉形成，看跌信号。"
                
                # 绘制分析文字，使用半透明背景
                ax1.text(text_x, text_y, analysis_text, 
                              ha='center', va='center', fontsize=10, 
                              color='white', fontweight='bold',
                              bbox=dict(facecolor='#333333', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.5'))
            
            # 绘制MACD
            if 'macd' in df.columns and 'macd_signal' in df.columns and 'macd_diff' in df.columns:
                dates = mdates.date2num(df['timestamp'])
                ax_macd.plot(dates, df['macd'], color=self.colors['macd'], linewidth=1.5, label='MACD')
                ax_macd.plot(dates, df['macd_signal'], color=self.colors['signal'], linewidth=1.5, label='Signal')
                
                # 绘制MACD柱状图
                for j in range(len(df)):
                    if j < len(df):
                        if df['macd_diff'].iloc[j] >= 0:
                            ax_macd.bar(dates[j], df['macd_diff'].iloc[j], width=0.6, color=self.colors['histogram_pos'])
                        else:
                            ax_macd.bar(dates[j], df['macd_diff'].iloc[j], width=0.6, color=self.colors['histogram_neg'])
                
                # 在MACD图表上也绘制趋势线
                if xd_list and len(xd_list) >= 2:
                    xd_list = xd_list
                    first_seg = xd_list[0]
                    last_seg = xd_list[-1]
                    
                    # 获取MACD值范围作为趋势线的垂直位置基础
                    macd_max = df['macd'].max()
                    macd_min = df['macd'].min()
                    macd_range = macd_max - macd_min
                    
                    # 取MACD范围的60%位置绘制趋势线
                    macd_level = macd_max - 0.4 * macd_range
                    
                    x_start = mdates.date2num(df['timestamp'].iloc[first_seg[0]])
                    x_end = mdates.date2num(df['timestamp'].iloc[last_seg[1]])
                    
                    # 绘制水平趋势线
                    ax_macd.plot([x_start, x_end], [macd_level, macd_level], 
                               color=self.colors['trend_line'], linewidth=2.0, linestyle='-', zorder=4)
                
                ax_macd.axhline(y=0, color='gray', linestyle='-', linewidth=0.5)
                ax_macd.set_ylabel('MACD', fontsize=10)
                ax_macd.legend(loc='upper left', fontsize=8)
            
            # 设置x轴格式
            if i == n_timeframes - 1:  # 仅对最后一个图表显示详细的x轴标签
                ax_macd.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            else:
                plt.setp(ax_macd.get_xticklabels(), visible=False)
            
            # 添加网格和图例
            ax1.grid(True, linestyle=':', alpha=0.7)
            ax1.set_ylabel('价格', fontsize=12)
            ax1.legend(loc='upper left', fontsize=9)
            
            # 添加最新价格标注
            last_price = df['close'].iloc[-1]
            ax1.axhline(y=last_price, color='red', linestyle='--', alpha=0.5)
            ax1.text(mdates.date2num(df['timestamp'].iloc[-1]), last_price, 
                         f' 最新: {last_price:.2f}', verticalalignment='bottom', 
                         horizontalalignment='right', color='red', fontsize=9)
        
        # 优化布局
        plt.tight_layout()
        fig.subplots_adjust(top=0.95)  # 为主标题留出空间
        
        # 保存图表
        if save_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            symbol_str = symbol.replace('/', '_')
            save_path = os.path.join(self.save_dir, f"{symbol_str}_multi_timeframe_{timestamp}.png")
        
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"多时间周期缠论分析图表已保存: {save_path}")
        return save_path

    def calculate_profit_targets(self, bi_list, df, pivot_zones, buy_points, sell_points, point_type):
        """
        计算买卖点的止盈目标
        
        参数:
            bi_list: 笔列表
            df: 数据DataFrame
            pivot_zones: 中枢列表
            buy_points: 买点列表
            sell_points: 卖点列表
            point_type: 买卖点类型 (1, 2, 3)
            
        返回:
            dict: 包含每个买卖点的止盈目标信息
        """
        profit_targets = {
            'buy_targets': [],  # 买点止盈目标
            'sell_targets': []  # 卖点止盈目标
        }
        
        # 计算买点止盈目标
        for buy_point in buy_points:
            if point_type == 3:
                # 第三类买点：(idx, price, strength, pivot_idx, reason)
                idx, price, strength, pivot_idx, reason = buy_point
            else:
                # 第一类和第二类买点：(idx, price, strength) 或 (idx, price, strength, pivot_idx)
                idx, price, strength = buy_point[:3]
                pivot_idx = buy_point[3] if len(buy_point) > 3 else None
            
            targets = self._calculate_buy_profit_targets(
                idx, price, bi_list, df, pivot_zones, pivot_idx, point_type
            )
            
            profit_targets['buy_targets'].append({
                'point': buy_point,
                'targets': targets
            })
        
        # 计算卖点止盈目标
        for sell_point in sell_points:
            if point_type == 3:
                # 第三类卖点：(idx, price, strength, pivot_idx, reason)
                idx, price, strength, pivot_idx, reason = sell_point
            else:
                # 第一类和第二类卖点：(idx, price, strength) 或 (idx, price, strength, pivot_idx)
                idx, price, strength = sell_point[:3]
                pivot_idx = sell_point[3] if len(sell_point) > 3 else None
            
            targets = self._calculate_sell_profit_targets(
                idx, price, bi_list, df, pivot_zones, pivot_idx, point_type
            )
            
            profit_targets['sell_targets'].append({
                'point': sell_point,
                'targets': targets
            })
        
        return profit_targets
    
    def _calculate_buy_profit_targets(self, idx, price, bi_list, df, pivot_zones, pivot_idx, point_type):
        """
        计算买点的止盈目标
        """
        targets = {}
        
        if point_type == 1:
            # 第一类买点止盈策略
            targets['conservative'] = self._find_next_resistance(idx, price, bi_list, df)
            targets['aggressive'] = self._find_trend_target(idx, price, bi_list, df, 'up')
            targets['description'] = "第一类买点：保守目标为下一阻力位，积极目标为趋势延续"
            
        elif point_type == 2:
            # 第二类买点止盈策略
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                pivot_zone = pivot_zones[pivot_idx]
                zg = pivot_zone[2]  # 中枢上沿
                
                # 寻找中枢形成前的高点
                pre_pivot_high = self._find_pre_pivot_high(pivot_zone, bi_list, df)
                
                targets['conservative'] = zg * 1.01  # 中枢上沿上方1%
                targets['aggressive'] = pre_pivot_high * 1.02 if pre_pivot_high else zg * 1.05
                targets['description'] = "第二类买点：保守目标为中枢上沿，积极目标为前高突破"
            
        elif point_type == 3:
            # 第三类买点止盈策略
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                pivot_zone = pivot_zones[pivot_idx]
                zg = pivot_zone[2]  # 中枢上沿
                
                # 寻找前一个向上笔的高点
                prev_up_high = self._find_previous_up_stroke_high(idx, bi_list)
                
                targets['conservative'] = zg  # 中枢上沿
                targets['aggressive'] = prev_up_high if prev_up_high else zg * 1.02
                targets['description'] = "第三类买点：保守目标为中枢上沿，积极目标为前一上升笔高点"
        
        # 添加止损位
        targets['stop_loss'] = self._calculate_buy_stop_loss(idx, price, bi_list, pivot_zones, pivot_idx, point_type)
        
        return targets
    
    def _calculate_sell_profit_targets(self, idx, price, bi_list, df, pivot_zones, pivot_idx, point_type):
        """
        计算卖点的止盈目标
        """
        targets = {}
        
        if point_type == 1:
            # 第一类卖点止盈策略
            targets['conservative'] = self._find_next_support(idx, price, bi_list, df)
            targets['aggressive'] = self._find_trend_target(idx, price, bi_list, df, 'down')
            targets['description'] = "第一类卖点：保守目标为下一支撑位，积极目标为趋势延续"
            
        elif point_type == 2:
            # 第二类卖点止盈策略
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                pivot_zone = pivot_zones[pivot_idx]
                zd = pivot_zone[3]  # 中枢下沿
                
                # 寻找中枢形成前的低点
                pre_pivot_low = self._find_pre_pivot_low(pivot_zone, bi_list, df)
                
                targets['conservative'] = zd * 0.99  # 中枢下沿下方1%
                targets['aggressive'] = pre_pivot_low * 0.98 if pre_pivot_low else zd * 0.95
                targets['description'] = "第二类卖点：保守目标为中枢下沿，积极目标为前低跌破"
            
        elif point_type == 3:
            # 第三类卖点止盈策略
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                pivot_zone = pivot_zones[pivot_idx]
                zd = pivot_zone[3]  # 中枢下沿
                
                # 寻找前一个向下笔的低点
                prev_down_low = self._find_previous_down_stroke_low(idx, bi_list)
                
                targets['conservative'] = zd  # 中枢下沿
                targets['aggressive'] = prev_down_low if prev_down_low else zd * 0.98
                targets['description'] = "第三类卖点：保守目标为中枢下沿，积极目标为前一下降笔低点"
        
        # 添加止损位
        targets['stop_loss'] = self._calculate_sell_stop_loss(idx, price, bi_list, pivot_zones, pivot_idx, point_type)
        
        return targets
    
    def _find_next_resistance(self, idx, price, bi_list, df):
        """寻找下一个阻力位"""
        # 简化实现：寻找后续的高点
        for bi in bi_list:
            if bi[0] > idx:  # 在买点之后的笔
                if bi[4] == 'up':  # 上升笔
                    return bi[3]  # 返回高点
        return price * 1.05  # 默认5%目标
    
    def _find_next_support(self, idx, price, bi_list, df):
        """寻找下一个支撑位"""
        # 简化实现：寻找后续的低点
        for bi in bi_list:
            if bi[0] > idx:  # 在卖点之后的笔
                if bi[4] == 'down':  # 下降笔
                    return bi[3]  # 返回低点
        return price * 0.95  # 默认5%目标
    
    def _find_trend_target(self, idx, price, bi_list, df, direction):
        """寻找趋势目标"""
        if direction == 'up':
            return price * 1.10  # 默认10%上涨目标
        else:
            return price * 0.90  # 默认10%下跌目标
    
    def _find_pre_pivot_high(self, pivot_zone, bi_list, df):
        """寻找中枢形成前的高点"""
        pivot_start_idx = pivot_zone[0]
        
        for bi in reversed(bi_list):
            if bi[1] < pivot_start_idx:  # 在中枢开始之前
                if bi[4] == 'up':  # 上升笔
                    return bi[3]  # 返回高点
        return None
    
    def _find_pre_pivot_low(self, pivot_zone, bi_list, df):
        """寻找中枢形成前的低点"""
        pivot_start_idx = pivot_zone[0]
        
        for bi in reversed(bi_list):
            if bi[1] < pivot_start_idx:  # 在中枢开始之前
                if bi[4] == 'down':  # 下降笔
                    return bi[3]  # 返回低点
        return None
    
    def _find_previous_up_stroke_high(self, idx, bi_list):
        """寻找前一个上升笔的高点"""
        for bi in reversed(bi_list):
            if bi[1] < idx and bi[4] == 'up':  # 在当前点之前的上升笔
                return bi[3]  # 返回高点
        return None
    
    def _find_previous_down_stroke_low(self, idx, bi_list):
        """寻找前一个下降笔的低点"""
        for bi in reversed(bi_list):
            if bi[1] < idx and bi[4] == 'down':  # 在当前点之前的下降笔
                return bi[3]  # 返回低点
        return None
    
    def _calculate_buy_stop_loss(self, idx, price, bi_list, pivot_zones, pivot_idx, point_type):
        """计算买点止损位"""
        if point_type == 1:
            # 第一类买点：前低下方
            for bi in reversed(bi_list):
                if bi[1] < idx and bi[4] == 'down':
                    return bi[3] * 0.98  # 前低下方2%
            return price * 0.95  # 默认5%止损
            
        elif point_type == 2:
            # 第二类买点：中枢下沿下方
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                zd = pivot_zones[pivot_idx][3]  # 中枢下沿
                return zd * 0.98  # 中枢下沿下方2%
            return price * 0.95
            
        elif point_type == 3:
            # 第三类买点：中枢上沿下方
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                zg = pivot_zones[pivot_idx][2]  # 中枢上沿
                return zg * 0.99  # 中枢上沿下方1%
            return price * 0.97
        
        return price * 0.95  # 默认止损
    
    def _calculate_sell_stop_loss(self, idx, price, bi_list, pivot_zones, pivot_idx, point_type):
        """计算卖点止损位"""
        if point_type == 1:
            # 第一类卖点：前高上方
            for bi in reversed(bi_list):
                if bi[1] < idx and bi[4] == 'up':
                    return bi[3] * 1.02  # 前高上方2%
            return price * 1.05  # 默认5%止损
            
        elif point_type == 2:
            # 第二类卖点：中枢上沿上方
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                zg = pivot_zones[pivot_idx][2]  # 中枢上沿
                return zg * 1.02  # 中枢上沿上方2%
            return price * 1.05
            
        elif point_type == 3:
            # 第三类卖点：中枢下沿上方
            if pivot_idx is not None and pivot_idx < len(pivot_zones):
                zd = pivot_zones[pivot_idx][3]  # 中枢下沿
                return zd * 1.01  # 中枢下沿上方1%
            return price * 1.03
        
        return price * 1.05  # 默认止损

    def plot_multi_timeframe_with_evolution(self, data_dict, symbol, fx_dict=None, bi_dict=None,
                                           xd_dict=None, evolution_data=None, show_pivots=True,
                                           show_line=True, show_buy_sell_points=True,
                                           add_analysis_text=True, title=None):
        """
        绘制多时间周期缠论分析图表（包含走势推演）

        参数:
            data_dict: 多时间周期数据字典 {timeframe: DataFrame}
            symbol: 交易对符号
            fx_dict: 多时间周期分型数据字典
            bi_dict: 多时间周期笔数据字典
            xd_dict: 多时间周期线段数据字典
            evolution_data: 走势推演数据
            show_pivots: 是否显示中枢
            show_line: 是否显示线图（否则显示K线图）
            show_buy_sell_points: 是否显示买卖点
            add_analysis_text: 是否添加分析文本
            title: 图表标题

        返回:
            str: 图表保存路径
        """
        try:
            # 如果有推演数据，直接绘制包含推演的完整图表
            if evolution_data and evolution_data.get('evolution_points'):
                return self._plot_complete_chart_with_evolution(
                    data_dict=data_dict,
                    symbol=symbol,
                    fx_dict=fx_dict,
                    bi_dict=bi_dict,
                    xd_dict=xd_dict,
                    evolution_data=evolution_data,
                    show_pivots=show_pivots,
                    show_line=show_line,
                    show_buy_sell_points=show_buy_sell_points,
                    add_analysis_text=add_analysis_text,
                    title=title
                )
            else:
                # 没有推演数据，使用原有方法
                return self.plot_multi_timeframe(
                    data_dict=data_dict,
                    symbol=symbol,
                    fx_dict=fx_dict,
                    bi_dict=bi_dict,
                    xd_dict=xd_dict,
                    show_pivots=show_pivots,
                    show_line=show_line,
                    show_buy_sell_points=show_buy_sell_points,
                    add_analysis_text=add_analysis_text,
                    title=title
                )

        except Exception as e:
            logger.error(f"绘制带推演的多级别图表失败: {str(e)}")
            return None

    def _plot_complete_chart_with_evolution(self, data_dict, symbol, fx_dict=None, bi_dict=None,
                                          xd_dict=None, evolution_data=None, show_pivots=True,
                                          show_line=True, show_buy_sell_points=True,
                                          add_analysis_text=True, title=None):
        """
        绘制包含所有缠论元素和推演数据的完整图表
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from matplotlib.gridspec import GridSpec
            from datetime import datetime

            # 设置图表样式
            plt.style.use('dark_background')

            # 创建图表
            n_timeframes = len(data_dict)
            fig = plt.figure(figsize=(16, 6 * n_timeframes))
            fig.patch.set_facecolor('#161a2b')

            # 创建子图布局
            gs = GridSpec(n_timeframes * 2, 1, height_ratios=[3, 1] * n_timeframes,
                         hspace=0.3, figure=fig)

            # 设置图表标题
            if title:
                fig.suptitle(title, fontsize=16, color='white', y=0.98)

            # 准备推演数据
            evolution_timeframe = evolution_data.get('timeframe', '15m')
            evolution_points = evolution_data['evolution_points']
            current_time = evolution_data['current_time']
            current_price = evolution_data['current_price']
            primary_scenario = evolution_data['primary_scenario']

            # 转换推演时间数据
            evolution_times = [current_time] + [point['timestamp'] for point in evolution_points]
            evolution_prices = [current_price] + [point['price'] for point in evolution_points]
            evolution_dates = [mdates.date2num(t) for t in evolution_times]

            # 确定推演线颜色
            scenario_color = self.colors['evolution_line']
            if primary_scenario.trend_type.value == 'up':
                scenario_color = '#00ff00'  # 绿色表示上涨
            elif primary_scenario.trend_type.value == 'down':
                scenario_color = '#ff0000'  # 红色表示下跌
            else:
                scenario_color = '#ffff00'  # 黄色表示震荡

            # 为每个时间周期绘制子图
            for i, (tf, df) in enumerate(data_dict.items()):
                # 创建K线子图和MACD子图
                ax_candle = fig.add_subplot(gs[i*2, 0])
                ax_macd = fig.add_subplot(gs[i*2+1, 0], sharex=ax_candle)

                # 设置子图背景色
                ax_candle.set_facecolor('#161a2b')
                ax_macd.set_facecolor('#161a2b')

                # 设置标题
                ax_candle.set_title(f"{tf} 周期", fontsize=12, color='white')

                # 处理时间戳
                df = df.copy()
                if 'timestamp' in df.columns:
                    if isinstance(df['timestamp'].iloc[0], str):
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                else:
                    df['timestamp'] = pd.to_datetime(df.index)

                # 绘制价格线
                ax_candle.plot(mdates.date2num(df['timestamp']), df['close'],
                              color=self.colors['price_line'], linewidth=1.5, label='价格', zorder=1)

                # 绘制缠论元素
                self._draw_chan_elements_on_axis(
                    ax_candle, df, tf, fx_dict, bi_dict, xd_dict,
                    show_pivots, show_buy_sell_points
                )

                # 如果是推演对应的时间周期，添加推演线
                if tf == evolution_timeframe:
                    self._draw_evolution_on_axis(
                        ax_candle, evolution_dates, evolution_prices,
                        scenario_color, primary_scenario
                    )

                # 绘制MACD
                self._draw_macd_on_axis(ax_macd, df)

                # 设置坐标轴样式
                self._setup_axis_style(ax_candle, ax_macd, df, i, n_timeframes)

            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{symbol.replace('/', '_')}_multi_timeframe_{timestamp}.png"
            save_path = os.path.join(self.save_dir, filename)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='#161a2b', edgecolor='none')
            plt.close(fig)

            logger.info(f"多时间周期缠论分析图表已保存: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"绘制完整推演图表失败: {str(e)}")
            return None

    def _draw_chan_elements_on_axis(self, ax, df, timeframe, fx_dict, bi_dict, xd_dict,
                                   show_pivots, show_buy_sell_points):
        """在指定坐标轴上绘制缠论元素"""
        try:
            import matplotlib.dates as mdates

            # 获取当前时间周期的缠论数据
            fx_list = fx_dict.get(timeframe, []) if fx_dict else []
            bi_list = bi_dict.get(timeframe, []) if bi_dict else []
            xd_list = xd_dict.get(timeframe, []) if xd_dict else []

            # 绘制分型
            if fx_list:
                self._draw_fractals_on_axis(ax, df, fx_list)

            # 绘制笔
            if bi_list:
                self._draw_strokes_on_axis(ax, df, bi_list)

            # 绘制线段
            if xd_list:
                self._draw_segments_on_axis(ax, df, xd_list)

            # 绘制中枢（如果有）
            if show_pivots and hasattr(self, 'pivot_zones'):
                self._draw_pivots_on_axis(ax, df, getattr(self, 'pivot_zones', []))

            # 绘制买卖点（如果有）
            if show_buy_sell_points:
                self._draw_buy_sell_points_on_axis(ax, df, timeframe)

        except Exception as e:
            logger.warning(f"绘制缠论元素失败: {str(e)}")

    def _draw_fractals_on_axis(self, ax, df, fx_list):
        """绘制分型"""
        try:
            import matplotlib.dates as mdates

            for fx in fx_list:
                if len(fx) >= 4:
                    idx, fx_type, price, _ = fx[:4]
                    if 0 <= idx < len(df):
                        timestamp = df['timestamp'].iloc[idx]
                        date_val = mdates.date2num(timestamp)

                        if fx_type == 'top':
                            ax.scatter(date_val, price, color=self.colors['top_fractal'],
                                     marker='v', s=50, zorder=5, alpha=0.8)
                        elif fx_type == 'bottom':
                            ax.scatter(date_val, price, color=self.colors['bottom_fractal'],
                                     marker='^', s=50, zorder=5, alpha=0.8)
        except Exception as e:
            logger.warning(f"绘制分型失败: {str(e)}")

    def _draw_strokes_on_axis(self, ax, df, bi_list):
        """绘制笔"""
        try:
            import matplotlib.dates as mdates

            for bi in bi_list:
                if len(bi) >= 5:
                    start_idx, end_idx, start_price, end_price, direction = bi[:5]

                    if (0 <= start_idx < len(df) and 0 <= end_idx < len(df)):
                        start_time = df['timestamp'].iloc[start_idx]
                        end_time = df['timestamp'].iloc[end_idx]

                        start_date = mdates.date2num(start_time)
                        end_date = mdates.date2num(end_time)

                        # 根据方向选择颜色
                        color = self.colors['up_stroke'] if direction == 'up' else self.colors['down_stroke']

                        ax.plot([start_date, end_date], [start_price, end_price],
                               color=color, linewidth=2, alpha=0.8, zorder=3)
        except Exception as e:
            logger.warning(f"绘制笔失败: {str(e)}")

    def _draw_segments_on_axis(self, ax, df, xd_list):
        """绘制线段"""
        try:
            import matplotlib.dates as mdates

            for xd in xd_list:
                if len(xd) >= 5:
                    start_idx, end_idx, start_price, end_price, direction = xd[:5]

                    if (0 <= start_idx < len(df) and 0 <= end_idx < len(df)):
                        start_time = df['timestamp'].iloc[start_idx]
                        end_time = df['timestamp'].iloc[end_idx]

                        start_date = mdates.date2num(start_time)
                        end_date = mdates.date2num(end_time)

                        # 根据方向选择颜色
                        color = self.colors['up_segment'] if direction == 'up' else self.colors['down_segment']

                        ax.plot([start_date, end_date], [start_price, end_price],
                               color=color, linewidth=3, alpha=0.9, zorder=4)
        except Exception as e:
            logger.warning(f"绘制线段失败: {str(e)}")

    def _draw_pivots_on_axis(self, ax, df, pivot_zones):
        """绘制中枢"""
        try:
            import matplotlib.dates as mdates

            for pivot in pivot_zones:
                if isinstance(pivot, dict):
                    start_idx = pivot.get('start_idx', 0)
                    end_idx = pivot.get('end_idx', len(df)-1)
                    high = pivot.get('high', 0)
                    low = pivot.get('low', 0)

                    if (0 <= start_idx < len(df) and 0 <= end_idx < len(df) and high > low):
                        start_time = df['timestamp'].iloc[start_idx]
                        end_time = df['timestamp'].iloc[end_idx]

                        start_date = mdates.date2num(start_time)
                        end_date = mdates.date2num(end_time)

                        # 绘制中枢矩形
                        ax.fill_between([start_date, end_date], [low, low], [high, high],
                                       color=self.colors['pivot_zone'], alpha=0.2, zorder=2)

                        # 绘制中枢边界线
                        ax.plot([start_date, end_date], [high, high],
                               color=self.colors['pivot_zone'], linewidth=1, alpha=0.6)
                        ax.plot([start_date, end_date], [low, low],
                               color=self.colors['pivot_zone'], linewidth=1, alpha=0.6)
        except Exception as e:
            logger.warning(f"绘制中枢失败: {str(e)}")

    def _draw_buy_sell_points_on_axis(self, ax, df, timeframe):
        """绘制买卖点"""
        try:
            import matplotlib.dates as mdates

            # 这里可以根据实际的买卖点数据进行绘制
            # 由于买卖点数据结构可能复杂，这里提供基本框架
            # 实际使用时需要根据具体的买卖点数据结构调整

            # 示例：如果有买卖点数据
            if hasattr(self, 'buy_points') and timeframe in getattr(self, 'buy_points', {}):
                buy_points = self.buy_points[timeframe]
                for point in buy_points:
                    if isinstance(point, (list, tuple)) and len(point) >= 3:
                        idx, price, point_type = point[:3]
                        if 0 <= idx < len(df):
                            timestamp = df['timestamp'].iloc[idx]
                            date_val = mdates.date2num(timestamp)
                            ax.scatter(date_val, price, color='green', marker='^',
                                     s=100, zorder=6, alpha=0.9, label=f'买点{point_type}')

            if hasattr(self, 'sell_points') and timeframe in getattr(self, 'sell_points', {}):
                sell_points = self.sell_points[timeframe]
                for point in sell_points:
                    if isinstance(point, (list, tuple)) and len(point) >= 3:
                        idx, price, point_type = point[:3]
                        if 0 <= idx < len(df):
                            timestamp = df['timestamp'].iloc[idx]
                            date_val = mdates.date2num(timestamp)
                            ax.scatter(date_val, price, color='red', marker='v',
                                     s=100, zorder=6, alpha=0.9, label=f'卖点{point_type}')

        except Exception as e:
            logger.warning(f"绘制买卖点失败: {str(e)}")

    def _draw_evolution_on_axis(self, ax, evolution_dates, evolution_prices, scenario_color, primary_scenario):
        """在指定坐标轴上绘制推演线"""
        try:
            # 绘制推演线（虚线）- 从当前价格点开始
            ax.plot(evolution_dates, evolution_prices,
                   color=scenario_color, linewidth=3, linestyle='--',
                   alpha=0.9, label=f'推演走势({primary_scenario.trend_type.value.upper()})',
                   zorder=10)

            # 绘制推演区域（置信区间）
            confidence = primary_scenario.probability
            price_range = max(evolution_prices) - min(evolution_prices)
            if price_range > 0:
                uncertainty = (1 - confidence) * price_range * 0.2

                upper_prices = [p + uncertainty for p in evolution_prices]
                lower_prices = [p - uncertainty for p in evolution_prices]

                ax.fill_between(evolution_dates, lower_prices, upper_prices,
                               color=scenario_color, alpha=0.15,
                               label=f'推演区间(置信度:{confidence:.1%})')

            # 添加推演起点标记
            ax.scatter([evolution_dates[0]], [evolution_prices[0]],
                      color='white', s=80, marker='o',
                      edgecolor=scenario_color, linewidth=2,
                      label='推演起点', zorder=11)

            # 添加推演终点标记
            ax.scatter([evolution_dates[-1]], [evolution_prices[-1]],
                      color=scenario_color, s=120, marker='*',
                      edgecolor='white', linewidth=1,
                      label='推演目标', zorder=11)

            # 添加推演信息文本
            text_x = evolution_dates[-1]
            text_y = evolution_prices[-1]

            info_text = (f"推演: {primary_scenario.trend_type.value.upper()}\n"
                        f"概率: {primary_scenario.probability:.1%}\n"
                        f"目标: ${evolution_prices[-1]:,.2f}")

            ax.annotate(info_text,
                       xy=(text_x, text_y),
                       xytext=(15, 15),
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.5',
                               facecolor=scenario_color,
                               alpha=0.8),
                       fontsize=9,
                       color='white',
                       weight='bold',
                       zorder=12)

        except Exception as e:
            logger.warning(f"绘制推演线失败: {str(e)}")

    def _draw_macd_on_axis(self, ax_macd, df):
        """在指定坐标轴上绘制MACD"""
        try:
            import matplotlib.dates as mdates

            if 'macd' in df.columns:
                ax_macd.plot(mdates.date2num(df['timestamp']), df['macd'],
                            color=self.colors['macd'], linewidth=1, label='MACD')
                if 'macd_signal' in df.columns:
                    ax_macd.plot(mdates.date2num(df['timestamp']), df['macd_signal'],
                                color=self.colors['signal'], linewidth=1, label='Signal')
                if 'macd_histogram' in df.columns:
                    colors = [self.colors['histogram_pos'] if x >= 0 else self.colors['histogram_neg']
                             for x in df['macd_histogram']]
                    ax_macd.bar(mdates.date2num(df['timestamp']), df['macd_histogram'],
                               color=colors, alpha=0.7, width=0.8)

            ax_macd.axhline(y=0, color='gray', linestyle='-', linewidth=0.5)
            ax_macd.set_ylabel('MACD', fontsize=10, color='white')

        except Exception as e:
            logger.warning(f"绘制MACD失败: {str(e)}")

    def _setup_axis_style(self, ax_candle, ax_macd, df, index, total_count):
        """设置坐标轴样式"""
        try:
            import matplotlib.dates as mdates

            # 设置x轴格式
            if index == total_count - 1:
                ax_macd.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                ax_macd.xaxis.set_major_locator(mdates.HourLocator(interval=4))
                plt.setp(ax_macd.get_xticklabels(), rotation=45, color='white')
            else:
                plt.setp(ax_macd.get_xticklabels(), visible=False)

            # 设置坐标轴颜色
            ax_candle.tick_params(colors='white')
            ax_macd.tick_params(colors='white')

            for spine in ax_candle.spines.values():
                spine.set_color('#2a2e3e')
            for spine in ax_macd.spines.values():
                spine.set_color('#2a2e3e')

            # 添加网格和图例
            ax_candle.grid(True, linestyle=':', alpha=0.7, color='#2a2e3e')
            ax_candle.set_ylabel('价格 (USDT)', fontsize=12, color='white')
            ax_candle.legend(loc='upper left', fontsize=9)

            # 添加最新价格标注
            last_price = df['close'].iloc[-1]
            ax_candle.axhline(y=last_price, color='red', linestyle='--', alpha=0.5)
            ax_candle.text(mdates.date2num(df['timestamp'].iloc[-1]), last_price,
                          f' 最新: {last_price:.2f}', verticalalignment='bottom',
                          horizontalalignment='right', color='red', fontsize=9)

        except Exception as e:
            logger.warning(f"设置坐标轴样式失败: {str(e)}")

    def _add_evolution_overlay(self, chart_path: str, evolution_data: Dict, data_dict: Dict):
        """
        在现有图表上添加走势推演覆盖层

        参数:
            chart_path: 现有图表路径
            evolution_data: 推演数据
            data_dict: 原始数据字典
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            import matplotlib.image as mpimg
            from datetime import datetime, timedelta

            # 读取已生成的图表
            img = mpimg.imread(chart_path)

            # 重新打开图表进行编辑（保持原有内容）
            fig = plt.figure(figsize=(16, 6 * len(data_dict)))

            # 找到推演数据对应的时间周期
            evolution_timeframe = evolution_data.get('timeframe', '15m')
            if evolution_timeframe not in data_dict:
                evolution_timeframe = list(data_dict.keys())[0]

            # 准备推演数据
            evolution_points = evolution_data['evolution_points']
            current_time = evolution_data['current_time']
            current_price = evolution_data['current_price']
            primary_scenario = evolution_data['primary_scenario']

            # 获取对应时间周期的数据
            target_df = data_dict[evolution_timeframe]

            # 确保时间格式一致
            if isinstance(current_time, str):
                current_time = pd.to_datetime(current_time)

            # 转换推演时间数据
            evolution_times = [current_time] + [point['timestamp'] for point in evolution_points]
            evolution_prices = [current_price] + [point['price'] for point in evolution_points]

            # 转换为matplotlib日期格式
            evolution_dates = [mdates.date2num(t) for t in evolution_times]

            # 确定推演线颜色
            scenario_color = self.colors['evolution_line']
            if primary_scenario.trend_type.value == 'up':
                scenario_color = '#00ff00'  # 绿色表示上涨
            elif primary_scenario.trend_type.value == 'down':
                scenario_color = '#ff0000'  # 红色表示下跌
            else:
                scenario_color = '#ffff00'  # 黄色表示震荡

            # 重新绘制完整图表（包含原有数据和推演数据）
            self._redraw_chart_with_evolution(
                fig, data_dict, evolution_data, scenario_color, chart_path
            )

            logger.info(f"走势推演覆盖层已添加到图表: {chart_path}")

        except Exception as e:
            logger.error(f"添加推演覆盖层失败: {str(e)}")
            # 不抛出异常，保持原图表可用

    def _redraw_chart_with_evolution(self, fig, data_dict, evolution_data, scenario_color, save_path):
        """
        重新绘制包含推演数据的完整图表
        """
        try:
            # 清除当前图表
            fig.clear()

            # 创建子图布局
            n_timeframes = len(data_dict)
            gs = GridSpec(n_timeframes * 2, 1, height_ratios=[3, 1] * n_timeframes,
                         hspace=0.3, figure=fig)

            evolution_timeframe = evolution_data.get('timeframe', '15m')
            evolution_points = evolution_data['evolution_points']
            current_time = evolution_data['current_time']
            current_price = evolution_data['current_price']
            primary_scenario = evolution_data['primary_scenario']

            # 转换推演时间数据
            evolution_times = [current_time] + [point['timestamp'] for point in evolution_points]
            evolution_prices = [current_price] + [point['price'] for point in evolution_points]
            evolution_dates = [mdates.date2num(t) for t in evolution_times]

            # 为每个时间周期绘制子图
            for i, (tf, df) in enumerate(data_dict.items()):
                # 创建K线子图和MACD子图
                ax_candle = fig.add_subplot(gs[i*2, 0])
                ax_macd = fig.add_subplot(gs[i*2+1, 0], sharex=ax_candle)

                # 设置标题
                ax_candle.set_title(f"{tf} 周期", fontsize=12)

                # 处理时间戳
                df = df.copy()
                if 'timestamp' in df.columns:
                    if isinstance(df['timestamp'].iloc[0], str):
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                else:
                    df['timestamp'] = pd.to_datetime(df.index)

                # 为绘图准备数据
                ohlc_data = []
                for j, row in df.iterrows():
                    date_val = mdates.date2num(row['timestamp'])
                    ohlc_data.append([date_val, row['open'], row['high'], row['low'], row['close']])

                # 绘制价格线图
                ax_candle.plot(mdates.date2num(df['timestamp']), df['close'],
                              color=self.colors['price_line'], linewidth=1.5, label='价格')

                # 如果是推演对应的时间周期，添加推演线
                if tf == evolution_timeframe:
                    # 绘制推演线（虚线）- 从当前价格点开始
                    ax_candle.plot(evolution_dates, evolution_prices,
                                  color=scenario_color, linewidth=3, linestyle='--',
                                  alpha=0.9, label=f'推演走势({primary_scenario.trend_type.value.upper()})',
                                  zorder=10)

                    # 绘制推演区域（置信区间）
                    confidence = primary_scenario.probability
                    price_range = max(evolution_prices) - min(evolution_prices)
                    if price_range > 0:
                        uncertainty = (1 - confidence) * price_range * 0.2

                        upper_prices = [p + uncertainty for p in evolution_prices]
                        lower_prices = [p - uncertainty for p in evolution_prices]

                        ax_candle.fill_between(evolution_dates, lower_prices, upper_prices,
                                              color=scenario_color, alpha=0.15,
                                              label=f'推演区间(置信度:{confidence:.1%})')

                    # 添加推演起点标记
                    ax_candle.scatter([evolution_dates[0]], [evolution_prices[0]],
                                     color='white', s=80, marker='o',
                                     edgecolor=scenario_color, linewidth=2,
                                     label='推演起点', zorder=11)

                    # 添加推演终点标记
                    ax_candle.scatter([evolution_dates[-1]], [evolution_prices[-1]],
                                     color=scenario_color, s=120, marker='*',
                                     edgecolor='white', linewidth=1,
                                     label='推演目标', zorder=11)

                    # 添加推演信息文本
                    text_x = evolution_dates[-1]
                    text_y = evolution_prices[-1]

                    info_text = (f"推演: {primary_scenario.trend_type.value.upper()}\n"
                                f"概率: {primary_scenario.probability:.1%}\n"
                                f"目标: ${evolution_prices[-1]:,.2f}")

                    ax_candle.annotate(info_text,
                                      xy=(text_x, text_y),
                                      xytext=(15, 15),
                                      textcoords='offset points',
                                      bbox=dict(boxstyle='round,pad=0.5',
                                              facecolor=scenario_color,
                                              alpha=0.8),
                                      fontsize=9,
                                      color='white',
                                      weight='bold',
                                      zorder=12)

                # 绘制MACD
                if 'macd' in df.columns:
                    ax_macd.plot(mdates.date2num(df['timestamp']), df['macd'],
                                color=self.colors['macd'], linewidth=1, label='MACD')
                    if 'macd_signal' in df.columns:
                        ax_macd.plot(mdates.date2num(df['timestamp']), df['macd_signal'],
                                    color=self.colors['signal'], linewidth=1, label='Signal')
                    if 'macd_histogram' in df.columns:
                        colors = [self.colors['histogram_pos'] if x >= 0 else self.colors['histogram_neg']
                                 for x in df['macd_histogram']]
                        ax_macd.bar(mdates.date2num(df['timestamp']), df['macd_histogram'],
                                   color=colors, alpha=0.7, width=0.8)

                ax_macd.axhline(y=0, color='gray', linestyle='-', linewidth=0.5)
                ax_macd.set_ylabel('MACD', fontsize=10)

                # 设置x轴格式
                if i == n_timeframes - 1:
                    ax_macd.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                else:
                    plt.setp(ax_macd.get_xticklabels(), visible=False)

                # 添加网格和图例
                ax_candle.grid(True, linestyle=':', alpha=0.7)
                ax_candle.set_ylabel('价格', fontsize=12)
                ax_candle.legend(loc='upper left', fontsize=9)

                # 添加最新价格标注
                last_price = df['close'].iloc[-1]
                ax_candle.axhline(y=last_price, color='red', linestyle='--', alpha=0.5)
                ax_candle.text(mdates.date2num(df['timestamp'].iloc[-1]), last_price,
                              f' 最新: {last_price:.2f}', verticalalignment='bottom',
                              horizontalalignment='right', color='red', fontsize=9)

            # 优化布局
            plt.tight_layout()
            fig.subplots_adjust(top=0.95)

            # 保存图表
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='#161a2b', edgecolor='none')
            plt.close(fig)

        except Exception as e:
            logger.error(f"重新绘制图表失败: {str(e)}")
            raise


# 测试代码
if __name__ == "__main__":
    from data_fetcher import DataFetcher
    from chan_analysis import ChanAnalysis
    
    # 设置日志
    logger.add("logs/chart_plotter_{time}.log", rotation="100 MB")
    
    try:
        # 获取数据
        fetcher = DataFetcher(symbols="BTC/USDT", timeframe="5m")
        df = fetcher.get_klines(limit=100)
        
        # 执行缠论分析
        analyzer = ChanAnalysis()
        analyzer.analyze(df)
        
        # 创建绘图器
        plotter = ChartPlotter()
        
        # 绘制单一时间周期图表
        chart_path = plotter.plot_chan_analysis(
            df=df,
            symbol="BTC/USDT",
            timeframe="5m",
            fx_list=analyzer.fx_list,
            bi_list=analyzer.bi_list,
            xd_list=analyzer.xd_list,
            show_ma=True,
            show_macd=True,
            show_pivots=True,
            show_line=True,
            show_buy_sell_points=True,
            show_second_buy_sell_points=True,
            show_third_buy_sell_points=True
        )
        
        print(f"图表已保存: {chart_path}")
        
        # 测试多时间周期图表
        timeframes = ["5m", "15m", "1h"]
        data_dict = {}
        fx_dict = {}
        bi_dict = {}
        xd_dict = {}
        
        for tf in timeframes:
            tf_fetcher = DataFetcher(symbols="BTC/USDT", timeframe=tf)
            tf_df = tf_fetcher.get_klines(limit=100)
            data_dict[tf] = tf_df
            
            tf_analyzer = ChanAnalysis()
            tf_analyzer.analyze(tf_df)
            
            fx_dict[tf] = tf_analyzer.fx_list
            bi_dict[tf] = tf_analyzer.bi_list
            xd_dict[tf] = tf_analyzer.xd_list
        
        multi_chart_path = plotter.plot_multi_timeframe(
            data_dict=data_dict,
            symbol="BTC/USDT",
            fx_dict=fx_dict,
            bi_dict=bi_dict,
            xd_dict=xd_dict,
            show_pivots=True,
            show_line=True,
            show_buy_sell_points=True,
            add_analysis_text=True
        )
        
        print(f"多时间周期图表已保存: {multi_chart_path}")
        
    except Exception as e:
        logger.exception(f"测试过程中发生错误: {str(e)}") 
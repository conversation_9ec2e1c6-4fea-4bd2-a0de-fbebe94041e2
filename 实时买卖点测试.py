#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实时买卖点判断测试
测试系统是否能准确判断当前时刻的买卖点
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_trading_system import EnhancedTradingSystem
from unified_signal_analyzer import UnifiedSignalAnalyzer
from datetime import datetime
import time

def test_real_time_signals():
    """测试实时买卖点判断"""
    print("=" * 80)
    print("🔍 实时买卖点判断能力测试")
    print("=" * 80)
    
    # 获取当前时间
    current_time = datetime.now()
    print(f"📅 测试时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 初始化系统
    print("\n1️⃣ 初始化增强交易系统...")
    system = EnhancedTradingSystem(symbol="BTC/USDT", exchange="gate")
    print("✅ 系统初始化完成")
    
    # 分析当前买卖点
    print("\n2️⃣ 分析当前时刻买卖点...")
    decision = system.analyze_trading_opportunity()
    
    # 详细展示分析结果
    print("\n" + "=" * 60)
    print("📊 当前时刻买卖点分析结果")
    print("=" * 60)
    
    # 基本信号信息
    print(f"🎯 交易信号: {decision.action.upper()}")
    print(f"📊 信号置信度: {decision.confidence:.1%}")
    print(f"⚠️ 风险等级: {decision.risk_level}")
    print(f"⏰ 主要时间周期: {decision.timeframe}")
    
    # 大周期趋势状态
    major_trend = decision.major_trend_status
    print(f"\n🔍 大周期趋势状态:")
    print(f"   总体方向: {major_trend['status'].upper()}")
    print(f"   方向置信度: {major_trend['confidence']:.1%}")
    print(f"   变盘状态: {'⚠️ 即将变盘' if major_trend.get('is_changing', False) else '✅ 趋势稳定'}")
    
    # 各级别趋势详情
    if 'level_trends' in major_trend:
        print(f"\n📈 各级别趋势详情:")
        level_weights = {'1h': 0.6, '30m': 0.4, '15m': 0.3, '5m': 0.2, '1m': 0.1}
        
        for timeframe, trend in major_trend['level_trends'].items():
            weight = level_weights.get(timeframe, 0.1)
            trend_emoji = "📈" if trend == "bullish" else "📉" if trend == "bearish" else "➡️"
            print(f"   {trend_emoji} {timeframe:>3}: {trend.upper():>8} (权重: {weight:.1f})")
    
    # 交易信号详情
    if decision.action in ['buy', 'sell']:
        print(f"\n💰 具体交易信号:")
        print(f"   📍 建议入场价: ${decision.entry_price:,.2f}")
        print(f"   🛡️ 止损价格: ${decision.stop_loss:,.2f}")
        
        if decision.take_profits:
            print(f"   🎯 止盈目标:")
            for i, tp in enumerate(decision.take_profits, 1):
                profit_pct = abs(tp - decision.entry_price) / decision.entry_price * 100
                print(f"     第{i}目标: ${tp:,.2f} (+{profit_pct:.1f}%)")
        
        print(f"   📏 建议仓位: {decision.position_size}")
        
        # 风险收益分析
        if decision.stop_loss > 0:
            risk_pct = abs(decision.entry_price - decision.stop_loss) / decision.entry_price * 100
            if decision.take_profits:
                reward_pct = abs(decision.take_profits[0] - decision.entry_price) / decision.entry_price * 100
                risk_reward_ratio = reward_pct / risk_pct if risk_pct > 0 else 0
                print(f"   ⚖️ 风险: {risk_pct:.1f}% | 收益: {reward_pct:.1f}% | 比例: 1:{risk_reward_ratio:.1f}")
        
        # 止盈止损验证
        if decision.validation_report:
            report = decision.validation_report
            print(f"\n🔍 止盈止损验证:")
            print(f"   验证结果: {report.overall_result.value.upper()}")
            print(f"   可达性评分: {report.achievability_score:.2f}/1.0")
            print(f"   历史成功率: {report.historical_success_rate:.1%}")
            
            validation_status = "✅ 通过" if report.overall_result.value == "achievable" else \
                              "⚠️ 部分通过" if report.overall_result.value == "partial" else \
                              "❌ 不通过"
            print(f"   验证状态: {validation_status}")
    
    # 决策理由
    print(f"\n📝 系统判断理由:")
    reasoning_lines = decision.reasoning.split(';')
    for line in reasoning_lines:
        if line.strip():
            print(f"   • {line.strip()}")
    
    # 警告和建议
    if decision.warnings:
        print(f"\n⚠️ 风险警告:")
        for warning in decision.warnings:
            print(f"   • {warning}")
    
    if decision.recommendations:
        print(f"\n💡 操作建议:")
        for rec in decision.recommendations:
            print(f"   • {rec}")
    
    # 系统能力评估
    print(f"\n" + "=" * 60)
    print("🎯 系统买卖点判断能力评估")
    print("=" * 60)
    
    capabilities = []
    
    # 1. 数据获取能力
    if decision.action != 'wait' or major_trend['status'] != 'unknown':
        capabilities.append("✅ 实时数据获取: 正常")
    else:
        capabilities.append("❌ 实时数据获取: 异常")
    
    # 2. 多级别分析能力
    if 'level_trends' in major_trend and len(major_trend['level_trends']) >= 3:
        capabilities.append("✅ 多级别分析: 正常")
    else:
        capabilities.append("❌ 多级别分析: 异常")
    
    # 3. 趋势判断能力
    if major_trend['confidence'] > 0:
        capabilities.append(f"✅ 趋势判断: 正常 (置信度{major_trend['confidence']:.1%})")
    else:
        capabilities.append("❌ 趋势判断: 异常")
    
    # 4. 信号生成能力
    if decision.confidence > 0:
        capabilities.append(f"✅ 信号生成: 正常 (置信度{decision.confidence:.1%})")
    else:
        capabilities.append("❌ 信号生成: 异常")
    
    # 5. 风险评估能力
    if decision.risk_level in ['LOW', 'MEDIUM', 'HIGH']:
        capabilities.append(f"✅ 风险评估: 正常 ({decision.risk_level})")
    else:
        capabilities.append("❌ 风险评估: 异常")
    
    # 6. 止盈止损计算能力
    if decision.action in ['buy', 'sell'] and decision.stop_loss > 0:
        capabilities.append("✅ 止盈止损计算: 正常")
    elif decision.action == 'wait':
        capabilities.append("➡️ 止盈止损计算: 未触发 (等待信号)")
    else:
        capabilities.append("❌ 止盈止损计算: 异常")
    
    for capability in capabilities:
        print(f"   {capability}")
    
    # 总体评估
    success_count = sum(1 for cap in capabilities if cap.startswith("✅"))
    total_count = len([cap for cap in capabilities if not cap.startswith("➡️")])
    success_rate = success_count / total_count if total_count > 0 else 0
    
    print(f"\n📊 系统能力评分: {success_count}/{total_count} ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        print("🟢 系统状态: 优秀 - 完全具备实时买卖点判断能力")
    elif success_rate >= 0.6:
        print("🟡 系统状态: 良好 - 基本具备实时买卖点判断能力")
    else:
        print("🔴 系统状态: 需要改进 - 买卖点判断能力不足")
    
    return decision

def test_signal_quality():
    """测试信号质量"""
    print(f"\n" + "=" * 60)
    print("🔬 信号质量深度测试")
    print("=" * 60)
    
    # 初始化信号分析器
    analyzer = UnifiedSignalAnalyzer(symbol="BTC/USDT", exchange="gate")
    
    # 获取大周期趋势状态
    trend_status = analyzer.get_major_trend_status()
    
    print("📈 大周期趋势深度分析:")
    print(f"   状态: {trend_status['status'].upper()}")
    print(f"   置信度: {trend_status['confidence']:.1%}")
    print(f"   变盘检测: {'是' if trend_status.get('is_changing', False) else '否'}")
    print(f"   分析理由: {trend_status.get('reason', '无详细理由')}")
    
    # 分析统一信号
    unified_signal = analyzer.analyze_unified_signals()
    
    if unified_signal:
        print(f"\n🎯 统一信号详细分析:")
        print(f"   信号类型: {unified_signal.signal_type}")
        print(f"   信号强度: {unified_signal.strength.value}")
        print(f"   置信度: {unified_signal.confidence:.1%}")
        print(f"   有效期: {unified_signal.validity_period}分钟")
        print(f"   支持周期: {', '.join(unified_signal.supporting_timeframes) if unified_signal.supporting_timeframes else '无'}")
        print(f"   冲突周期: {', '.join(unified_signal.conflicting_timeframes) if unified_signal.conflicting_timeframes else '无'}")
        print(f"   详细理由: {unified_signal.reasoning}")
        
        # 信号质量评估
        quality_score = 0
        quality_factors = []
        
        # 强度评估
        if unified_signal.strength.value in ['VERY_STRONG', 'STRONG']:
            quality_score += 25
            quality_factors.append("✅ 信号强度: 优秀")
        elif unified_signal.strength.value == 'MEDIUM':
            quality_score += 15
            quality_factors.append("🟡 信号强度: 中等")
        else:
            quality_factors.append("🔴 信号强度: 较弱")
        
        # 置信度评估
        if unified_signal.confidence >= 0.7:
            quality_score += 25
            quality_factors.append("✅ 置信度: 高")
        elif unified_signal.confidence >= 0.5:
            quality_score += 15
            quality_factors.append("🟡 置信度: 中等")
        else:
            quality_factors.append("🔴 置信度: 低")
        
        # 支持度评估
        support_count = len(unified_signal.supporting_timeframes)
        conflict_count = len(unified_signal.conflicting_timeframes)
        
        if support_count >= 3 and conflict_count == 0:
            quality_score += 25
            quality_factors.append("✅ 多级别支持: 强")
        elif support_count >= 2:
            quality_score += 15
            quality_factors.append("🟡 多级别支持: 中等")
        else:
            quality_factors.append("🔴 多级别支持: 弱")
        
        # 时效性评估
        if unified_signal.validity_period >= 30:
            quality_score += 25
            quality_factors.append("✅ 信号时效性: 良好")
        elif unified_signal.validity_period >= 15:
            quality_score += 15
            quality_factors.append("🟡 信号时效性: 一般")
        else:
            quality_factors.append("🔴 信号时效性: 较短")
        
        print(f"\n📊 信号质量评估:")
        for factor in quality_factors:
            print(f"   {factor}")
        
        print(f"\n🏆 信号质量得分: {quality_score}/100")
        
        if quality_score >= 80:
            print("🟢 信号质量: 优秀 - 强烈推荐操作")
        elif quality_score >= 60:
            print("🟡 信号质量: 良好 - 可以考虑操作")
        elif quality_score >= 40:
            print("🟠 信号质量: 一般 - 谨慎操作")
        else:
            print("🔴 信号质量: 较差 - 建议等待")
    
    else:
        print(f"\n❌ 当前无统一信号生成")
        print("   可能原因:")
        print("   • 多级别分歧严重")
        print("   • 大周期方向不明确")
        print("   • 缺乏有效的技术形态")
        print("   • 市场处于震荡状态")

def real_time_monitoring_demo():
    """实时监控演示"""
    print(f"\n" + "=" * 60)
    print("📡 实时监控能力演示")
    print("=" * 60)
    
    print("🔄 开始5分钟实时监控演示...")
    print("(实际使用中可以7x24小时监控)")
    
    system = EnhancedTradingSystem(symbol="BTC/USDT", exchange="gate")
    
    for i in range(3):  # 演示3次检测
        print(f"\n⏰ 第{i+1}次检测 ({datetime.now().strftime('%H:%M:%S')})")
        
        decision = system.analyze_trading_opportunity()
        
        print(f"   信号: {decision.action.upper()}")
        print(f"   置信度: {decision.confidence:.1%}")
        print(f"   大周期: {decision.major_trend_status['status'].upper()}")
        
        if decision.action != 'wait':
            print(f"   🚨 检测到交易信号！")
            print(f"   建议: {decision.action.upper()} @ ${decision.entry_price:,.2f}")
        else:
            print(f"   ✅ 继续等待中...")
        
        if i < 2:  # 不在最后一次等待
            print("   等待30秒后继续监控...")
            time.sleep(30)
    
    print(f"\n✅ 实时监控演示完成")
    print("💡 系统具备持续监控和实时信号检测能力")

if __name__ == "__main__":
    # 测试实时买卖点判断
    decision = test_real_time_signals()
    
    # 测试信号质量
    test_signal_quality()
    
    # 实时监控演示
    real_time_monitoring_demo()
    
    print(f"\n" + "=" * 80)
    print("🎯 实时买卖点判断能力测试完成")
    print("=" * 80)
    
    # 最终结论
    if decision.action != 'wait':
        print(f"✅ 系统当前检测到 {decision.action.upper()} 信号")
        print(f"📊 信号置信度: {decision.confidence:.1%}")
        print(f"💡 系统完全具备实时买卖点判断能力！")
    else:
        print(f"⏸️ 系统当前建议等待")
        print(f"📊 大周期状态: {decision.major_trend_status['status'].upper()}")
        print(f"💡 系统正常运行，等待合适的交易机会！")
    
    print(f"\n🚀 结论: 系统已经具备完整的实时买卖点判断能力！")

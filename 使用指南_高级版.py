#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多级别联立买卖点系统 - 高级使用指南
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_trading_system import EnhancedTradingSystem
from unified_signal_analyzer import UnifiedSignalAnalyzer
from probability_optimizer import ProbabilityOptimizer
from stop_profit_validator import StopProfitValidator

def advanced_analysis_demo():
    """高级分析演示"""
    print("=" * 80)
    print("🔬 多级别联立买卖点系统 - 高级分析")
    print("=" * 80)
    
    # 1. 分别使用各个模块进行深度分析
    symbol = "BTC/USDT"
    exchange = "gate"
    
    print("\n1️⃣ 初始化各分析模块...")
    
    # 统一信号分析器
    signal_analyzer = UnifiedSignalAnalyzer(symbol=symbol, exchange=exchange)
    
    # 概率优化器
    prob_optimizer = ProbabilityOptimizer(symbol=symbol, exchange=exchange)
    
    # 止盈止损验证器
    validator = StopProfitValidator(symbol=symbol, exchange=exchange)
    
    print("✅ 所有模块初始化完成")
    
    # 2. 深度大周期趋势分析
    print("\n2️⃣ 深度大周期趋势分析...")
    trend_status = signal_analyzer.get_major_trend_status()
    
    print("🔍 大周期趋势详细分析:")
    print(f"   总体状态: {trend_status['status'].upper()}")
    print(f"   置信度: {trend_status['confidence']:.1%}")
    print(f"   变盘状态: {'即将变盘' if trend_status.get('is_changing', False) else '趋势稳定'}")
    
    if 'level_trends' in trend_status:
        print("   各级别详情:")
        level_weights = {
            '1h': 0.6, '30m': 0.4, '15m': 0.3, '5m': 0.2, '1m': 0.1
        }
        
        for timeframe, trend in trend_status['level_trends'].items():
            weight = level_weights.get(timeframe, 0.1)
            print(f"     {timeframe:>3}: {trend.upper():>8} (权重: {weight:.1f})")
    
    print(f"   判断理由: {trend_status.get('reason', '无详细理由')}")
    
    # 3. 统一信号分析
    print("\n3️⃣ 统一信号深度分析...")
    unified_signal = signal_analyzer.analyze_unified_signals()
    
    if unified_signal:
        print("📊 统一信号详情:")
        print(f"   动作: {unified_signal.action.upper()}")
        print(f"   信号类型: {unified_signal.signal_type}")
        print(f"   强度: {unified_signal.strength.value}")
        print(f"   置信度: {unified_signal.confidence:.1%}")
        print(f"   主要周期: {unified_signal.timeframe}")
        print(f"   有效期: {unified_signal.validity_period}分钟")
        
        if unified_signal.supporting_timeframes:
            print(f"   支持周期: {', '.join(unified_signal.supporting_timeframes)}")
        
        if unified_signal.conflicting_timeframes:
            print(f"   冲突周期: {', '.join(unified_signal.conflicting_timeframes)}")
        
        print(f"   详细理由: {unified_signal.reasoning}")
    else:
        print("❌ 当前无有效统一信号")
    
    # 4. 止盈止损验证（如果有交易信号）
    if unified_signal and unified_signal.action in ['buy', 'sell']:
        print("\n4️⃣ 止盈止损深度验证...")
        
        validation_report = validator.validate_stop_profit_levels(
            entry_price=unified_signal.entry_price,
            stop_loss=unified_signal.stop_loss,
            take_profits=unified_signal.take_profits,
            timeframe=unified_signal.timeframe
        )
        
        print("🔍 验证报告详情:")
        print(f"   总体结果: {validation_report.overall_result.value.upper()}")
        print(f"   可达性评分: {validation_report.achievability_score:.2f}")
        print(f"   风险收益比: {validation_report.risk_reward_ratio:.2f}")
        print(f"   历史成功率: {validation_report.historical_success_rate:.1%}")
        
        # 止损分析
        stop_loss_target = validation_report.stop_loss
        print(f"\n   止损分析:")
        print(f"     价格: ${stop_loss_target.price:,.2f}")
        print(f"     幅度: {stop_loss_target.percentage:.1f}%")
        print(f"     置信度: {stop_loss_target.confidence:.1%}")
        print(f"     评估: {stop_loss_target.reasoning}")
        
        # 止盈分析
        print(f"\n   止盈分析:")
        for target in validation_report.profit_targets:
            print(f"     第{target.level}目标: ${target.price:,.2f} ({target.percentage:.1f}%)")
            print(f"       置信度: {target.confidence:.1%}")
            print(f"       预期时间: {target.time_expectation}分钟")
            print(f"       评估: {target.reasoning}")
        
        # 建议和警告
        if validation_report.recommendations:
            print(f"\n   专业建议:")
            for rec in validation_report.recommendations:
                print(f"     • {rec}")
        
        if validation_report.warnings:
            print(f"\n   风险警告:")
            for warning in validation_report.warnings:
                print(f"     • {warning}")
    
    return {
        'trend_status': trend_status,
        'unified_signal': unified_signal,
        'validation_report': validation_report if unified_signal and unified_signal.action in ['buy', 'sell'] else None
    }

def parameter_customization_demo():
    """参数自定义演示"""
    print("\n" + "=" * 80)
    print("⚙️ 参数自定义配置")
    print("=" * 80)
    
    print("\n🔧 系统参数配置选项:")
    
    # 1. 增强交易系统配置
    print("\n1️⃣ 增强交易系统配置:")
    system_config = {
        'min_confidence': 0.6,        # 最小置信度要求
        'max_risk_level': 'MEDIUM',   # 最大可接受风险等级
        'require_validation': True,    # 是否需要止盈止损验证
        'enable_major_trend_filter': True,  # 是否启用大周期过滤
    }
    
    for key, value in system_config.items():
        print(f"   {key}: {value}")
    
    print("\n   配置说明:")
    print("   • min_confidence: 低于此置信度的信号将被过滤")
    print("   • max_risk_level: 超过此风险等级的信号将被拒绝")
    print("   • require_validation: 是否必须通过止盈止损验证")
    print("   • enable_major_trend_filter: 是否只允许与大周期一致的信号")
    
    # 2. 统一信号分析器配置
    print("\n2️⃣ 统一信号分析器配置:")
    signal_config = {
        'timeframes': ['5m', '15m', '30m', '1h', '4h', '1d'],
        'major_timeframes': ['1h', '4h', '1d'],
        'minor_timeframes': ['5m', '15m', '30m'],
        'timeframe_weights': {
            '1d': 1.0, '4h': 0.8, '1h': 0.6,
            '30m': 0.4, '15m': 0.3, '5m': 0.2
        }
    }
    
    print("   时间周期配置:")
    for key, value in signal_config.items():
        if key == 'timeframe_weights':
            print(f"   {key}:")
            for tf, weight in value.items():
                print(f"     {tf}: {weight}")
        else:
            print(f"   {key}: {value}")
    
    # 3. 概率优化器配置
    print("\n3️⃣ 概率优化器配置:")
    prob_config = {
        'method': 'adaptive',  # traditional/ml/ensemble/adaptive
        'dynamic_weights': {
            'structure_analysis': 0.25,
            'volume_analysis': 0.15,
            'multi_level_joint': 0.20,
            'technical_momentum': 0.15,
            'market_sentiment': 0.10,
            'time_cycle': 0.10,
            'historical_feedback': 0.05
        },
        'probability_thresholds': {
            'very_high': 0.75,
            'high': 0.60,
            'medium': 0.45,
            'low': 0.30,
            'very_low': 0.15
        }
    }
    
    print(f"   计算方法: {prob_config['method']}")
    print("   动态权重:")
    for factor, weight in prob_config['dynamic_weights'].items():
        print(f"     {factor}: {weight:.2f}")
    
    # 4. 止盈止损验证器配置
    print("\n4️⃣ 止盈止损验证器配置:")
    validator_config = {
        'lookback_days': 30,
        'min_samples': 50,
        'volatility_threshold': 0.05,
        'time_decay_factor': 0.95,
        'confidence_threshold': 0.7
    }
    
    for key, value in validator_config.items():
        print(f"   {key}: {value}")
    
    print("\n💡 自定义配置示例:")
    print("""
    # 保守配置
    conservative_config = {
        'min_confidence': 0.8,        # 更高的置信度要求
        'max_risk_level': 'LOW',      # 只接受低风险信号
        'require_validation': True,    # 必须验证
    }
    
    # 激进配置
    aggressive_config = {
        'min_confidence': 0.5,        # 较低的置信度要求
        'max_risk_level': 'HIGH',     # 接受高风险信号
        'require_validation': False,   # 不强制验证
    }
    """)

def monitoring_and_alerts_demo():
    """监控和报警演示"""
    print("\n" + "=" * 80)
    print("📡 监控和报警系统")
    print("=" * 80)
    
    print("\n🔍 实时监控功能:")
    
    monitoring_features = [
        "📊 多级别趋势状态监控",
        "🎯 买卖信号实时检测",
        "⚠️ 风险等级变化报警",
        "🔄 大周期变盘预警",
        "📈 概率变化追踪",
        "🛡️ 止盈止损触发监控"
    ]
    
    for feature in monitoring_features:
        print(f"   • {feature}")
    
    print("\n📱 报警触发条件:")
    
    alert_conditions = {
        "🟢 买入信号": [
            "大周期确认看多(置信度>60%)",
            "小周期出现买入机会",
            "多级别共振(≥3个级别一致)",
            "信号置信度≥设定阈值"
        ],
        "🔴 卖出信号": [
            "大周期确认看空(置信度>60%)",
            "小周期出现卖出机会",
            "多级别共振(≥3个级别一致)",
            "信号置信度≥设定阈值"
        ],
        "⚠️ 风险警告": [
            "大周期即将变盘",
            "多级别严重分歧",
            "止损位被触发",
            "市场异常波动"
        ],
        "🔄 状态变化": [
            "趋势方向改变",
            "风险等级调整",
            "概率显著变化",
            "新的技术形态出现"
        ]
    }
    
    for condition_type, conditions in alert_conditions.items():
        print(f"\n{condition_type}:")
        for condition in conditions:
            print(f"   • {condition}")
    
    print("\n💡 监控最佳实践:")
    best_practices = [
        "设置合理的报警阈值，避免过度频繁",
        "关注大周期变化，及时调整策略",
        "监控多级别共振情况，把握最佳时机",
        "跟踪历史准确率，持续优化参数",
        "结合基本面消息，综合判断市场"
    ]
    
    for practice in best_practices:
        print(f"   • {practice}")

def performance_optimization_demo():
    """性能优化演示"""
    print("\n" + "=" * 80)
    print("🚀 性能优化指南")
    print("=" * 80)
    
    print("\n⚡ 系统性能优化:")
    
    optimization_tips = {
        "🔧 参数优化": [
            "根据历史回测结果调整权重",
            "优化置信度阈值设置",
            "调整时间周期组合",
            "微调概率计算参数"
        ],
        "📊 数据优化": [
            "使用足够的历史数据(建议≥30天)",
            "定期清理过期数据",
            "优化数据获取频率",
            "缓存常用计算结果"
        ],
        "🎯 策略优化": [
            "根据市场特征调整策略",
            "优化止盈止损设置",
            "改进信号过滤条件",
            "增强风险控制机制"
        ],
        "🔄 持续改进": [
            "定期评估系统表现",
            "收集用户反馈",
            "跟踪市场变化",
            "更新算法模型"
        ]
    }
    
    for category, tips in optimization_tips.items():
        print(f"\n{category}:")
        for tip in tips:
            print(f"   • {tip}")
    
    print("\n📈 性能评估指标:")
    
    metrics = [
        "信号准确率 (目标: >70%)",
        "风险收益比 (目标: >2:1)",
        "最大回撤控制 (目标: <5%)",
        "信号及时性 (目标: <5分钟延迟)",
        "系统稳定性 (目标: >99%可用性)"
    ]
    
    for metric in metrics:
        print(f"   • {metric}")

if __name__ == "__main__":
    # 运行高级分析演示
    analysis_results = advanced_analysis_demo()
    
    # 参数自定义演示
    parameter_customization_demo()
    
    # 监控和报警演示
    monitoring_and_alerts_demo()
    
    # 性能优化演示
    performance_optimization_demo()
    
    print("\n" + "=" * 80)
    print("✅ 高级使用指南演示完成")
    print("=" * 80)
    print("\n🎓 您现在已经掌握了系统的高级使用方法！")
    print("💡 建议：从基础配置开始，逐步根据实际效果调整参数。")

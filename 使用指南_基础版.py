#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多级别联立买卖点系统 - 基础使用指南
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_trading_system import EnhancedTradingSystem

def basic_usage_demo():
    """基础使用演示"""
    print("=" * 60)
    print("🚀 多级别联立买卖点系统 - 基础使用")
    print("=" * 60)
    
    # 1. 初始化系统
    print("\n1️⃣ 初始化系统...")
    system = EnhancedTradingSystem(symbol="BTC/USDT", exchange="gate")
    print("✅ 系统初始化完成")
    
    # 2. 分析交易机会
    print("\n2️⃣ 分析当前交易机会...")
    decision = system.analyze_trading_opportunity()
    
    # 3. 解读分析结果
    print("\n3️⃣ 分析结果解读:")
    print("=" * 40)
    
    # 基本信息
    print(f"📊 交易建议: {decision.action.upper()}")
    print(f"🎯 置信度: {decision.confidence:.1%}")
    print(f"⚠️ 风险等级: {decision.risk_level}")
    
    # 大周期趋势分析
    major_trend = decision.major_trend_status
    print(f"\n🔍 大周期分析:")
    print(f"   趋势状态: {major_trend['status'].upper()}")
    print(f"   置信度: {major_trend['confidence']:.1%}")
    print(f"   是否变盘: {'是' if major_trend.get('is_changing', False) else '否'}")
    
    # 各级别趋势
    if 'level_trends' in major_trend:
        print(f"   各级别趋势:")
        for timeframe, trend in major_trend['level_trends'].items():
            print(f"     {timeframe}: {trend.upper()}")
    
    # 交易信号详情
    if decision.action in ['buy', 'sell']:
        print(f"\n💰 交易信号详情:")
        print(f"   入场价格: ${decision.entry_price:,.2f}")
        print(f"   止损价格: ${decision.stop_loss:,.2f}")
        print(f"   止盈目标: {[f'${tp:,.2f}' for tp in decision.take_profits]}")
        print(f"   仓位建议: {decision.position_size}")
        print(f"   主要周期: {decision.timeframe}")
        
        # 止盈止损验证
        if decision.validation_report:
            report = decision.validation_report
            print(f"\n🔍 验证结果:")
            print(f"   可达性: {report.overall_result.value.upper()}")
            print(f"   评分: {report.achievability_score:.2f}")
            print(f"   风险收益比: {report.risk_reward_ratio:.2f}")
            print(f"   历史成功率: {report.historical_success_rate:.1%}")
    
    # 决策理由
    print(f"\n📝 决策理由:")
    print(f"   {decision.reasoning}")
    
    # 警告和建议
    if decision.warnings:
        print(f"\n⚠️ 警告:")
        for warning in decision.warnings:
            print(f"   • {warning}")
    
    if decision.recommendations:
        print(f"\n💡 建议:")
        for rec in decision.recommendations:
            print(f"   • {rec}")
    
    return decision

def interpret_signals(decision):
    """信号解读指南"""
    print("\n" + "=" * 60)
    print("📖 信号解读指南")
    print("=" * 60)
    
    action = decision.action
    confidence = decision.confidence
    risk_level = decision.risk_level
    
    print(f"\n🎯 当前信号: {action.upper()}")
    
    if action == 'buy':
        print("📈 买入信号解读:")
        print("   • 系统检测到多级别看多机会")
        print("   • 大周期趋势支持上涨")
        print("   • 小周期出现买入时机")
        
        if confidence >= 0.7:
            print("   ✅ 高置信度信号，可考虑操作")
        elif confidence >= 0.5:
            print("   ⚠️ 中等置信度，谨慎操作")
        else:
            print("   ❌ 低置信度，建议等待")
            
    elif action == 'sell':
        print("📉 卖出信号解读:")
        print("   • 系统检测到多级别看空机会")
        print("   • 大周期趋势支持下跌")
        print("   • 小周期出现卖出时机")
        
        if confidence >= 0.7:
            print("   ✅ 高置信度信号，可考虑操作")
        elif confidence >= 0.5:
            print("   ⚠️ 中等置信度，谨慎操作")
        else:
            print("   ❌ 低置信度，建议等待")
            
    else:  # wait
        print("⏸️ 等待信号解读:")
        print("   • 当前市场条件不适合操作")
        
        major_trend = decision.major_trend_status
        if major_trend.get('is_changing', False):
            print("   • 大周期即将变盘，建议空仓等待")
        elif major_trend['confidence'] < 0.5:
            print("   • 大周期方向不明确，等待确认")
        else:
            print("   • 缺乏有效的小周期入场机会")
    
    # 风险等级解读
    print(f"\n⚠️ 风险等级: {risk_level}")
    if risk_level == 'LOW':
        print("   🟢 低风险：适合保守投资者")
    elif risk_level == 'MEDIUM':
        print("   🟡 中等风险：适合稳健投资者")
    else:  # HIGH
        print("   🔴 高风险：仅适合激进投资者，需严格止损")

def usage_best_practices():
    """使用最佳实践"""
    print("\n" + "=" * 60)
    print("💡 使用最佳实践")
    print("=" * 60)
    
    practices = [
        "🎯 信号确认",
        [
            "等待置信度≥60%的信号",
            "确认大周期方向明确",
            "验证多级别共振",
            "检查止盈止损合理性"
        ],
        
        "⚠️ 风险控制",
        [
            "严格执行止损，不抱侥幸心理",
            "根据风险等级调整仓位大小",
            "避免在变盘期操作",
            "单次风险不超过总资金的2%"
        ],
        
        "📊 操作纪律",
        [
            "只在系统给出明确信号时操作",
            "不要因为错过机会而追涨杀跌",
            "保持耐心，等待高质量机会",
            "记录每次操作，总结经验教训"
        ],
        
        "🔄 持续优化",
        [
            "定期回顾操作结果",
            "根据市场变化调整参数",
            "关注系统更新和改进",
            "结合基本面分析辅助判断"
        ]
    ]
    
    for i in range(0, len(practices), 2):
        category = practices[i]
        items = practices[i + 1]
        
        print(f"\n{category}:")
        for item in items:
            print(f"   • {item}")

def common_scenarios():
    """常见场景处理"""
    print("\n" + "=" * 60)
    print("🎭 常见场景处理")
    print("=" * 60)
    
    scenarios = {
        "🟢 理想信号": {
            "特征": [
                "大周期方向明确(置信度>70%)",
                "多级别共振(3个以上级别一致)",
                "小周期出现标准买卖点",
                "止盈止损验证通过"
            ],
            "操作": [
                "可以按建议仓位操作",
                "严格执行止盈止损",
                "密切关注市场变化"
            ]
        },
        
        "🟡 一般信号": {
            "特征": [
                "大周期方向基本明确(置信度50-70%)",
                "部分级别共振",
                "信号质量中等",
                "存在一定风险因素"
            ],
            "操作": [
                "减少仓位操作",
                "更严格的止损",
                "密切监控信号变化"
            ]
        },
        
        "🔴 等待信号": {
            "特征": [
                "大周期方向不明确",
                "多级别分歧严重",
                "即将变盘",
                "缺乏有效入场点"
            ],
            "操作": [
                "空仓等待",
                "持续监控市场",
                "等待更好机会"
            ]
        }
    }
    
    for scenario, details in scenarios.items():
        print(f"\n{scenario}:")
        print("   特征:")
        for feature in details["特征"]:
            print(f"     • {feature}")
        print("   操作建议:")
        for action in details["操作"]:
            print(f"     • {action}")

if __name__ == "__main__":
    # 运行基础使用演示
    decision = basic_usage_demo()
    
    # 信号解读
    interpret_signals(decision)
    
    # 最佳实践
    usage_best_practices()
    
    # 常见场景
    common_scenarios()
    
    print("\n" + "=" * 60)
    print("✅ 使用指南演示完成")
    print("=" * 60)
    print("\n💡 记住：系统是辅助工具，最终决策需要结合您的投资经验和风险承受能力！")

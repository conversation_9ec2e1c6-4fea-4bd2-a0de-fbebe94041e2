#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调优信号生成器
基于对比回测结果，调整优化参数，平衡信号质量和数量
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from 优化信号生成器 import OptimizedSignalGenerator, OptimizedSignal

class TunedSignalGenerator(OptimizedSignalGenerator):
    """调优信号生成器 - 基于回测结果优化参数"""
    
    def __init__(self):
        super().__init__()
        
        # 基于回测结果调整配置
        self.config = {
            'min_confidence': 0.58,        # 降低置信度要求 (从0.65降到0.58)
            'min_conditions': 1,           # 降低最少条件数 (从2降到1)
            'sell_signal_boost': 0.10,     # 降低卖出信号额外要求 (从0.15降到0.10)
            'volume_threshold': 1.3,       # 降低成交量阈值 (从1.5降到1.3)
            'atr_multiplier': 2.0,         # 降低ATR止损倍数 (从2.5降到2.0)
            'enable_fractal_signals': True, # 启用分型信号
            'enable_bi_signals': True,     # 启用笔信号
        }
        
        print("🔧 调优信号生成器配置:")
        print(f"   最低置信度: {self.config['min_confidence']} (降低)")
        print(f"   最少条件数: {self.config['min_conditions']} (降低)")
        print(f"   卖出信号额外要求: {self.config['sell_signal_boost']} (降低)")
        print(f"   成交量阈值: {self.config['volume_threshold']} (降低)")
        print(f"   ATR止损倍数: {self.config['atr_multiplier']} (降低)")
    
    def generate_optimized_signal(self, data: pd.DataFrame, chan_analyzer, 
                                timestamp: datetime) -> Optional[OptimizedSignal]:
        """
        生成调优后的信号
        
        调优策略:
        1. 降低门槛，增加信号数量
        2. 保持质量控制，但不过度过滤
        3. 特别优化卖出信号验证
        4. 平衡风险和收益
        """
        try:
            latest_price = data['close'].iloc[-1]
            
            # 1. 基础缠论分析 (更宽松的条件)
            base_signal = self._get_enhanced_base_signal(chan_analyzer, data)
            if not base_signal:
                return self._create_wait_signal(latest_price, timestamp, "无有效缠论信号")
            
            # 2. 多条件验证 (降低要求)
            conditions = self._check_relaxed_conditions(data, chan_analyzer, base_signal['action'])
            
            # 3. 信号质量评分 (调整权重)
            quality_score = self._calculate_balanced_quality_score(conditions, base_signal)
            
            # 4. 卖出信号特殊处理 (降低要求但保持验证)
            if base_signal['action'] == 'sell':
                sell_conditions = self._relaxed_sell_validation(data, chan_analyzer)
                conditions.update(sell_conditions)
                quality_score = self._adjust_sell_quality_score(quality_score, sell_conditions)
            
            # 5. 最终决策 (更灵活的标准)
            final_signal = self._make_balanced_decision(
                base_signal, conditions, quality_score, data, timestamp
            )
            
            return final_signal
            
        except Exception as e:
            return self._create_wait_signal(latest_price, timestamp, f"信号生成失败: {str(e)}")
    
    def _get_enhanced_base_signal(self, chan_analyzer, data: pd.DataFrame) -> Optional[Dict]:
        """获取增强的基础信号 (更多信号源)"""
        try:
            # 1. 线段终结信号 (保持)
            is_ended, end_reason = chan_analyzer.is_xd_ended()
            
            if is_ended and chan_analyzer.xd_list:
                latest_xd = chan_analyzer.xd_list[-1]
                xd_direction = latest_xd[4]
                
                if xd_direction == 'up':
                    return {
                        'action': 'sell',
                        'confidence': 0.7,
                        'reasoning': f"线段终结: {end_reason.get('原因', '多重信号确认')}",
                        'signal_type': 'xd_end',
                        'priority': 'high'
                    }
                elif xd_direction == 'down':
                    return {
                        'action': 'buy', 
                        'confidence': 0.7,
                        'reasoning': f"线段终结: {end_reason.get('原因', '多重信号确认')}",
                        'signal_type': 'xd_end',
                        'priority': 'high'
                    }
            
            # 2. 分型信号 (放宽时间限制)
            if self.config['enable_fractal_signals'] and chan_analyzer.fx_list:
                recent_fx = [fx for fx in chan_analyzer.fx_list if len(data) - fx[0] <= 5]  # 扩展到5个K线
                
                if recent_fx:
                    latest_fx = recent_fx[-1]
                    fx_type = latest_fx[1]
                    
                    if fx_type == 'bottom':
                        return {
                            'action': 'buy',
                            'confidence': 0.6,
                            'reasoning': "检测到底分型",
                            'signal_type': 'fractal',
                            'priority': 'medium'
                        }
                    elif fx_type == 'top':
                        return {
                            'action': 'sell',
                            'confidence': 0.6,
                            'reasoning': "检测到顶分型", 
                            'signal_type': 'fractal',
                            'priority': 'medium'
                        }
            
            # 3. 笔信号 (新增)
            if self.config['enable_bi_signals'] and len(chan_analyzer.bi_list) >= 2:
                last_bi = chan_analyzer.bi_list[-1]
                prev_bi = chan_analyzer.bi_list[-2]
                
                # 检查笔的方向变化
                if last_bi[4] != prev_bi[4]:
                    if last_bi[4] == 'up':
                        return {
                            'action': 'buy',
                            'confidence': 0.55,
                            'reasoning': "笔方向转为上升",
                            'signal_type': 'bi_change',
                            'priority': 'low'
                        }
                    elif last_bi[4] == 'down':
                        return {
                            'action': 'sell',
                            'confidence': 0.55,
                            'reasoning': "笔方向转为下降",
                            'signal_type': 'bi_change',
                            'priority': 'low'
                        }
            
            return None
            
        except Exception as e:
            return None
    
    def _check_relaxed_conditions(self, data: pd.DataFrame, chan_analyzer, action: str) -> Dict[str, Any]:
        """检查放宽的条件"""
        conditions = {}
        
        try:
            # 1. 成交量确认 (降低阈值)
            if len(data) >= 20:
                current_volume = data['volume'].iloc[-1]
                avg_volume = data['volume'].rolling(20).mean().iloc[-1]
                
                if current_volume > avg_volume * self.config['volume_threshold']:
                    conditions['volume_confirm'] = True
                    conditions['volume_ratio'] = current_volume / avg_volume
                else:
                    conditions['volume_confirm'] = False
                    conditions['volume_ratio'] = current_volume / avg_volume
            
            # 2. 技术指标确认 (放宽标准)
            conditions.update(self._check_relaxed_technical_indicators(data, action))
            
            # 3. 趋势确认 (简化)
            conditions.update(self._check_simple_trend_confirmation(data, action))
            
            return conditions
            
        except Exception as e:
            return {'error': str(e)}
    
    def _check_relaxed_technical_indicators(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """检查放宽的技术指标"""
        try:
            conditions = {}
            
            if len(data) >= 14:
                # RSI (放宽标准)
                delta = data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                current_rsi = rsi.iloc[-1]
                
                if action == 'buy' and current_rsi < 50:  # 从40放宽到50
                    conditions['rsi_support'] = True
                elif action == 'sell' and current_rsi > 50:  # 从60放宽到50
                    conditions['rsi_support'] = True
                else:
                    conditions['rsi_support'] = False
                
                conditions['rsi_value'] = current_rsi
            
            return conditions
            
        except Exception as e:
            return {'technical_error': str(e)}
    
    def _check_simple_trend_confirmation(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """简化的趋势确认"""
        try:
            if len(data) < 5:
                return {'trend_confirmed': False}
            
            # 简单趋势确认：最近3个收盘价的趋势 (从5个减少到3个)
            recent_closes = data['close'].tail(3)
            
            if action == 'buy':
                trend_up = (recent_closes.iloc[-1] > recent_closes.iloc[0])
                return {'trend_confirmed': trend_up, 'trend_direction': 'up' if trend_up else 'down'}
            elif action == 'sell':
                trend_down = (recent_closes.iloc[-1] < recent_closes.iloc[0])
                return {'trend_confirmed': trend_down, 'trend_direction': 'down' if trend_down else 'up'}
            
            return {'trend_confirmed': False}
            
        except Exception as e:
            return {'trend_error': str(e)}
    
    def _relaxed_sell_validation(self, data: pd.DataFrame, chan_analyzer) -> Dict[str, Any]:
        """放宽的卖出信号验证"""
        sell_conditions = {}
        
        try:
            # 1. 背驰确认 (保持，但降低要求)
            sell_conditions.update(self._check_simple_divergence(data))
            
            # 2. 阻力位确认 (放宽距离)
            sell_conditions.update(self._check_relaxed_resistance_level(data))
            
            # 3. 简化的趋势强度确认
            sell_conditions.update(self._check_simple_trend_strength(data, 'sell'))
            
            return sell_conditions
            
        except Exception as e:
            return {'sell_validation_error': str(e)}
    
    def _check_simple_divergence(self, data: pd.DataFrame) -> Dict[str, Any]:
        """简化的背驰检查"""
        try:
            if len(data) < 20:  # 从26降到20
                return {'divergence': False, 'reason': '数据不足'}
            
            # 简化的MACD背驰检查
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            macd = exp1 - exp2
            
            # 检查最近5个点的背驰 (从10个减少到5个)
            recent_prices = data['close'].tail(5)
            recent_macd = macd.tail(5)
            
            price_trend = recent_prices.iloc[-1] > recent_prices.iloc[0]
            macd_trend = recent_macd.iloc[-1] > recent_macd.iloc[0]
            
            if price_trend != macd_trend:
                return {
                    'divergence': True,
                    'type': 'simple_macd_divergence',
                    'strength': abs(recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
                }
            
            return {'divergence': False, 'reason': '无背驰'}
            
        except Exception as e:
            return {'divergence': False, 'error': str(e)}
    
    def _check_relaxed_resistance_level(self, data: pd.DataFrame) -> Dict[str, Any]:
        """放宽的阻力位检查"""
        try:
            current_price = data['close'].iloc[-1]
            
            # 寻找近期高点作为阻力位
            recent_highs = data['high'].rolling(5).max().tail(10)  # 缩小范围
            resistance_levels = recent_highs.unique()
            resistance_levels = resistance_levels[resistance_levels > current_price]
            
            if len(resistance_levels) > 0:
                nearest_resistance = min(resistance_levels)
                distance_to_resistance = (nearest_resistance - current_price) / current_price
                
                # 放宽距离要求到5%
                if distance_to_resistance <= 0.05:
                    return {
                        'near_resistance': True,
                        'resistance_level': nearest_resistance,
                        'distance_pct': distance_to_resistance * 100
                    }
            
            return {'near_resistance': False}
            
        except Exception as e:
            return {'resistance_error': str(e)}
    
    def _check_simple_trend_strength(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """简化的趋势强度检查"""
        try:
            if len(data) < 10:
                return {'trend_strength': 'unknown'}
            
            # 简化的移动平均线检查
            ma10 = data['close'].rolling(10).mean()
            current_price = data['close'].iloc[-1]
            
            if action == 'sell':
                if current_price < ma10.iloc[-1]:
                    return {'trend_strength': 'down', 'alignment': True}
                else:
                    return {'trend_strength': 'up', 'alignment': False}
            
            elif action == 'buy':
                if current_price > ma10.iloc[-1]:
                    return {'trend_strength': 'up', 'alignment': True}
                else:
                    return {'trend_strength': 'down', 'alignment': False}
            
            return {'trend_strength': 'neutral'}
            
        except Exception as e:
            return {'trend_error': str(e)}
    
    def _calculate_balanced_quality_score(self, conditions: Dict, base_signal: Dict) -> float:
        """计算平衡的质量评分"""
        try:
            score = base_signal['confidence']  # 基础分数
            
            # 根据信号优先级调整
            priority = base_signal.get('priority', 'medium')
            if priority == 'high':
                score += 0.05
            elif priority == 'low':
                score -= 0.05
            
            # 成交量加分 (降低权重)
            if conditions.get('volume_confirm', False):
                score += 0.05  # 从0.1降到0.05
            
            # 技术指标加分
            if conditions.get('rsi_support', False):
                score += 0.03  # 从0.05降到0.03
            
            # 趋势确认加分
            if conditions.get('trend_confirmed', False):
                score += 0.05  # 从0.1降到0.05
            
            return min(1.0, score)
            
        except Exception as e:
            return 0.5
    
    def _make_balanced_decision(self, base_signal: Dict, conditions: Dict, quality_score: float,
                              data: pd.DataFrame, timestamp: datetime) -> OptimizedSignal:
        """做出平衡的最终决策"""
        try:
            # 检查是否满足调优后的要求
            conditions_met = []
            
            if conditions.get('volume_confirm', False):
                conditions_met.append('成交量确认')
            if conditions.get('trend_confirmed', False):
                conditions_met.append('趋势确认')
            if conditions.get('rsi_support', False):
                conditions_met.append('RSI支持')
            if conditions.get('divergence', False):
                conditions_met.append('背驰确认')
            if conditions.get('near_resistance', False):
                conditions_met.append('阻力位确认')
            
            # 调优后的决策逻辑
            if quality_score < self.config['min_confidence']:
                return self._create_wait_signal(
                    data['close'].iloc[-1], timestamp, 
                    f"信号质量不足: {quality_score:.2f} < {self.config['min_confidence']}"
                )
            
            if len(conditions_met) < self.config['min_conditions']:
                return self._create_wait_signal(
                    data['close'].iloc[-1], timestamp,
                    f"满足条件不足: {len(conditions_met)} < {self.config['min_conditions']}"
                )
            
            # 卖出信号的调优检查
            if base_signal['action'] == 'sell':
                sell_score_boost = self.config['sell_signal_boost']
                if quality_score < self.config['min_confidence'] + sell_score_boost:
                    return self._create_wait_signal(
                        data['close'].iloc[-1], timestamp,
                        f"卖出信号质量不足: {quality_score:.2f} < {self.config['min_confidence'] + sell_score_boost}"
                    )
            
            # 生成调优信号
            return self._create_tuned_signal(
                base_signal, conditions, quality_score, conditions_met, data, timestamp
            )
            
        except Exception as e:
            return self._create_wait_signal(
                data['close'].iloc[-1], timestamp, f"决策失败: {str(e)}"
            )
    
    def _create_tuned_signal(self, base_signal: Dict, conditions: Dict, quality_score: float,
                           conditions_met: list, data: pd.DataFrame, timestamp: datetime) -> OptimizedSignal:
        """创建调优信号"""
        try:
            entry_price = data['close'].iloc[-1]
            
            # 调优的止损计算
            atr_value = conditions.get('atr_value', entry_price * 0.015)  # 降低默认ATR
            
            if base_signal['action'] == 'buy':
                stop_loss = entry_price - atr_value * self.config['atr_multiplier']
                take_profits = [
                    entry_price + atr_value * 1.5,  # 降低止盈目标
                    entry_price + atr_value * 3,
                    entry_price + atr_value * 4.5
                ]
            else:  # sell
                stop_loss = entry_price + atr_value * self.config['atr_multiplier']
                take_profits = [
                    entry_price - atr_value * 1.5,
                    entry_price - atr_value * 3,
                    entry_price - atr_value * 4.5
                ]
            
            # 风险等级评估
            risk_level = 'LOW' if quality_score >= 0.75 else 'MEDIUM' if quality_score >= 0.6 else 'HIGH'
            
            # 详细理由
            reasoning = f"[调优] {base_signal['reasoning']}; 质量评分: {quality_score:.2f}; 满足条件: {', '.join(conditions_met)}"
            
            return OptimizedSignal(
                action=base_signal['action'],
                confidence=quality_score,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profits=take_profits,
                reasoning=reasoning,
                risk_level=risk_level,
                quality_score=quality_score,
                conditions_met=conditions_met
            )
            
        except Exception as e:
            return self._create_wait_signal(
                data['close'].iloc[-1], timestamp, f"调优信号创建失败: {str(e)}"
            )

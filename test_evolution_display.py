#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试走势推演显示功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from chart_plotter import ChartPlotter

def create_test_data():
    """创建测试数据"""
    # 生成历史数据（过去100个15分钟K线）
    start_time = datetime.now() - timedelta(hours=25)  # 25小时前
    times = [start_time + timedelta(minutes=15*i) for i in range(100)]
    
    # 生成价格数据（模拟BTC价格）
    base_price = 104500
    prices = []
    for i in range(100):
        # 添加趋势和随机波动
        trend = -200 * (i / 100)  # 轻微下跌趋势
        noise = np.random.normal(0, 50)  # 随机波动
        price = base_price + trend + noise
        prices.append(price)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'timestamp': times,
        'open': prices,
        'high': [p + np.random.uniform(10, 50) for p in prices],
        'low': [p - np.random.uniform(10, 50) for p in prices],
        'close': prices,
        'volume': [np.random.uniform(100, 1000) for _ in range(100)]
    })
    
    return df

def create_test_evolution_data(df):
    """创建测试推演数据"""
    current_time = df['timestamp'].iloc[-1]
    current_price = df['close'].iloc[-1]
    
    # 生成未来4个15分钟周期的推演数据
    evolution_points = []
    target_price = current_price * 0.95  # 5%下跌目标
    
    for i in range(1, 5):
        future_time = current_time + timedelta(minutes=15 * i)
        # 逐步向目标价格移动
        progress = i / 4
        projected_price = current_price + (target_price - current_price) * progress
        
        evolution_points.append({
            'timestamp': future_time,
            'price': projected_price,
            'scenario': 'down',
            'confidence': 0.75
        })
    
    # 模拟推演结果对象
    class MockScenario:
        def __init__(self):
            self.trend_type = type('TrendType', (), {'value': 'down'})()
            self.probability = 0.75
            self.target_price = target_price
    
    return {
        'timeframe': '15m',
        'current_price': current_price,
        'current_time': current_time,
        'evolution_points': evolution_points,
        'primary_scenario': MockScenario(),
        'alternative_scenarios': [],
        'target_price': target_price
    }

def test_evolution_display():
    """测试推演显示功能"""
    print("🧪 开始测试走势推演显示功能...")
    
    # 创建测试数据
    df = create_test_data()
    evolution_data = create_test_evolution_data(df)
    
    # 创建绘图器
    plotter = ChartPlotter()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(16, 8))
    fig.patch.set_facecolor('#161a2b')
    ax.set_facecolor('#161a2b')
    
    # 绘制历史价格线
    historical_dates = [mdates.date2num(t) for t in df['timestamp']]
    ax.plot(historical_dates, df['close'], 
            color=plotter.colors['price_line'], linewidth=2, 
            label='历史价格', zorder=5)
    
    # 准备推演数据
    current_time = evolution_data['current_time']
    current_price = evolution_data['current_price']
    evolution_points = evolution_data['evolution_points']
    
    # 推演时间和价格
    evolution_times = [current_time] + [point['timestamp'] for point in evolution_points]
    evolution_prices = [current_price] + [point['price'] for point in evolution_points]
    evolution_dates = [mdates.date2num(t) for t in evolution_times]
    
    # 绘制推演线（红色虚线表示下跌）
    scenario_color = '#ff0000'
    ax.plot(evolution_dates, evolution_prices, 
            color=scenario_color, linewidth=3, linestyle='--', 
            alpha=0.9, label='推演走势(DOWN)', zorder=10)
    
    # 绘制推演区域（置信区间）
    confidence = 0.75
    price_range = max(evolution_prices) - min(evolution_prices)
    uncertainty = (1 - confidence) * price_range * 0.2
    
    upper_prices = [p + uncertainty for p in evolution_prices]
    lower_prices = [p - uncertainty for p in evolution_prices]
    
    ax.fill_between(evolution_dates, lower_prices, upper_prices,
                    color=scenario_color, alpha=0.15, 
                    label=f'推演区间(置信度:{confidence:.1%})')
    
    # 添加推演起点标记
    ax.scatter([evolution_dates[0]], [evolution_prices[0]], 
               color='white', s=100, marker='o', 
               edgecolor=scenario_color, linewidth=2,
               label='推演起点', zorder=11)
    
    # 添加推演终点标记
    ax.scatter([evolution_dates[-1]], [evolution_prices[-1]], 
               color=scenario_color, s=150, marker='*', 
               edgecolor='white', linewidth=1,
               label='推演目标', zorder=11)
    
    # 添加当前价格线
    ax.axhline(y=current_price, color='yellow', linestyle=':', alpha=0.7, 
               label=f'当前价格: ${current_price:.2f}')
    
    # 添加推演信息文本
    text_x = evolution_dates[-1]
    text_y = evolution_prices[-1]
    
    info_text = (f"推演: DOWN\n"
                f"概率: {confidence:.1%}\n"
                f"目标: ${evolution_prices[-1]:,.2f}")
    
    ax.annotate(info_text, 
               xy=(text_x, text_y), 
               xytext=(15, 15), 
               textcoords='offset points',
               bbox=dict(boxstyle='round,pad=0.5', 
                       facecolor=scenario_color, 
                       alpha=0.8),
               fontsize=10, 
               color='white',
               weight='bold',
               zorder=12)
    
    # 设置图表样式
    ax.set_title('走势推演显示测试 - 推演线应该从当前点位开始', fontsize=16, color='white')
    ax.set_xlabel('时间', fontsize=12, color='white')
    ax.set_ylabel('价格 (USDT)', fontsize=12, color='white')
    ax.grid(True, linestyle=':', alpha=0.7, color='#2a2e3e')
    ax.legend(loc='upper right', fontsize=10)
    
    # 设置坐标轴颜色
    ax.tick_params(colors='white')
    ax.spines['bottom'].set_color('#2a2e3e')
    ax.spines['top'].set_color('#2a2e3e')
    ax.spines['right'].set_color('#2a2e3e')
    ax.spines['left'].set_color('#2a2e3e')
    
    # 设置x轴时间格式
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=4))
    plt.xticks(rotation=45)
    
    # 保存测试图表
    plt.tight_layout()
    plt.savefig('charts/evolution_display_test.png', dpi=300, bbox_inches='tight',
                facecolor='#161a2b', edgecolor='none')
    plt.close()
    
    print("✅ 推演显示测试图表已生成: charts/evolution_display_test.png")
    
    # 验证数据连续性
    print("\n📊 数据连续性验证:")
    print("=" * 50)
    print(f"历史数据最后时间: {df['timestamp'].iloc[-1]}")
    print(f"历史数据最后价格: ${df['close'].iloc[-1]:.2f}")
    print(f"推演起始时间: {current_time}")
    print(f"推演起始价格: ${current_price:.2f}")
    print(f"推演结束时间: {evolution_times[-1]}")
    print(f"推演目标价格: ${evolution_prices[-1]:.2f}")
    
    # 检查时间连续性
    time_gap = (evolution_times[1] - current_time).total_seconds() / 60
    print(f"推演时间间隔: {time_gap}分钟")
    
    if abs(df['close'].iloc[-1] - current_price) < 0.01:
        print("✅ 价格连续性: 正确")
    else:
        print("❌ 价格连续性: 错误")
    
    if time_gap == 15:
        print("✅ 时间连续性: 正确")
    else:
        print("❌ 时间连续性: 错误")

if __name__ == "__main__":
    test_evolution_display()

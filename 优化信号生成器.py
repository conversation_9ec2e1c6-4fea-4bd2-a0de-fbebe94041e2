#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化信号生成器
基于胜率分析结果，优化信号生成逻辑，目标胜率70%+
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from chan_analysis import ChanAnalysis

@dataclass
class OptimizedSignal:
    """优化后的信号"""
    action: str                    # buy/sell/wait
    confidence: float              # 置信度
    entry_price: float            # 入场价格
    stop_loss: float              # 止损价格
    take_profits: list            # 止盈目标
    reasoning: str                # 判断理由
    risk_level: str               # 风险等级
    quality_score: float          # 信号质量评分
    conditions_met: list          # 满足的条件列表

class OptimizedSignalGenerator:
    """优化信号生成器"""
    
    def __init__(self):
        self.config = {
            'min_confidence': 0.65,        # 提高最低置信度要求
            'min_conditions': 2,           # 最少满足条件数
            'sell_signal_boost': 0.15,     # 卖出信号额外要求
            'volume_threshold': 1.5,       # 成交量异常阈值
            'atr_multiplier': 2.5,         # ATR止损倍数
        }
    
    def generate_optimized_signal(self, data: pd.DataFrame, chan_analyzer: ChanAnalysis, 
                                timestamp: datetime) -> Optional[OptimizedSignal]:
        """
        生成优化后的信号
        
        基于胜率分析结果的改进:
        1. 提高信号质量门槛
        2. 特别优化卖出信号 (解决62.5%失败率问题)
        3. 动态止损策略
        4. 多条件验证
        """
        try:
            latest_price = data['close'].iloc[-1]
            
            # 1. 基础缠论分析
            base_signal = self._get_base_chan_signal(chan_analyzer, data)
            if not base_signal:
                return self._create_wait_signal(latest_price, timestamp, "无有效缠论信号")
            
            # 2. 多条件验证
            conditions = self._check_multiple_conditions(data, chan_analyzer, base_signal['action'])
            
            # 3. 信号质量评分
            quality_score = self._calculate_quality_score(conditions, base_signal)
            
            # 4. 特殊处理卖出信号 (解决62.5%失败率)
            if base_signal['action'] == 'sell':
                sell_conditions = self._enhanced_sell_validation(data, chan_analyzer)
                conditions.update(sell_conditions)
                quality_score = self._adjust_sell_quality_score(quality_score, sell_conditions)
            
            # 5. 最终信号决策
            final_signal = self._make_final_decision(
                base_signal, conditions, quality_score, data, timestamp
            )
            
            return final_signal
            
        except Exception as e:
            return self._create_wait_signal(latest_price, timestamp, f"信号生成失败: {str(e)}")
    
    def _get_base_chan_signal(self, chan_analyzer: ChanAnalysis, data: pd.DataFrame) -> Optional[Dict]:
        """获取基础缠论信号"""
        try:
            # 检查线段终结
            is_ended, end_reason = chan_analyzer.is_xd_ended()
            
            if is_ended and chan_analyzer.xd_list:
                latest_xd = chan_analyzer.xd_list[-1]
                xd_direction = latest_xd[4]
                
                if xd_direction == 'up':
                    return {
                        'action': 'sell',
                        'confidence': 0.7,
                        'reasoning': f"线段终结: {end_reason.get('原因', '多重信号确认')}",
                        'signal_type': 'xd_end'
                    }
                elif xd_direction == 'down':
                    return {
                        'action': 'buy', 
                        'confidence': 0.7,
                        'reasoning': f"线段终结: {end_reason.get('原因', '多重信号确认')}",
                        'signal_type': 'xd_end'
                    }
            
            # 检查分型信号
            if chan_analyzer.fx_list:
                recent_fx = [fx for fx in chan_analyzer.fx_list if len(data) - fx[0] <= 3]
                
                if recent_fx:
                    latest_fx = recent_fx[-1]
                    fx_type = latest_fx[1]
                    
                    if fx_type == 'bottom':
                        return {
                            'action': 'buy',
                            'confidence': 0.6,
                            'reasoning': "检测到底分型",
                            'signal_type': 'fractal'
                        }
                    elif fx_type == 'top':
                        return {
                            'action': 'sell',
                            'confidence': 0.6,
                            'reasoning': "检测到顶分型", 
                            'signal_type': 'fractal'
                        }
            
            return None
            
        except Exception as e:
            return None
    
    def _check_multiple_conditions(self, data: pd.DataFrame, chan_analyzer: ChanAnalysis, 
                                 action: str) -> Dict[str, Any]:
        """多条件验证"""
        conditions = {}
        
        try:
            # 1. 成交量确认
            if len(data) >= 20:
                current_volume = data['volume'].iloc[-1]
                avg_volume = data['volume'].rolling(20).mean().iloc[-1]
                
                if current_volume > avg_volume * self.config['volume_threshold']:
                    conditions['volume_confirm'] = True
                    conditions['volume_ratio'] = current_volume / avg_volume
                else:
                    conditions['volume_confirm'] = False
                    conditions['volume_ratio'] = current_volume / avg_volume
            
            # 2. 技术指标确认
            conditions.update(self._check_technical_indicators(data, action))
            
            # 3. 结构确认
            conditions.update(self._check_structure_confirmation(data, chan_analyzer, action))
            
            # 4. 趋势确认
            conditions.update(self._check_trend_confirmation(data, action))
            
            return conditions
            
        except Exception as e:
            return {'error': str(e)}
    
    def _enhanced_sell_validation(self, data: pd.DataFrame, chan_analyzer: ChanAnalysis) -> Dict[str, Any]:
        """
        增强卖出信号验证 - 专门解决62.5%卖出失败率问题
        """
        sell_conditions = {}
        
        try:
            # 1. 背驰确认 (关键!)
            sell_conditions.update(self._check_divergence(data))
            
            # 2. 阻力位确认
            sell_conditions.update(self._check_resistance_level(data))
            
            # 3. 趋势强度确认
            sell_conditions.update(self._check_trend_strength(data, 'sell'))
            
            # 4. 时间周期确认
            sell_conditions.update(self._check_timeframe_alignment(data, 'sell'))
            
            # 5. 风险收益比确认
            sell_conditions.update(self._check_risk_reward_ratio(data, 'sell'))
            
            return sell_conditions
            
        except Exception as e:
            return {'sell_validation_error': str(e)}
    
    def _check_divergence(self, data: pd.DataFrame) -> Dict[str, Any]:
        """检查背驰"""
        try:
            if len(data) < 26:
                return {'divergence': False, 'reason': '数据不足'}
            
            # 计算MACD
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            macd = exp1 - exp2
            
            # 检查价格和MACD的背驰
            recent_prices = data['close'].tail(10)
            recent_macd = macd.tail(10)
            
            price_trend = recent_prices.iloc[-1] > recent_prices.iloc[0]
            macd_trend = recent_macd.iloc[-1] > recent_macd.iloc[0]
            
            # 背驰：价格和MACD方向不一致
            if price_trend != macd_trend:
                return {
                    'divergence': True,
                    'type': 'macd_divergence',
                    'strength': abs(recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
                }
            
            return {'divergence': False, 'reason': '无背驰'}
            
        except Exception as e:
            return {'divergence': False, 'error': str(e)}
    
    def _check_resistance_level(self, data: pd.DataFrame) -> Dict[str, Any]:
        """检查阻力位"""
        try:
            current_price = data['close'].iloc[-1]
            
            # 寻找近期高点作为阻力位
            recent_highs = data['high'].rolling(10).max().tail(20)
            resistance_levels = recent_highs.unique()
            resistance_levels = resistance_levels[resistance_levels > current_price]
            
            if len(resistance_levels) > 0:
                nearest_resistance = min(resistance_levels)
                distance_to_resistance = (nearest_resistance - current_price) / current_price
                
                # 如果距离阻力位很近(2%以内)，卖出信号更可靠
                if distance_to_resistance <= 0.02:
                    return {
                        'near_resistance': True,
                        'resistance_level': nearest_resistance,
                        'distance_pct': distance_to_resistance * 100
                    }
            
            return {'near_resistance': False}
            
        except Exception as e:
            return {'resistance_error': str(e)}
    
    def _check_trend_strength(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """检查趋势强度"""
        try:
            if len(data) < 20:
                return {'trend_strength': 'unknown'}
            
            # 计算多个周期的移动平均线
            ma5 = data['close'].rolling(5).mean()
            ma10 = data['close'].rolling(10).mean()
            ma20 = data['close'].rolling(20).mean()
            
            current_price = data['close'].iloc[-1]
            
            if action == 'sell':
                # 卖出时，希望看到下降趋势的确认
                if (current_price < ma5.iloc[-1] < ma10.iloc[-1] < ma20.iloc[-1]):
                    return {'trend_strength': 'strong_down', 'alignment': True}
                elif current_price < ma20.iloc[-1]:
                    return {'trend_strength': 'weak_down', 'alignment': True}
                else:
                    return {'trend_strength': 'up_trend', 'alignment': False}
            
            elif action == 'buy':
                # 买入时，希望看到上升趋势的确认
                if (current_price > ma5.iloc[-1] > ma10.iloc[-1] > ma20.iloc[-1]):
                    return {'trend_strength': 'strong_up', 'alignment': True}
                elif current_price > ma20.iloc[-1]:
                    return {'trend_strength': 'weak_up', 'alignment': True}
                else:
                    return {'trend_strength': 'down_trend', 'alignment': False}
            
            return {'trend_strength': 'neutral'}
            
        except Exception as e:
            return {'trend_error': str(e)}
    
    def _check_technical_indicators(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """检查技术指标"""
        try:
            conditions = {}
            
            if len(data) >= 14:
                # RSI
                delta = data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                current_rsi = rsi.iloc[-1]
                
                if action == 'buy' and current_rsi < 40:
                    conditions['rsi_support'] = True
                elif action == 'sell' and current_rsi > 60:
                    conditions['rsi_support'] = True
                else:
                    conditions['rsi_support'] = False
                
                conditions['rsi_value'] = current_rsi
            
            return conditions
            
        except Exception as e:
            return {'technical_error': str(e)}
    
    def _check_structure_confirmation(self, data: pd.DataFrame, chan_analyzer: ChanAnalysis, 
                                    action: str) -> Dict[str, Any]:
        """检查结构确认"""
        try:
            conditions = {}
            
            # 检查笔的数量和质量
            if chan_analyzer.bi_list:
                conditions['bi_count'] = len(chan_analyzer.bi_list)
                conditions['bi_quality'] = len(chan_analyzer.bi_list) >= 3
            
            # 检查线段的数量和质量
            if chan_analyzer.xd_list:
                conditions['xd_count'] = len(chan_analyzer.xd_list)
                conditions['xd_quality'] = len(chan_analyzer.xd_list) >= 2
            
            return conditions
            
        except Exception as e:
            return {'structure_error': str(e)}
    
    def _check_trend_confirmation(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """检查趋势确认"""
        try:
            if len(data) < 10:
                return {'trend_confirmed': False}
            
            # 简单趋势确认：最近5个收盘价的趋势
            recent_closes = data['close'].tail(5)
            
            if action == 'buy':
                trend_up = (recent_closes.iloc[-1] > recent_closes.iloc[0])
                return {'trend_confirmed': trend_up, 'trend_direction': 'up' if trend_up else 'down'}
            elif action == 'sell':
                trend_down = (recent_closes.iloc[-1] < recent_closes.iloc[0])
                return {'trend_confirmed': trend_down, 'trend_direction': 'down' if trend_down else 'up'}
            
            return {'trend_confirmed': False}
            
        except Exception as e:
            return {'trend_error': str(e)}
    
    def _check_timeframe_alignment(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """检查时间周期对齐"""
        # 这里可以添加多时间周期的对齐检查
        # 暂时返回基础实现
        return {'timeframe_aligned': True}
    
    def _check_risk_reward_ratio(self, data: pd.DataFrame, action: str) -> Dict[str, Any]:
        """检查风险收益比"""
        try:
            current_price = data['close'].iloc[-1]
            
            # 计算ATR作为风险参考
            if len(data) >= 14:
                high_low = data['high'] - data['low']
                high_close = abs(data['high'] - data['close'].shift(1))
                low_close = abs(data['low'] - data['close'].shift(1))
                
                tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = tr.rolling(14).mean().iloc[-1]
                
                if pd.isna(atr):
                    atr = current_price * 0.02
                
                # 风险收益比计算
                risk = atr * self.config['atr_multiplier']
                reward = atr * 3  # 目标3倍ATR收益
                
                risk_reward_ratio = reward / risk if risk > 0 else 0
                
                return {
                    'risk_reward_ratio': risk_reward_ratio,
                    'risk_amount': risk,
                    'reward_target': reward,
                    'atr_value': atr
                }
            
            return {'risk_reward_ratio': 1.0}
            
        except Exception as e:
            return {'risk_reward_error': str(e)}
    
    def _calculate_quality_score(self, conditions: Dict, base_signal: Dict) -> float:
        """计算信号质量评分"""
        try:
            score = base_signal['confidence']  # 基础分数
            
            # 成交量加分
            if conditions.get('volume_confirm', False):
                score += 0.1
            
            # 技术指标加分
            if conditions.get('rsi_support', False):
                score += 0.05
            
            # 趋势确认加分
            if conditions.get('trend_confirmed', False):
                score += 0.1
            
            # 结构质量加分
            if conditions.get('bi_quality', False) and conditions.get('xd_quality', False):
                score += 0.1
            
            return min(1.0, score)  # 最高1.0
            
        except Exception as e:
            return 0.5
    
    def _adjust_sell_quality_score(self, base_score: float, sell_conditions: Dict) -> float:
        """调整卖出信号质量评分"""
        try:
            adjusted_score = base_score
            
            # 背驰确认大幅加分
            if sell_conditions.get('divergence', False):
                adjusted_score += 0.15
            
            # 阻力位确认加分
            if sell_conditions.get('near_resistance', False):
                adjusted_score += 0.1
            
            # 趋势强度确认加分
            if sell_conditions.get('trend_strength') in ['strong_down', 'weak_down']:
                adjusted_score += 0.1
            
            return min(1.0, adjusted_score)
            
        except Exception as e:
            return base_score
    
    def _make_final_decision(self, base_signal: Dict, conditions: Dict, quality_score: float,
                           data: pd.DataFrame, timestamp: datetime) -> OptimizedSignal:
        """做出最终信号决策"""
        try:
            # 检查是否满足最低要求
            conditions_met = []
            
            if conditions.get('volume_confirm', False):
                conditions_met.append('成交量确认')
            if conditions.get('trend_confirmed', False):
                conditions_met.append('趋势确认')
            if conditions.get('rsi_support', False):
                conditions_met.append('RSI支持')
            if conditions.get('divergence', False):
                conditions_met.append('背驰确认')
            if conditions.get('near_resistance', False):
                conditions_met.append('阻力位确认')
            
            # 决策逻辑
            if quality_score < self.config['min_confidence']:
                return self._create_wait_signal(
                    data['close'].iloc[-1], timestamp, 
                    f"信号质量不足: {quality_score:.2f} < {self.config['min_confidence']}"
                )
            
            if len(conditions_met) < self.config['min_conditions']:
                return self._create_wait_signal(
                    data['close'].iloc[-1], timestamp,
                    f"满足条件不足: {len(conditions_met)} < {self.config['min_conditions']}"
                )
            
            # 特别检查卖出信号
            if base_signal['action'] == 'sell':
                # 卖出信号需要额外的确认
                sell_score_boost = self.config['sell_signal_boost']
                if quality_score < self.config['min_confidence'] + sell_score_boost:
                    return self._create_wait_signal(
                        data['close'].iloc[-1], timestamp,
                        f"卖出信号质量不足: {quality_score:.2f} < {self.config['min_confidence'] + sell_score_boost}"
                    )
            
            # 生成最终信号
            return self._create_optimized_signal(
                base_signal, conditions, quality_score, conditions_met, data, timestamp
            )
            
        except Exception as e:
            return self._create_wait_signal(
                data['close'].iloc[-1], timestamp, f"决策失败: {str(e)}"
            )
    
    def _create_optimized_signal(self, base_signal: Dict, conditions: Dict, quality_score: float,
                               conditions_met: list, data: pd.DataFrame, timestamp: datetime) -> OptimizedSignal:
        """创建优化信号"""
        try:
            entry_price = data['close'].iloc[-1]
            
            # 动态止损计算
            atr_value = conditions.get('atr_value', entry_price * 0.02)
            
            if base_signal['action'] == 'buy':
                stop_loss = entry_price - atr_value * self.config['atr_multiplier']
                take_profits = [
                    entry_price + atr_value * 2,
                    entry_price + atr_value * 4,
                    entry_price + atr_value * 6
                ]
            else:  # sell
                stop_loss = entry_price + atr_value * self.config['atr_multiplier']
                take_profits = [
                    entry_price - atr_value * 2,
                    entry_price - atr_value * 4,
                    entry_price - atr_value * 6
                ]
            
            # 风险等级评估
            risk_level = 'LOW' if quality_score >= 0.8 else 'MEDIUM' if quality_score >= 0.65 else 'HIGH'
            
            # 详细理由
            reasoning = f"{base_signal['reasoning']}; 质量评分: {quality_score:.2f}; 满足条件: {', '.join(conditions_met)}"
            
            return OptimizedSignal(
                action=base_signal['action'],
                confidence=quality_score,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profits=take_profits,
                reasoning=reasoning,
                risk_level=risk_level,
                quality_score=quality_score,
                conditions_met=conditions_met
            )
            
        except Exception as e:
            return self._create_wait_signal(
                data['close'].iloc[-1], timestamp, f"信号创建失败: {str(e)}"
            )
    
    def _create_wait_signal(self, price: float, timestamp: datetime, reason: str) -> OptimizedSignal:
        """创建等待信号"""
        return OptimizedSignal(
            action='wait',
            confidence=0.5,
            entry_price=price,
            stop_loss=0,
            take_profits=[],
            reasoning=reason,
            risk_level='LOW',
            quality_score=0.0,
            conditions_met=[]
        )

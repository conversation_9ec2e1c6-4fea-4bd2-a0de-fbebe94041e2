#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试图表颜色区分度
"""

import matplotlib.pyplot as plt
import numpy as np
from chart_plotter import ChartPlotter

def test_color_distinction():
    """测试颜色区分度"""
    
    # 创建绘图器
    plotter = ChartPlotter()
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置深色背景
    fig.patch.set_facecolor('#161a2b')
    ax.set_facecolor('#161a2b')
    
    # 测试数据
    x = np.linspace(0, 10, 100)
    
    # 绘制不同类型的线条来测试颜色区分度
    lines_info = [
        ('笔(上升)', plotter.colors['up_stroke'], '-', 1.2),
        ('笔(下降)', plotter.colors['down_stroke'], '-', 1.2),
        ('线段(上升)', plotter.colors['up_segment'], '-', 2.0),
        ('线段(下降)', plotter.colors['down_segment'], '-', 2.0),
        ('主趋势线', plotter.colors['trend_line'], '-', 2.5),
        ('推演走势(上涨)', '#00ff00', '--', 3.0),
        ('推演走势(下跌)', '#ff0000', '--', 3.0),
        ('推演走势(震荡)', '#ffff00', '--', 3.0),
        ('MA5均线', plotter.colors['ma5'], '-', 1.0),
        ('MA10均线', plotter.colors['ma10'], '-', 1.0),
        ('MA20均线', plotter.colors['ma20'], '-', 1.0),
    ]
    
    # 绘制测试线条
    for i, (label, color, style, width) in enumerate(lines_info):
        y = np.sin(x + i * 0.5) + i * 0.5
        ax.plot(x, y, color=color, linestyle=style, linewidth=width, 
                label=label, alpha=0.8)
    
    # 设置图表样式
    ax.set_title('缠论图表颜色区分度测试', fontsize=16, color='white')
    ax.set_xlabel('时间', fontsize=12, color='white')
    ax.set_ylabel('价格', fontsize=12, color='white')
    ax.grid(True, linestyle=':', alpha=0.7, color='#2a2e3e')
    ax.legend(loc='upper left', fontsize=10)
    
    # 设置坐标轴颜色
    ax.tick_params(colors='white')
    ax.spines['bottom'].set_color('#2a2e3e')
    ax.spines['top'].set_color('#2a2e3e')
    ax.spines['right'].set_color('#2a2e3e')
    ax.spines['left'].set_color('#2a2e3e')
    
    # 保存测试图表
    plt.tight_layout()
    plt.savefig('charts/color_distinction_test.png', dpi=300, bbox_inches='tight',
                facecolor='#161a2b', edgecolor='none')
    plt.close()
    
    print("✅ 颜色区分度测试图表已生成: charts/color_distinction_test.png")
    
    # 打印颜色配置
    print("\n🎨 当前颜色配置:")
    print("=" * 50)
    for key, value in plotter.colors.items():
        print(f"{key:20s}: {value}")
    
    print("\n📊 颜色区分度优化说明:")
    print("=" * 50)
    print("✅ 笔: 上升(绿色) vs 下降(红色) - 对比度高")
    print("✅ 线段: 上升(黄色) vs 下降(青色) - 对比度高") 
    print("✅ 主趋势线: 橙红色 - 与笔、线段区分明显")
    print("✅ 推演线: 虚线样式 + 鲜明颜色 - 易于识别")
    print("✅ 均线: 紫色、橙色、白色 - 层次分明")

if __name__ == "__main__":
    test_color_distinction()

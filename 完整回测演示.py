#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整回测演示
使用增强回测系统验证缠论交易系统的真实表现
严格不倒果为因，每次判断只基于当前及之前的数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_backtest import EnhancedBacktest
from datetime import datetime, timedelta
from loguru import logger

def run_complete_backtest():
    """运行完整回测"""
    print("=" * 80)
    print("🔍 完整缠论回测系统演示")
    print("=" * 80)
    print("📋 回测特点:")
    print("   ✅ 严格按时间顺序分析，不倒果为因")
    print("   ✅ 每次判断只基于当前及之前的数据")
    print("   ✅ 使用真实的缠论分析算法")
    print("   ✅ 模拟真实的交易执行过程")
    print("   ✅ 包含完整的止盈止损机制")
    
    try:
        # 1. 初始化增强回测系统
        print("\n1️⃣ 初始化增强回测系统...")
        backtest = EnhancedBacktest(symbol="BTC/USDT", exchange="gate")
        print("✅ 增强回测系统初始化完成")
        
        # 2. 设置回测参数
        print("\n2️⃣ 设置回测参数...")
        
        # 回测最近3天的数据（足够验证系统效果）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        print(f"   📅 回测期间: {start_date_str} 到 {end_date_str}")
        print(f"   ⏰ 主要周期: 15m")
        print(f"   💰 交易品种: BTC/USDT")
        print(f"   💼 初始资金: {backtest.config['initial_capital']} USDT")
        print(f"   📏 每次仓位: {backtest.config['position_size']*100}%")
        print(f"   ⏱️ 最大持仓: {backtest.config['max_holding_time']} 分钟")
        
        # 3. 运行增强回测
        print("\n3️⃣ 开始增强回测...")
        print("   🔬 使用缠论分析算法")
        print("   📊 实时分析分型、笔、线段、中枢")
        print("   🎯 识别标准买卖点")
        print("   ⚠️ 严格时间顺序，不使用未来数据")
        
        report = backtest.run_enhanced_backtest(
            start_date=start_date_str,
            end_date=end_date_str,
            primary_timeframe='15m'
        )
        
        # 4. 显示详细回测结果
        print("\n4️⃣ 回测完成，生成详细报告...")
        backtest.print_enhanced_backtest_report(report)
        
        # 5. 深度分析结果
        print("\n5️⃣ 深度分析:")
        analyze_backtest_performance(report)
        
        # 6. 验证系统可靠性
        print("\n6️⃣ 系统可靠性验证:")
        validate_system_reliability(report)
        
        return report
        
    except Exception as e:
        logger.error(f"完整回测失败: {str(e)}")
        print(f"❌ 完整回测失败: {str(e)}")
        return None

def analyze_backtest_performance(report):
    """分析回测表现"""
    print("\n📈 回测表现深度分析:")
    
    # 1. 信号质量分析
    total_periods = (report.end_time - report.start_time).total_seconds() / (15 * 60)  # 15分钟周期
    signal_frequency = report.total_signals / total_periods if total_periods > 0 else 0
    
    print(f"\n🔢 信号频率分析:")
    print(f"   总时间段: {total_periods:.0f} 个15分钟周期")
    print(f"   信号频率: {signal_frequency:.1%}")
    print(f"   交易转化率: {report.total_trades/report.total_signals*100:.1f}%" if report.total_signals > 0 else "   交易转化率: 0%")
    
    if signal_frequency > 0.3:
        print("   📊 评估: 信号频率较高，系统较为敏感")
    elif signal_frequency < 0.05:
        print("   📊 评估: 信号频率较低，系统较为保守")
    else:
        print("   📊 评估: 信号频率适中")
    
    # 2. 交易质量分析
    if report.trade_results:
        print(f"\n💎 交易质量分析:")
        
        # 盈利交易分析
        profitable_trades = [t for t in report.trade_results if t.profit_loss > 0]
        losing_trades = [t for t in report.trade_results if t.profit_loss < 0]
        
        print(f"   盈利交易: {len(profitable_trades)} 笔")
        print(f"   亏损交易: {len(losing_trades)} 笔")
        
        if profitable_trades:
            avg_profit = sum(t.profit_loss_pct for t in profitable_trades) / len(profitable_trades)
            max_profit = max(t.profit_loss_pct for t in profitable_trades)
            print(f"   平均盈利: {avg_profit:.2f}%")
            print(f"   最大盈利: {max_profit:.2f}%")
        
        if losing_trades:
            avg_loss = sum(t.profit_loss_pct for t in losing_trades) / len(losing_trades)
            max_loss = min(t.profit_loss_pct for t in losing_trades)
            print(f"   平均亏损: {avg_loss:.2f}%")
            print(f"   最大亏损: {max_loss:.2f}%")
        
        # 盈亏比分析
        if profitable_trades and losing_trades:
            avg_profit = sum(t.profit_loss_pct for t in profitable_trades) / len(profitable_trades)
            avg_loss = abs(sum(t.profit_loss_pct for t in losing_trades) / len(losing_trades))
            profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
            
            print(f"   盈亏比: 1:{profit_loss_ratio:.2f}")
            
            if profit_loss_ratio >= 2:
                print("   ✅ 盈亏比优秀，单笔盈利能覆盖多笔亏损")
            elif profit_loss_ratio >= 1.5:
                print("   🟡 盈亏比良好，风险收益平衡")
            else:
                print("   ❌ 盈亏比偏低，需要优化止盈止损策略")
        
        # 退出原因分析
        print(f"\n🚪 退出原因分析:")
        exit_reasons = {}
        for trade in report.trade_results:
            reason = trade.exit_reason
            exit_reasons[reason] = exit_reasons.get(reason, 0) + 1
        
        for reason, count in exit_reasons.items():
            percentage = count / len(report.trade_results) * 100
            print(f"   {reason}: {count} 笔 ({percentage:.1f}%)")
    
    # 3. 时间分析
    print(f"\n⏱️ 时间效率分析:")
    print(f"   平均持仓时间: {report.avg_holding_time:.1f} 分钟")
    
    if report.avg_holding_time <= 30:
        print("   📊 评估: 超短线交易，适合高频策略")
    elif report.avg_holding_time <= 120:
        print("   📊 评估: 短线交易，符合15分钟周期特点")
    else:
        print("   📊 评估: 中线交易，持仓时间较长")

def validate_system_reliability(report):
    """验证系统可靠性"""
    print("\n🔍 系统可靠性验证:")
    
    # 1. 数据完整性验证
    print(f"\n📊 数据完整性:")
    if report.total_signals > 0:
        print("   ✅ 成功生成交易信号")
        print(f"   ✅ 信号数量: {report.total_signals} 个")
    else:
        print("   ❌ 未生成任何交易信号")
    
    # 2. 交易执行验证
    print(f"\n💼 交易执行验证:")
    if report.total_trades > 0:
        print("   ✅ 成功执行模拟交易")
        print(f"   ✅ 交易数量: {report.total_trades} 笔")
        
        # 验证止盈止损执行
        stop_loss_exits = len([t for t in report.trade_results if t.exit_reason == 'stop_loss'])
        take_profit_exits = len([t for t in report.trade_results if 'take_profit' in t.exit_reason])
        timeout_exits = len([t for t in report.trade_results if t.exit_reason == 'timeout'])
        
        print(f"   止损退出: {stop_loss_exits} 笔")
        print(f"   止盈退出: {take_profit_exits} 笔")
        print(f"   超时退出: {timeout_exits} 笔")
        
        if stop_loss_exits + take_profit_exits + timeout_exits == report.total_trades:
            print("   ✅ 所有交易都有明确的退出机制")
        else:
            print("   ⚠️ 部分交易退出机制不明确")
    else:
        print("   ⚠️ 未执行任何交易")
    
    # 3. 风险控制验证
    print(f"\n🛡️ 风险控制验证:")
    if report.max_drawdown <= 10:
        print(f"   ✅ 最大回撤 {report.max_drawdown:.2f}% ≤ 10%，风险控制良好")
    elif report.max_drawdown <= 20:
        print(f"   🟡 最大回撤 {report.max_drawdown:.2f}% ≤ 20%，风险控制一般")
    else:
        print(f"   ❌ 最大回撤 {report.max_drawdown:.2f}% > 20%，风险控制需要改进")
    
    # 4. 系统稳定性验证
    print(f"\n⚖️ 系统稳定性:")
    if report.sharpe_ratio >= 1:
        print(f"   ✅ 夏普比率 {report.sharpe_ratio:.2f} ≥ 1，风险调整收益优秀")
    elif report.sharpe_ratio >= 0.5:
        print(f"   🟡 夏普比率 {report.sharpe_ratio:.2f} ≥ 0.5，风险调整收益良好")
    else:
        print(f"   ❌ 夏普比率 {report.sharpe_ratio:.2f} < 0.5，风险调整收益较差")

def system_readiness_assessment(report):
    """系统就绪度评估"""
    print(f"\n🎯 系统投入使用就绪度评估:")
    
    score = 0
    max_score = 100
    
    # 评估标准
    criteria = [
        ("信号生成能力", report.total_signals > 0, 20),
        ("交易执行能力", report.total_trades > 0, 20),
        ("胜率表现", report.win_rate >= 0.4, 15),
        ("盈利能力", report.total_profit_loss_pct >= 0, 15),
        ("风险控制", report.max_drawdown <= 15, 15),
        ("系统稳定性", report.sharpe_ratio >= 0.3, 15)
    ]
    
    print(f"\n📋 评估标准:")
    for criterion, passed, points in criteria:
        status = "✅ 通过" if passed else "❌ 未通过"
        score += points if passed else 0
        print(f"   {criterion}: {status} ({points}分)")
    
    print(f"\n🏆 总体评分: {score}/{max_score} ({score/max_score*100:.1f}%)")
    
    if score >= 80:
        print("🟢 系统就绪度: 优秀 - 可以考虑投入实盘使用")
        print("💡 建议: 先进行小资金实盘验证")
    elif score >= 60:
        print("🟡 系统就绪度: 良好 - 需要进一步优化后使用")
        print("💡 建议: 优化关键指标后再考虑实盘")
    else:
        print("🔴 系统就绪度: 需要改进 - 暂不建议实盘使用")
        print("💡 建议: 重新调整策略参数和逻辑")

if __name__ == "__main__":
    # 运行完整回测
    report = run_complete_backtest()
    
    if report:
        # 系统就绪度评估
        system_readiness_assessment(report)
        
        print("\n" + "=" * 80)
        print("🎯 完整回测演示完成")
        print("=" * 80)
        
        # 最终结论
        if report.win_rate >= 0.5 and report.total_profit_loss_pct > 0:
            print("🟢 结论: 缠论系统通过历史回测验证，表现良好！")
            print("💡 系统能够在历史数据上识别有效的买卖点")
        elif report.total_trades > 0:
            print("🟡 结论: 缠论系统基本可用，但需要优化")
            print("💡 系统能够生成交易信号，但盈利能力需要提升")
        else:
            print("🔴 结论: 缠论系统需要重大改进")
            print("💡 系统信号生成能力不足，需要调整参数")
        
        print(f"\n📊 关键指标总结:")
        print(f"   📡 信号数量: {report.total_signals}")
        print(f"   💼 交易数量: {report.total_trades}")
        print(f"   🎯 胜率: {report.win_rate:.1%}")
        print(f"   💰 总收益: {report.total_profit_loss_pct:.2f}%")
        print(f"   📉 最大回撤: {report.max_drawdown:.2f}%")
        print(f"   ⚖️ 夏普比率: {report.sharpe_ratio:.2f}")
        
    else:
        print("❌ 回测失败，请检查系统配置和数据连接")
    
    print("\n🔄 下一步建议:")
    print("   1. 在更长时间周期进行回测验证")
    print("   2. 测试不同市场条件下的表现")
    print("   3. 优化缠论参数和买卖点识别逻辑")
    print("   4. 进行多品种回测验证")
    print("   5. 开始小资金实盘验证")

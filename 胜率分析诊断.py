#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
胜率分析诊断工具
深度分析53.3%胜率偏低的原因，找出问题所在
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_backtest import EnhancedBacktest
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from loguru import logger

def analyze_win_rate_issues():
    """分析胜率问题"""
    print("=" * 80)
    print("🔍 胜率深度诊断分析")
    print("=" * 80)
    print("📋 目标: 找出53.3%胜率偏低的根本原因")
    
    try:
        # 1. 重新运行回测获取详细数据
        print("\n1️⃣ 重新运行回测获取详细数据...")
        backtest = EnhancedBacktest(symbol="BTC/USDT", exchange="gate")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        report = backtest.run_enhanced_backtest(
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d'),
            primary_timeframe='15m'
        )
        
        print(f"✅ 获取到 {len(report.trade_results)} 笔交易数据")
        
        # 2. 分析失败交易的具体原因
        print("\n2️⃣ 分析失败交易的具体原因...")
        analyze_failed_trades(report.trade_results)
        
        # 3. 分析信号质量问题
        print("\n3️⃣ 分析信号质量问题...")
        analyze_signal_quality_issues(report)
        
        # 4. 分析止盈止损设置问题
        print("\n4️⃣ 分析止盈止损设置问题...")
        analyze_stop_profit_issues(report.trade_results)
        
        # 5. 分析缠论逻辑问题
        print("\n5️⃣ 分析缠论逻辑问题...")
        analyze_chan_logic_issues(report.trade_results)
        
        # 6. 分析时间周期问题
        print("\n6️⃣ 分析时间周期问题...")
        analyze_timeframe_issues(report.trade_results)
        
        # 7. 提出具体改进方案
        print("\n7️⃣ 提出具体改进方案...")
        propose_improvement_solutions(report)
        
        return report
        
    except Exception as e:
        logger.error(f"胜率分析失败: {str(e)}")
        print(f"❌ 胜率分析失败: {str(e)}")
        return None

def analyze_failed_trades(trades):
    """分析失败交易的具体原因"""
    print("\n📉 失败交易深度分析:")
    
    failed_trades = [t for t in trades if not t.success]
    successful_trades = [t for t in trades if t.success]
    
    print(f"   失败交易: {len(failed_trades)} 笔")
    print(f"   成功交易: {len(successful_trades)} 笔")
    print(f"   当前胜率: {len(successful_trades)/len(trades)*100:.1f}%")
    
    # 分析失败原因分布
    failure_reasons = {}
    for trade in failed_trades:
        reason = trade.exit_reason
        failure_reasons[reason] = failure_reasons.get(reason, 0) + 1
    
    print(f"\n🔍 失败原因分布:")
    for reason, count in failure_reasons.items():
        percentage = count / len(failed_trades) * 100
        print(f"   {reason}: {count} 笔 ({percentage:.1f}%)")
    
    # 分析失败交易的特征
    print(f"\n📊 失败交易特征分析:")
    
    if failed_trades:
        # 平均亏损
        avg_loss = sum(t.profit_loss_pct for t in failed_trades) / len(failed_trades)
        max_loss = min(t.profit_loss_pct for t in failed_trades)
        
        # 平均持仓时间
        avg_duration = sum(t.duration_minutes for t in failed_trades) / len(failed_trades)
        
        print(f"   平均亏损: {avg_loss:.2f}%")
        print(f"   最大亏损: {max_loss:.2f}%")
        print(f"   平均持仓时间: {avg_duration:.1f} 分钟")
        
        # 按交易方向分析
        failed_buys = [t for t in failed_trades if t.signal.action == 'buy']
        failed_sells = [t for t in failed_trades if t.signal.action == 'sell']
        
        print(f"   失败买入: {len(failed_buys)} 笔")
        print(f"   失败卖出: {len(failed_sells)} 笔")
        
        if len(failed_buys) > 0:
            buy_loss_rate = len(failed_buys) / len([t for t in trades if t.signal.action == 'buy']) * 100
            print(f"   买入失败率: {buy_loss_rate:.1f}%")
        
        if len(failed_sells) > 0:
            sell_loss_rate = len(failed_sells) / len([t for t in trades if t.signal.action == 'sell']) * 100
            print(f"   卖出失败率: {sell_loss_rate:.1f}%")

def analyze_signal_quality_issues(report):
    """分析信号质量问题"""
    print("\n📡 信号质量问题分析:")
    
    # 信号频率过高问题
    total_periods = (report.end_time - report.start_time).total_seconds() / (15 * 60)
    signal_frequency = report.total_signals / total_periods
    
    print(f"   信号频率: {signal_frequency:.1%}")
    if signal_frequency > 0.5:
        print("   ❌ 问题: 信号频率过高，可能存在过度交易")
        print("   💡 建议: 提高信号质量门槛，减少低质量信号")
    
    # 信号转化率问题
    conversion_rate = report.total_trades / report.total_signals
    print(f"   信号转化率: {conversion_rate:.1%}")
    if conversion_rate < 0.3:
        print("   ❌ 问题: 信号转化率过低，大量信号未转化为交易")
        print("   💡 建议: 优化信号过滤逻辑")
    
    # 等待信号过多问题
    wait_rate = report.wait_signals / report.total_signals
    print(f"   等待信号比例: {wait_rate:.1%}")
    if wait_rate > 0.7:
        print("   ⚠️ 注意: 等待信号过多，可能错过交易机会")
    
    # 买卖信号平衡性
    buy_sell_ratio = report.buy_signals / report.sell_signals if report.sell_signals > 0 else 0
    print(f"   买卖信号比例: {buy_sell_ratio:.2f}:1")
    if abs(buy_sell_ratio - 1) > 0.5:
        print("   ⚠️ 注意: 买卖信号不平衡，可能存在方向性偏差")

def analyze_stop_profit_issues(trades):
    """分析止盈止损设置问题"""
    print("\n🎯 止盈止损设置问题分析:")
    
    # 统计退出原因
    exit_reasons = {}
    for trade in trades:
        reason = trade.exit_reason
        exit_reasons[reason] = exit_reasons.get(reason, 0) + 1
    
    total_trades = len(trades)
    stop_loss_count = exit_reasons.get('stop_loss', 0)
    take_profit_count = sum(exit_reasons.get(f'take_profit_{i}', 0) for i in range(1, 4))
    timeout_count = exit_reasons.get('timeout', 0)
    
    print(f"   止损退出: {stop_loss_count} 笔 ({stop_loss_count/total_trades*100:.1f}%)")
    print(f"   止盈退出: {take_profit_count} 笔 ({take_profit_count/total_trades*100:.1f}%)")
    print(f"   超时退出: {timeout_count} 笔 ({timeout_count/total_trades*100:.1f}%)")
    
    # 分析止损问题
    if stop_loss_count / total_trades > 0.4:
        print("   ❌ 问题: 止损比例过高 (>40%)，止损设置可能过于激进")
        print("   💡 建议: 放宽止损幅度，或提高入场信号质量")
    
    # 分析止盈问题
    if take_profit_count / total_trades < 0.4:
        print("   ❌ 问题: 止盈比例过低 (<40%)，止盈设置可能过于保守")
        print("   💡 建议: 调整止盈目标，或优化入场时机")
    
    # 分析超时问题
    if timeout_count / total_trades > 0.1:
        print("   ⚠️ 注意: 超时退出较多，可能需要调整最大持仓时间")
    
    # 分析止盈止损比例
    if stop_loss_count > 0 and take_profit_count > 0:
        stop_profit_ratio = take_profit_count / stop_loss_count
        print(f"   止盈止损比: {stop_profit_ratio:.2f}:1")
        if stop_profit_ratio < 1.5:
            print("   ❌ 问题: 止盈止损比过低，需要优化")

def analyze_chan_logic_issues(trades):
    """分析缠论逻辑问题"""
    print("\n📈 缠论逻辑问题分析:")
    
    # 分析不同缠论信号的成功率
    signal_performance = {}
    
    for trade in trades:
        reasoning = trade.signal.reasoning
        
        # 提取信号类型
        if "线段终结" in reasoning:
            signal_type = "线段终结"
        elif "分型" in reasoning:
            signal_type = "分型信号"
        elif "笔方向" in reasoning:
            signal_type = "笔方向变化"
        else:
            signal_type = "其他信号"
        
        if signal_type not in signal_performance:
            signal_performance[signal_type] = {'total': 0, 'success': 0}
        
        signal_performance[signal_type]['total'] += 1
        if trade.success:
            signal_performance[signal_type]['success'] += 1
    
    print("   各类缠论信号成功率:")
    for signal_type, perf in signal_performance.items():
        success_rate = perf['success'] / perf['total'] * 100 if perf['total'] > 0 else 0
        print(f"     {signal_type}: {success_rate:.1f}% ({perf['success']}/{perf['total']})")
        
        if success_rate < 50:
            print(f"       ❌ 问题: {signal_type}成功率过低")
        elif success_rate < 60:
            print(f"       ⚠️ 注意: {signal_type}成功率偏低，需要优化")
    
    # 分析置信度与成功率的关系
    print("\n   置信度与成功率关系:")
    confidence_ranges = [
        (0.5, 0.6, "低置信度"),
        (0.6, 0.7, "中置信度"), 
        (0.7, 0.8, "高置信度"),
        (0.8, 1.0, "极高置信度")
    ]
    
    for min_conf, max_conf, label in confidence_ranges:
        range_trades = [t for t in trades if min_conf <= t.signal.confidence < max_conf]
        if range_trades:
            success_count = len([t for t in range_trades if t.success])
            success_rate = success_count / len(range_trades) * 100
            print(f"     {label} ({min_conf:.1f}-{max_conf:.1f}): {success_rate:.1f}% ({success_count}/{len(range_trades)})")

def analyze_timeframe_issues(trades):
    """分析时间周期问题"""
    print("\n⏰ 时间周期问题分析:")
    
    # 分析持仓时间分布
    durations = [t.duration_minutes for t in trades]
    avg_duration = sum(durations) / len(durations)
    
    print(f"   平均持仓时间: {avg_duration:.1f} 分钟")
    
    # 按持仓时间分组分析成功率
    duration_ranges = [
        (0, 30, "超短线"),
        (30, 60, "短线"),
        (60, 120, "中短线"),
        (120, 240, "中线"),
        (240, float('inf'), "长线")
    ]
    
    print("   不同持仓时间成功率:")
    for min_dur, max_dur, label in duration_ranges:
        range_trades = [t for t in trades if min_dur <= t.duration_minutes < max_dur]
        if range_trades:
            success_count = len([t for t in range_trades if t.success])
            success_rate = success_count / len(range_trades) * 100
            print(f"     {label} ({min_dur}-{max_dur if max_dur != float('inf') else '∞'}分钟): {success_rate:.1f}% ({success_count}/{len(range_trades)})")
    
    # 分析15分钟周期是否合适
    if avg_duration < 30:
        print("   ⚠️ 注意: 平均持仓时间过短，可能需要使用更小的时间周期(5m)")
    elif avg_duration > 180:
        print("   ⚠️ 注意: 平均持仓时间过长，可能需要使用更大的时间周期(30m/1h)")

def propose_improvement_solutions(report):
    """提出具体改进方案"""
    print("\n💡 具体改进方案:")
    
    current_win_rate = report.win_rate
    target_win_rate = 0.70  # 目标70%胜率
    
    print(f"   当前胜率: {current_win_rate:.1%}")
    print(f"   目标胜率: {target_win_rate:.1%}")
    print(f"   需要提升: {(target_win_rate - current_win_rate)*100:.1f}个百分点")
    
    solutions = []
    
    # 基于分析结果提出解决方案
    if report.total_signals / ((report.end_time - report.start_time).total_seconds() / (15 * 60)) > 0.5:
        solutions.append({
            "问题": "信号频率过高",
            "解决方案": "提高信号质量门槛",
            "具体措施": [
                "增加置信度最低要求到0.65",
                "要求多个缠论条件同时满足",
                "增加成交量确认条件",
                "加强多级别一致性验证"
            ],
            "预期效果": "减少30%低质量信号，胜率提升10-15%"
        })
    
    stop_loss_rate = len([t for t in report.trade_results if t.exit_reason == 'stop_loss']) / len(report.trade_results)
    if stop_loss_rate > 0.4:
        solutions.append({
            "问题": "止损比例过高",
            "解决方案": "优化止损策略",
            "具体措施": [
                "基于ATR动态调整止损幅度",
                "使用缠论结构止损而非固定百分比",
                "增加止损前的二次确认",
                "优化入场时机，避免追高杀跌"
            ],
            "预期效果": "减少20%止损，胜率提升8-12%"
        })
    
    # 缠论逻辑优化
    solutions.append({
        "问题": "缠论信号质量不稳定",
        "解决方案": "优化缠论分析逻辑",
        "具体措施": [
            "完善线段终结判断条件",
            "增加背驰确认机制",
            "优化分型识别准确性",
            "加强中枢理论应用"
        ],
        "预期效果": "提升信号准确性，胜率提升5-10%"
    })
    
    # 多级别联立优化
    solutions.append({
        "问题": "多级别联立不够严格",
        "解决方案": "强化多级别验证",
        "具体措施": [
            "要求大周期明确方向",
            "小周期必须与大周期一致",
            "增加级别间共振检测",
            "动态调整级别权重"
        ],
        "预期效果": "提升信号可靠性，胜率提升8-15%"
    })
    
    print(f"\n📋 改进方案详情:")
    for i, solution in enumerate(solutions, 1):
        print(f"\n   方案{i}: {solution['解决方案']}")
        print(f"   问题: {solution['问题']}")
        print(f"   具体措施:")
        for measure in solution['具体措施']:
            print(f"     • {measure}")
        print(f"   预期效果: {solution['预期效果']}")
    
    # 综合改进预期
    total_improvement = sum([15, 12, 10, 15])  # 各方案预期效果上限
    realistic_improvement = total_improvement * 0.6  # 考虑实际效果打折
    
    print(f"\n🎯 综合改进预期:")
    print(f"   理论最大提升: {total_improvement}个百分点")
    print(f"   实际预期提升: {realistic_improvement:.1f}个百分点")
    print(f"   改进后胜率: {(current_win_rate + realistic_improvement/100)*100:.1f}%")
    
    if (current_win_rate + realistic_improvement/100) >= target_win_rate:
        print("   ✅ 有望达到70%目标胜率")
    else:
        print("   ⚠️ 可能需要更深层次的算法优化")

if __name__ == "__main__":
    # 运行胜率分析
    report = analyze_win_rate_issues()
    
    if report:
        print("\n" + "=" * 80)
        print("🎯 胜率分析诊断完成")
        print("=" * 80)
        print(f"📊 当前胜率: {report.win_rate:.1%}")
        print("💡 主要问题已识别，改进方案已制定")
        print("🔄 建议按优先级逐步实施改进措施")
    else:
        print("❌ 胜率分析失败，请检查系统配置")

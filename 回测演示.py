#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史回测演示
验证系统在历史数据上的真实表现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from historical_backtest import HistoricalBacktest
from datetime import datetime, timedelta
from loguru import logger

def run_backtest_demo():
    """运行回测演示"""
    print("=" * 80)
    print("🔍 历史回测演示 - 验证系统真实表现")
    print("=" * 80)
    
    try:
        # 1. 初始化回测器
        print("\n1️⃣ 初始化历史回测器...")
        backtest = HistoricalBacktest(symbol="BTC/USDT", exchange="gate")
        print("✅ 回测器初始化完成")
        
        # 2. 设置回测参数
        print("\n2️⃣ 设置回测参数...")
        
        # 回测最近7天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        print(f"   回测期间: {start_date_str} 到 {end_date_str}")
        print(f"   时间周期: 15m")
        print(f"   交易品种: BTC/USDT")
        
        # 3. 运行回测
        print("\n3️⃣ 开始历史回测...")
        print("   注意：严格按照时间顺序，不使用未来数据")
        print("   每个时刻的判断只基于当时及之前的数据")
        
        report = backtest.run_backtest(
            start_date=start_date_str,
            end_date=end_date_str,
            timeframe='15m'
        )
        
        # 4. 显示回测结果
        print("\n4️⃣ 回测完成，生成详细报告...")
        backtest.print_backtest_report(report)
        
        # 5. 分析结果
        print("\n5️⃣ 结果分析:")
        analyze_backtest_results(report)
        
        return report
        
    except Exception as e:
        logger.error(f"回测演示失败: {str(e)}")
        print(f"❌ 回测演示失败: {str(e)}")
        return None

def analyze_backtest_results(report):
    """分析回测结果"""
    print("\n📈 深度分析:")
    
    # 1. 信号频率分析
    total_periods = (report.end_time - report.start_time).total_seconds() / (15 * 60)  # 15分钟周期
    signal_frequency = report.total_signals / total_periods if total_periods > 0 else 0
    
    print(f"\n🔢 信号频率分析:")
    print(f"   总时间段: {total_periods:.0f} 个15分钟周期")
    print(f"   信号频率: {signal_frequency:.1%} (每个周期产生信号的概率)")
    
    if signal_frequency > 0.5:
        print("   📊 信号频率过高，可能存在过度交易")
    elif signal_frequency < 0.05:
        print("   📊 信号频率过低，系统过于保守")
    else:
        print("   📊 信号频率合理")
    
    # 2. 交易质量分析
    print(f"\n💎 交易质量分析:")
    
    if report.trade_results:
        # 盈利交易分析
        profitable_trades = [t for t in report.trade_results if t.profit_loss > 0]
        losing_trades = [t for t in report.trade_results if t.profit_loss < 0]
        
        if profitable_trades:
            avg_profit = sum(t.profit_loss_pct for t in profitable_trades) / len(profitable_trades)
            print(f"   平均盈利: {avg_profit:.2f}%")
        
        if losing_trades:
            avg_loss = sum(t.profit_loss_pct for t in losing_trades) / len(losing_trades)
            print(f"   平均亏损: {avg_loss:.2f}%")
        
        # 盈亏比
        if profitable_trades and losing_trades:
            profit_loss_ratio = abs(avg_profit / avg_loss)
            print(f"   盈亏比: 1:{profit_loss_ratio:.2f}")
            
            if profit_loss_ratio >= 2:
                print("   ✅ 盈亏比优秀")
            elif profit_loss_ratio >= 1.5:
                print("   🟡 盈亏比良好")
            else:
                print("   ❌ 盈亏比需要改进")
    
    # 3. 系统稳定性分析
    print(f"\n🎯 系统稳定性分析:")
    
    if report.max_drawdown <= 5:
        print(f"   ✅ 最大回撤 {report.max_drawdown:.2f}% - 风险控制优秀")
    elif report.max_drawdown <= 10:
        print(f"   🟡 最大回撤 {report.max_drawdown:.2f}% - 风险控制良好")
    else:
        print(f"   ❌ 最大回撤 {report.max_drawdown:.2f}% - 风险控制需要改进")
    
    if report.sharpe_ratio >= 1:
        print(f"   ✅ 夏普比率 {report.sharpe_ratio:.2f} - 风险调整收益优秀")
    elif report.sharpe_ratio >= 0.5:
        print(f"   🟡 夏普比率 {report.sharpe_ratio:.2f} - 风险调整收益良好")
    else:
        print(f"   ❌ 夏普比率 {report.sharpe_ratio:.2f} - 风险调整收益需要改进")
    
    # 4. 持仓时间分析
    print(f"\n⏱️ 持仓时间分析:")
    print(f"   平均持仓: {report.avg_holding_time:.1f} 分钟")
    
    if report.avg_holding_time <= 30:
        print("   📊 短线交易为主，适合高频策略")
    elif report.avg_holding_time <= 120:
        print("   📊 中短线交易，平衡型策略")
    else:
        print("   📊 中长线交易，趋势跟踪策略")
    
    # 5. 改进建议
    print(f"\n💡 改进建议:")
    
    suggestions = []
    
    if report.win_rate < 0.5:
        suggestions.append("提高信号质量，减少假信号")
    
    if report.max_drawdown > 10:
        suggestions.append("加强风险控制，优化止损策略")
    
    if signal_frequency > 0.3:
        suggestions.append("减少交易频率，提高信号门槛")
    
    if report.total_profit_loss_pct < 0:
        suggestions.append("重新评估策略逻辑，可能需要反向操作")
    
    if not suggestions:
        suggestions.append("系统表现良好，继续保持当前策略")
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"   {i}. {suggestion}")

def compare_with_benchmark():
    """与基准比较"""
    print("\n📊 基准比较:")
    print("   基准策略: 买入并持有 (Buy and Hold)")
    print("   注意: 这里需要获取同期的价格数据来计算基准收益")
    print("   如果系统收益超过基准，说明策略有效")

def run_multiple_timeframes():
    """多时间周期回测"""
    print("\n🔄 多时间周期回测建议:")
    print("   建议在以下时间周期进行回测验证:")
    
    timeframes = [
        ("5m", "短线交易验证"),
        ("15m", "中短线交易验证"),
        ("30m", "中线交易验证"),
        ("1h", "中长线交易验证")
    ]
    
    for tf, desc in timeframes:
        print(f"   • {tf}: {desc}")
    
    print("\n   通过多时间周期回测可以:")
    print("   1. 验证策略在不同周期的适应性")
    print("   2. 找到最适合的交易周期")
    print("   3. 优化参数设置")

def backtest_validation_checklist():
    """回测验证检查清单"""
    print("\n✅ 回测验证检查清单:")
    
    checklist = [
        "严格按时间顺序分析，不使用未来数据",
        "每个时刻的判断只基于当时及之前的数据",
        "考虑交易成本（手续费、滑点）",
        "模拟真实的交易执行过程",
        "包含止盈止损的执行",
        "统计足够长的时间周期",
        "分析不同市场条件下的表现",
        "验证信号的时效性和准确性"
    ]
    
    for i, item in enumerate(checklist, 1):
        print(f"   {i}. ✓ {item}")

if __name__ == "__main__":
    # 运行回测演示
    report = run_backtest_demo()
    
    if report:
        # 基准比较
        compare_with_benchmark()
        
        # 多时间周期建议
        run_multiple_timeframes()
        
        # 验证检查清单
        backtest_validation_checklist()
        
        print("\n" + "=" * 80)
        print("🎯 回测演示完成")
        print("=" * 80)
        
        # 最终评估
        if report.win_rate >= 0.6 and report.total_profit_loss_pct > 0:
            print("🟢 系统通过历史回测验证，表现优秀！")
        elif report.win_rate >= 0.4 and report.total_profit_loss_pct >= 0:
            print("🟡 系统基本通过历史回测，有改进空间")
        else:
            print("🔴 系统未通过历史回测，需要重新优化")
        
        print(f"\n📊 关键指标:")
        print(f"   胜率: {report.win_rate:.1%}")
        print(f"   总收益: {report.total_profit_loss_pct:.2f}%")
        print(f"   最大回撤: {report.max_drawdown:.2f}%")
        print(f"   夏普比率: {report.sharpe_ratio:.2f}")
        
    else:
        print("❌ 回测失败，请检查系统配置")
    
    print("\n💡 下一步:")
    print("   1. 在更长的时间周期进行回测")
    print("   2. 测试不同的市场条件")
    print("   3. 优化系统参数")
    print("   4. 进行实盘模拟交易")

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强交易系统 - 整合所有改进模块
解决投入使用的关键问题
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from loguru import logger
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unified_signal_analyzer import UnifiedSignalAnalyzer, UnifiedSignal, TrendDirection
from probability_optimizer import ProbabilityOptimizer
from stop_profit_validator import StopProfitValidator, ValidationReport
from multi_level_analysis import MultiLevelAnalysis

@dataclass
class TradingDecision:
    """交易决策"""
    action: str                    # buy/sell/wait
    confidence: float              # 置信度 0-1
    entry_price: float             # 入场价格
    stop_loss: float               # 止损价格
    take_profits: List[float]      # 止盈价格列表
    position_size: str             # 仓位大小
    risk_level: str                # 风险等级
    timeframe: str                 # 主要时间周期
    reasoning: str                 # 决策理由
    validation_report: ValidationReport  # 验证报告
    major_trend_status: Dict[str, Any]   # 大周期趋势状态
    probability_analysis: Dict[str, Any] # 概率分析
    warnings: List[str]            # 警告信息
    recommendations: List[str]     # 建议信息

class EnhancedTradingSystem:
    """增强交易系统"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        self.symbol = symbol
        self.exchange = exchange
        
        # 初始化各个模块
        self.signal_analyzer = UnifiedSignalAnalyzer(symbol=symbol, exchange=exchange)
        self.probability_optimizer = ProbabilityOptimizer(symbol=symbol, exchange=exchange)
        self.stop_profit_validator = StopProfitValidator(symbol=symbol, exchange=exchange)
        self.multi_level_analyzer = MultiLevelAnalysis(symbol=symbol, exchange_id=exchange)
        
        # 系统配置
        self.config = {
            'min_confidence': 0.6,        # 最小置信度
            'max_risk_level': 'MEDIUM',   # 最大风险等级
            'require_validation': True,    # 是否需要验证
            'enable_major_trend_filter': True,  # 是否启用大周期过滤
        }
        
        logger.info(f"增强交易系统初始化完成 - {symbol}")
    
    def analyze_trading_opportunity(self) -> TradingDecision:
        """
        分析交易机会
        
        返回:
            TradingDecision: 交易决策
        """
        try:
            logger.info("开始分析交易机会")
            
            # 1. 获取大周期趋势状态
            major_trend_status = self.signal_analyzer.get_major_trend_status()
            logger.info(f"大周期趋势状态: {major_trend_status['status']}")
            
            # 2. 检查是否处于变盘期，如果是则建议等待
            if major_trend_status.get('is_changing', False):
                return self._create_wait_decision(
                    "大周期即将变盘，建议空仓等待",
                    major_trend_status
                )
            
            # 3. 分析统一信号
            unified_signal = self.signal_analyzer.analyze_unified_signals()
            if not unified_signal or unified_signal.action == 'wait':
                reason = unified_signal.reasoning if unified_signal else "未检测到有效信号"
                return self._create_wait_decision(reason, major_trend_status)
            
            # 4. 验证信号与大周期方向一致性
            if self.config['enable_major_trend_filter']:
                consistency_check = self._check_signal_consistency(unified_signal, major_trend_status)
                if not consistency_check['consistent']:
                    return self._create_wait_decision(
                        f"信号与大周期方向不一致: {consistency_check['reason']}",
                        major_trend_status
                    )
            
            # 5. 优化概率分析
            probability_analysis = self._enhance_probability_analysis(unified_signal)
            
            # 6. 验证止盈止损合理性
            validation_report = None
            if self.config['require_validation'] and unified_signal.stop_loss > 0:
                validation_report = self.stop_profit_validator.validate_stop_profit_levels(
                    unified_signal.entry_price,
                    unified_signal.stop_loss,
                    unified_signal.take_profits,
                    unified_signal.timeframe
                )
                
                # 检查验证结果
                if validation_report.overall_result.value in ['unrealistic', 'no_data']:
                    return self._create_wait_decision(
                        f"止盈止损验证失败: {validation_report.overall_result.value}",
                        major_trend_status,
                        validation_report
                    )
            
            # 7. 综合评估和决策
            final_decision = self._make_final_decision(
                unified_signal, major_trend_status, probability_analysis, validation_report
            )
            
            logger.info(f"交易机会分析完成: {final_decision.action}")
            return final_decision
            
        except Exception as e:
            logger.error(f"分析交易机会失败: {str(e)}")
            return self._create_error_decision(str(e))
    
    def _check_signal_consistency(self, signal: UnifiedSignal, 
                                major_trend: Dict[str, Any]) -> Dict[str, Any]:
        """检查信号与大周期一致性"""
        try:
            major_status = major_trend['status']
            signal_bullish = signal.action == 'buy'
            
            # 一致性检查
            if major_status == 'bullish' and signal_bullish:
                return {'consistent': True, 'reason': '信号与大周期看多方向一致'}
            elif major_status == 'bearish' and not signal_bullish:
                return {'consistent': True, 'reason': '信号与大周期看空方向一致'}
            elif major_status == 'neutral':
                return {'consistent': True, 'reason': '大周期中性，允许小周期信号'}
            else:
                return {
                    'consistent': False, 
                    'reason': f'大周期{major_status}与信号{signal.action}方向冲突'
                }
                
        except Exception as e:
            logger.warning(f"检查信号一致性失败: {str(e)}")
            return {'consistent': False, 'reason': f'检查失败: {str(e)}'}
    
    def _enhance_probability_analysis(self, signal: UnifiedSignal) -> Dict[str, Any]:
        """增强概率分析"""
        try:
            # 获取多级别数据
            multi_data = self.multi_level_analyzer.get_multi_level_data(limit=200)
            if not multi_data:
                return {'enhanced': False, 'reason': '无法获取数据'}
            
            # 构建结构数据
            structure_data = {'multi_level_analysis': {}}
            for timeframe, df in multi_data.items():
                try:
                    analyzer = self.multi_level_analyzer.analyzers[timeframe]
                    analyzer.analyze(df)
                    
                    structure_data['multi_level_analysis'][timeframe] = {
                        'bi_list': analyzer.bi_list,
                        'xd_list': analyzer.xd_list,
                        'fx_list': analyzer.fx_list
                    }
                except Exception as e:
                    logger.warning(f"分析 {timeframe} 结构失败: {str(e)}")
            
            # 使用概率优化器
            optimized_scenarios = self.probability_optimizer.optimize_probability_calculation(
                multi_data, structure_data
            )
            
            # 获取概率置信度
            confidence_info = self.probability_optimizer.get_probability_confidence(optimized_scenarios)
            
            return {
                'enhanced': True,
                'scenarios': optimized_scenarios,
                'confidence_info': confidence_info,
                'primary_probability': optimized_scenarios[0].probability if optimized_scenarios else 0.0
            }
            
        except Exception as e:
            logger.warning(f"增强概率分析失败: {str(e)}")
            return {'enhanced': False, 'reason': f'分析失败: {str(e)}'}
    
    def _make_final_decision(self, signal: UnifiedSignal, major_trend: Dict[str, Any],
                           probability_analysis: Dict[str, Any], 
                           validation_report: Optional[ValidationReport]) -> TradingDecision:
        """做出最终决策"""
        try:
            # 基础决策信息
            action = signal.action
            confidence = signal.confidence
            entry_price = signal.entry_price
            stop_loss = signal.stop_loss
            take_profits = signal.take_profits
            position_size = signal.position_size
            risk_level = signal.risk_level
            timeframe = signal.timeframe
            
            # 综合置信度调整
            if probability_analysis.get('enhanced'):
                prob_confidence = probability_analysis['confidence_info']['score']
                confidence = (confidence + prob_confidence) / 2
            
            if validation_report:
                validation_score = validation_report.achievability_score
                confidence = (confidence + validation_score) / 2
            
            # 风险等级调整
            if validation_report and validation_report.risk_reward_ratio < 1.5:
                risk_level = 'HIGH'
            
            # 生成综合理由
            reasoning_parts = [signal.reasoning]
            
            if major_trend['confidence'] > 0.7:
                reasoning_parts.append(f"大周期{major_trend['status']}趋势明确")
            
            if probability_analysis.get('enhanced'):
                primary_prob = probability_analysis.get('primary_probability', 0)
                reasoning_parts.append(f"优化概率分析显示{primary_prob:.1%}概率")
            
            if validation_report:
                reasoning_parts.append(f"止盈止损验证评分{validation_report.achievability_score:.2f}")
            
            reasoning = "; ".join(reasoning_parts)
            
            # 收集警告和建议
            warnings = []
            recommendations = []
            
            if confidence < self.config['min_confidence']:
                warnings.append(f"置信度{confidence:.1%}低于最小要求{self.config['min_confidence']:.1%}")
            
            if validation_report:
                warnings.extend(validation_report.warnings)
                recommendations.extend(validation_report.recommendations)
            
            # 最终决策检查
            if confidence < self.config['min_confidence']:
                action = 'wait'
                reasoning = f"置信度不足，建议等待: {reasoning}"
            
            return TradingDecision(
                action=action,
                confidence=confidence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profits=take_profits,
                position_size=position_size,
                risk_level=risk_level,
                timeframe=timeframe,
                reasoning=reasoning,
                validation_report=validation_report,
                major_trend_status=major_trend,
                probability_analysis=probability_analysis,
                warnings=warnings,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"做出最终决策失败: {str(e)}")
            return self._create_error_decision(str(e))
    
    def _create_wait_decision(self, reason: str, major_trend: Dict[str, Any],
                            validation_report: Optional[ValidationReport] = None) -> TradingDecision:
        """创建等待决策"""
        return TradingDecision(
            action='wait',
            confidence=0.8,
            entry_price=0.0,
            stop_loss=0.0,
            take_profits=[],
            position_size='NONE',
            risk_level='LOW',
            timeframe='all',
            reasoning=reason,
            validation_report=validation_report,
            major_trend_status=major_trend,
            probability_analysis={'enhanced': False},
            warnings=[],
            recommendations=['等待更好的交易机会']
        )
    
    def _create_error_decision(self, error_msg: str) -> TradingDecision:
        """创建错误决策"""
        return TradingDecision(
            action='wait',
            confidence=0.0,
            entry_price=0.0,
            stop_loss=0.0,
            take_profits=[],
            position_size='NONE',
            risk_level='HIGH',
            timeframe='unknown',
            reasoning=f"系统错误: {error_msg}",
            validation_report=None,
            major_trend_status={'status': 'unknown', 'confidence': 0},
            probability_analysis={'enhanced': False},
            warnings=[f"系统错误: {error_msg}"],
            recommendations=['修复系统错误后重新分析']
        )

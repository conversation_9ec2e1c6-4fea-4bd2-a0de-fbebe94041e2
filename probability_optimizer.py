#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
大概率走势优化器
解决问题6: 大概率走势分析不准，达不到效果
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trend_evolution_engine import TrendEvolutionEngine, TrendScenario, TrendType

class ProbabilityMethod(Enum):
    """概率计算方法"""
    TRADITIONAL = "traditional"    # 传统权重法
    MACHINE_LEARNING = "ml"       # 机器学习法
    ENSEMBLE = "ensemble"         # 集成方法
    ADAPTIVE = "adaptive"         # 自适应方法

@dataclass
class ProbabilityFactors:
    """概率影响因子"""
    structure_strength: float     # 结构强度 0-1
    volume_confirmation: float    # 成交量确认 0-1
    multi_level_consensus: float  # 多级别共识 0-1
    technical_momentum: float     # 技术动量 0-1
    market_sentiment: float       # 市场情绪 0-1
    time_cycle_position: float    # 时间周期位置 0-1
    historical_accuracy: float    # 历史准确率 0-1

class ProbabilityOptimizer:
    """大概率走势优化器"""
    
    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate"):
        self.symbol = symbol
        self.exchange = exchange
        
        # 初始化走势推演引擎
        self.evolution_engine = TrendEvolutionEngine(symbol=symbol, exchange=exchange)
        
        # 概率计算方法
        self.method = ProbabilityMethod.ADAPTIVE
        
        # 动态权重配置
        self.dynamic_weights = {
            'structure_analysis': 0.25,      # 结构分析权重
            'volume_analysis': 0.15,         # 成交量分析权重
            'multi_level_joint': 0.20,       # 多级别联立权重
            'technical_momentum': 0.15,      # 技术动量权重
            'market_sentiment': 0.10,        # 市场情绪权重
            'time_cycle': 0.10,              # 时间周期权重
            'historical_feedback': 0.05      # 历史反馈权重
        }
        
        # 历史准确率记录
        self.accuracy_history = []
        self.prediction_history = []
        
        # 概率阈值配置
        self.probability_thresholds = {
            'very_high': 0.75,    # 非常高概率
            'high': 0.60,         # 高概率
            'medium': 0.45,       # 中等概率
            'low': 0.30,          # 低概率
            'very_low': 0.15      # 非常低概率
        }
        
        logger.info(f"大概率走势优化器初始化完成 - {symbol}")
    
    def optimize_probability_calculation(self, market_data: Dict[str, pd.DataFrame], 
                                       structure_data: Dict[str, Any]) -> List[TrendScenario]:
        """
        优化概率计算
        
        参数:
            market_data: 多级别市场数据
            structure_data: 结构数据
            
        返回:
            List[TrendScenario]: 优化后的走势情景列表
        """
        try:
            logger.info("开始优化概率计算")
            
            # 1. 提取概率影响因子
            factors = self._extract_probability_factors(market_data, structure_data)
            
            # 2. 生成基础走势情景
            base_scenarios = self._generate_base_scenarios(market_data, structure_data)
            
            # 3. 根据选择的方法计算概率
            if self.method == ProbabilityMethod.TRADITIONAL:
                optimized_scenarios = self._calculate_traditional_probability(base_scenarios, factors)
            elif self.method == ProbabilityMethod.MACHINE_LEARNING:
                optimized_scenarios = self._calculate_ml_probability(base_scenarios, factors)
            elif self.method == ProbabilityMethod.ENSEMBLE:
                optimized_scenarios = self._calculate_ensemble_probability(base_scenarios, factors)
            else:  # ADAPTIVE
                optimized_scenarios = self._calculate_adaptive_probability(base_scenarios, factors)
            
            # 4. 应用历史反馈调整
            optimized_scenarios = self._apply_historical_feedback(optimized_scenarios)
            
            # 5. 概率归一化和排序
            optimized_scenarios = self._normalize_and_sort_probabilities(optimized_scenarios)
            
            # 6. 验证概率合理性
            optimized_scenarios = self._validate_probability_distribution(optimized_scenarios)
            
            logger.info(f"概率优化完成，生成 {len(optimized_scenarios)} 个情景")
            return optimized_scenarios
            
        except Exception as e:
            logger.error(f"优化概率计算失败: {str(e)}")
            return []
    
    def _extract_probability_factors(self, market_data: Dict[str, pd.DataFrame], 
                                   structure_data: Dict[str, Any]) -> ProbabilityFactors:
        """提取概率影响因子"""
        try:
            # 1. 结构强度分析
            structure_strength = self._analyze_structure_strength(structure_data)
            
            # 2. 成交量确认分析
            volume_confirmation = self._analyze_volume_confirmation(market_data)
            
            # 3. 多级别共识分析
            multi_level_consensus = self._analyze_multi_level_consensus(market_data, structure_data)
            
            # 4. 技术动量分析
            technical_momentum = self._analyze_technical_momentum(market_data)
            
            # 5. 市场情绪分析
            market_sentiment = self._analyze_market_sentiment(market_data)
            
            # 6. 时间周期位置分析
            time_cycle_position = self._analyze_time_cycle_position(market_data)
            
            # 7. 历史准确率
            historical_accuracy = self._get_historical_accuracy()
            
            return ProbabilityFactors(
                structure_strength=structure_strength,
                volume_confirmation=volume_confirmation,
                multi_level_consensus=multi_level_consensus,
                technical_momentum=technical_momentum,
                market_sentiment=market_sentiment,
                time_cycle_position=time_cycle_position,
                historical_accuracy=historical_accuracy
            )
            
        except Exception as e:
            logger.warning(f"提取概率因子失败: {str(e)}")
            return ProbabilityFactors(0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5)
    
    def _analyze_structure_strength(self, structure_data: Dict[str, Any]) -> float:
        """分析结构强度"""
        try:
            strength_score = 0.5
            
            # 检查多级别结构数据
            multi_level = structure_data.get('multi_level_analysis', {})
            
            for timeframe, analysis in multi_level.items():
                # 笔的完整性
                bi_list = analysis.get('bi_list', [])
                if bi_list and len(bi_list) >= 3:
                    strength_score += 0.1
                
                # 线段的完整性
                xd_list = analysis.get('xd_list', [])
                if xd_list and len(xd_list) >= 2:
                    strength_score += 0.15
                
                # 中枢的存在
                pivot_zones = analysis.get('pivot_zones', [])
                if pivot_zones:
                    strength_score += 0.1
            
            return min(1.0, max(0.0, strength_score))
            
        except Exception as e:
            logger.warning(f"分析结构强度失败: {str(e)}")
            return 0.5
    
    def _analyze_volume_confirmation(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """分析成交量确认"""
        try:
            confirmation_score = 0.5
            
            for timeframe, df in market_data.items():
                if 'volume' not in df.columns or len(df) < 20:
                    continue
                
                # 计算成交量趋势
                recent_volume = df['volume'].tail(5).mean()
                avg_volume = df['volume'].tail(20).mean()
                
                # 成交量放大确认
                if recent_volume > avg_volume * 1.2:
                    confirmation_score += 0.1
                elif recent_volume < avg_volume * 0.8:
                    confirmation_score -= 0.1
                
                # 价量配合
                price_change = (df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5]
                volume_change = (recent_volume - avg_volume) / avg_volume
                
                # 价涨量增或价跌量增
                if (price_change > 0 and volume_change > 0) or (price_change < 0 and volume_change > 0):
                    confirmation_score += 0.1
            
            return min(1.0, max(0.0, confirmation_score))
            
        except Exception as e:
            logger.warning(f"分析成交量确认失败: {str(e)}")
            return 0.5
    
    def _analyze_multi_level_consensus(self, market_data: Dict[str, pd.DataFrame], 
                                     structure_data: Dict[str, Any]) -> float:
        """分析多级别共识"""
        try:
            consensus_score = 0.5
            
            # 检查各级别趋势一致性
            level_trends = []
            multi_level = structure_data.get('multi_level_analysis', {})
            
            for timeframe, analysis in multi_level.items():
                # 获取最新笔的方向
                bi_list = analysis.get('bi_list', [])
                if bi_list:
                    latest_bi = bi_list[-1]
                    bi_direction = latest_bi[4] if len(latest_bi) > 4 else 'unknown'
                    level_trends.append(bi_direction)
            
            # 计算一致性
            if level_trends:
                up_count = level_trends.count('up')
                down_count = level_trends.count('down')
                total_count = len(level_trends)
                
                # 一致性得分
                max_consensus = max(up_count, down_count)
                consensus_ratio = max_consensus / total_count
                
                consensus_score = consensus_ratio
            
            return min(1.0, max(0.0, consensus_score))
            
        except Exception as e:
            logger.warning(f"分析多级别共识失败: {str(e)}")
            return 0.5
    
    def _analyze_technical_momentum(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """分析技术动量"""
        try:
            momentum_score = 0.5
            
            for timeframe, df in market_data.items():
                if len(df) < 20:
                    continue
                
                # MACD动量
                if 'macd' in df.columns:
                    macd_current = df['macd'].iloc[-1]
                    macd_prev = df['macd'].iloc[-2]
                    
                    if macd_current > macd_prev > 0:
                        momentum_score += 0.1  # 上升动量
                    elif macd_current < macd_prev < 0:
                        momentum_score += 0.1  # 下降动量
                
                # RSI动量
                if 'rsi' in df.columns:
                    rsi = df['rsi'].iloc[-1]
                    if 30 < rsi < 70:  # 非超买超卖区域
                        momentum_score += 0.05
                
                # 价格动量
                price_change_5 = (df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5]
                if abs(price_change_5) > 0.02:  # 价格变化超过2%
                    momentum_score += 0.05
            
            return min(1.0, max(0.0, momentum_score))
            
        except Exception as e:
            logger.warning(f"分析技术动量失败: {str(e)}")
            return 0.5

    def _analyze_market_sentiment(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """分析市场情绪"""
        try:
            sentiment_score = 0.5

            # 基于价格行为分析市场情绪
            for timeframe, df in market_data.items():
                if len(df) < 10:
                    continue

                # 连续上涨/下跌天数
                price_changes = df['close'].diff().tail(5)
                consecutive_up = 0
                consecutive_down = 0

                for change in price_changes:
                    if change > 0:
                        consecutive_up += 1
                        consecutive_down = 0
                    elif change < 0:
                        consecutive_down += 1
                        consecutive_up = 0

                # 连续性加分
                if consecutive_up >= 3:
                    sentiment_score += 0.1  # 乐观情绪
                elif consecutive_down >= 3:
                    sentiment_score -= 0.1  # 悲观情绪

                # 波动率分析
                volatility = df['close'].tail(10).std() / df['close'].tail(10).mean()
                if volatility < 0.02:  # 低波动
                    sentiment_score += 0.05
                elif volatility > 0.05:  # 高波动
                    sentiment_score -= 0.05

            return min(1.0, max(0.0, sentiment_score))

        except Exception as e:
            logger.warning(f"分析市场情绪失败: {str(e)}")
            return 0.5

    def _analyze_time_cycle_position(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """分析时间周期位置"""
        try:
            cycle_score = 0.5

            # 基于K线数量和时间分析周期位置
            for timeframe, df in market_data.items():
                if len(df) < 50:
                    continue

                # 计算当前位置在整个周期中的位置
                total_bars = len(df)

                # 基于斐波那契时间比例
                fib_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
                current_position = 1.0  # 当前在最新位置

                # 检查是否接近关键时间节点
                for fib in fib_levels:
                    expected_position = fib
                    if abs(current_position - expected_position) < 0.1:
                        cycle_score += 0.1

                # 周期完整性评估
                if total_bars >= 100:  # 足够的历史数据
                    cycle_score += 0.1

            return min(1.0, max(0.0, cycle_score))

        except Exception as e:
            logger.warning(f"分析时间周期位置失败: {str(e)}")
            return 0.5

    def _get_historical_accuracy(self) -> float:
        """获取历史准确率"""
        try:
            if not self.accuracy_history:
                return 0.5  # 没有历史数据，返回中性值

            # 计算最近的准确率
            recent_accuracy = self.accuracy_history[-10:] if len(self.accuracy_history) >= 10 else self.accuracy_history
            return sum(recent_accuracy) / len(recent_accuracy)

        except Exception as e:
            logger.warning(f"获取历史准确率失败: {str(e)}")
            return 0.5

    def _generate_base_scenarios(self, market_data: Dict[str, pd.DataFrame],
                               structure_data: Dict[str, Any]) -> List[TrendScenario]:
        """生成基础走势情景"""
        try:
            # 使用原有的走势推演引擎生成基础情景
            result = self.evolution_engine.evolve_trend(market_data, structure_data, '15m')

            if result and hasattr(result, 'scenarios'):
                return result.scenarios
            else:
                # 如果没有结果，生成默认情景
                return self._create_default_scenarios()

        except Exception as e:
            logger.warning(f"生成基础情景失败: {str(e)}")
            return self._create_default_scenarios()

    def _create_default_scenarios(self) -> List[TrendScenario]:
        """创建默认情景"""
        scenarios = []

        # 上涨情景
        up_scenario = TrendScenario(
            trend_type=TrendType.UP,
            probability=0.33,
            target_price=None,
            time_horizon=60,
            confidence_level='medium',
            supporting_factors=['默认上涨情景'],
            risk_factors=['缺乏具体分析'],
            description='默认上涨情景'
        )
        scenarios.append(up_scenario)

        # 下跌情景
        down_scenario = TrendScenario(
            trend_type=TrendType.DOWN,
            probability=0.33,
            target_price=None,
            time_horizon=60,
            confidence_level='medium',
            supporting_factors=['默认下跌情景'],
            risk_factors=['缺乏具体分析'],
            description='默认下跌情景'
        )
        scenarios.append(down_scenario)

        # 震荡情景
        consolidation_scenario = TrendScenario(
            trend_type=TrendType.CONSOLIDATION,
            probability=0.34,
            target_price=None,
            time_horizon=60,
            confidence_level='medium',
            supporting_factors=['默认震荡情景'],
            risk_factors=['缺乏具体分析'],
            description='默认震荡情景'
        )
        scenarios.append(consolidation_scenario)

        return scenarios

    def _calculate_adaptive_probability(self, scenarios: List[TrendScenario],
                                      factors: ProbabilityFactors) -> List[TrendScenario]:
        """自适应概率计算"""
        try:
            # 根据历史准确率动态调整权重
            self._adjust_dynamic_weights(factors.historical_accuracy)

            for scenario in scenarios:
                # 基础概率
                base_prob = 1.0 / len(scenarios)  # 均等概率起点

                # 各因子贡献
                structure_contrib = factors.structure_strength * self.dynamic_weights['structure_analysis']
                volume_contrib = factors.volume_confirmation * self.dynamic_weights['volume_analysis']
                consensus_contrib = factors.multi_level_consensus * self.dynamic_weights['multi_level_joint']
                momentum_contrib = factors.technical_momentum * self.dynamic_weights['technical_momentum']
                sentiment_contrib = factors.market_sentiment * self.dynamic_weights['market_sentiment']
                cycle_contrib = factors.time_cycle_position * self.dynamic_weights['time_cycle']
                history_contrib = factors.historical_accuracy * self.dynamic_weights['historical_feedback']

                # 根据情景类型调整贡献
                total_contrib = self._adjust_contributions_by_scenario(
                    scenario, structure_contrib, volume_contrib, consensus_contrib,
                    momentum_contrib, sentiment_contrib, cycle_contrib, history_contrib
                )

                # 计算最终概率
                scenario.probability = base_prob + total_contrib

                # 确保概率在合理范围内
                scenario.probability = min(0.8, max(0.1, scenario.probability))

            return scenarios

        except Exception as e:
            logger.warning(f"自适应概率计算失败: {str(e)}")
            return scenarios

    def _adjust_dynamic_weights(self, historical_accuracy: float):
        """根据历史准确率调整动态权重"""
        try:
            # 如果历史准确率高，增加历史反馈权重
            if historical_accuracy > 0.7:
                self.dynamic_weights['historical_feedback'] = 0.1
                self.dynamic_weights['structure_analysis'] = 0.23
            elif historical_accuracy < 0.4:
                # 历史准确率低，减少历史反馈权重，增加结构分析权重
                self.dynamic_weights['historical_feedback'] = 0.02
                self.dynamic_weights['structure_analysis'] = 0.28

            # 确保权重总和为1
            total_weight = sum(self.dynamic_weights.values())
            for key in self.dynamic_weights:
                self.dynamic_weights[key] /= total_weight

        except Exception as e:
            logger.warning(f"调整动态权重失败: {str(e)}")

    def _adjust_contributions_by_scenario(self, scenario: TrendScenario, *contributions) -> float:
        """根据情景类型调整各因子贡献"""
        try:
            structure_contrib, volume_contrib, consensus_contrib, momentum_contrib, \
            sentiment_contrib, cycle_contrib, history_contrib = contributions

            # 根据趋势类型调整
            if scenario.trend_type == TrendType.UP:
                # 上涨情景：动量和情绪更重要
                momentum_contrib *= 1.2
                sentiment_contrib *= 1.1
            elif scenario.trend_type == TrendType.DOWN:
                # 下跌情景：结构和成交量更重要
                structure_contrib *= 1.2
                volume_contrib *= 1.1
            else:  # CONSOLIDATION
                # 震荡情景：多级别共识更重要
                consensus_contrib *= 1.3

            total_contrib = (structure_contrib + volume_contrib + consensus_contrib +
                           momentum_contrib + sentiment_contrib + cycle_contrib + history_contrib)

            return total_contrib

        except Exception as e:
            logger.warning(f"调整情景贡献失败: {str(e)}")
            return sum(contributions)

    def _calculate_traditional_probability(self, scenarios: List[TrendScenario],
                                         factors: ProbabilityFactors) -> List[TrendScenario]:
        """传统权重法计算概率"""
        try:
            # 使用固定权重
            fixed_weights = {
                'structure': 0.35,
                'volume': 0.15,
                'consensus': 0.20,
                'momentum': 0.15,
                'sentiment': 0.10,
                'cycle': 0.05
            }

            for scenario in scenarios:
                # 简单加权求和
                probability = (
                    factors.structure_strength * fixed_weights['structure'] +
                    factors.volume_confirmation * fixed_weights['volume'] +
                    factors.multi_level_consensus * fixed_weights['consensus'] +
                    factors.technical_momentum * fixed_weights['momentum'] +
                    factors.market_sentiment * fixed_weights['sentiment'] +
                    factors.time_cycle_position * fixed_weights['cycle']
                )

                scenario.probability = min(0.8, max(0.1, probability))

            return scenarios

        except Exception as e:
            logger.warning(f"传统概率计算失败: {str(e)}")
            return scenarios

    def _calculate_ml_probability(self, scenarios: List[TrendScenario],
                                factors: ProbabilityFactors) -> List[TrendScenario]:
        """机器学习法计算概率（简化版）"""
        try:
            # 这里是简化的ML方法，实际应该使用训练好的模型
            # 基于因子的非线性组合

            for scenario in scenarios:
                # 特征向量
                features = np.array([
                    factors.structure_strength,
                    factors.volume_confirmation,
                    factors.multi_level_consensus,
                    factors.technical_momentum,
                    factors.market_sentiment,
                    factors.time_cycle_position,
                    factors.historical_accuracy
                ])

                # 简化的非线性变换
                probability = self._simple_ml_transform(features, scenario.trend_type)
                scenario.probability = min(0.8, max(0.1, probability))

            return scenarios

        except Exception as e:
            logger.warning(f"ML概率计算失败: {str(e)}")
            return scenarios

    def _simple_ml_transform(self, features: np.ndarray, trend_type: TrendType) -> float:
        """简化的ML变换"""
        try:
            # 基于趋势类型的不同权重
            if trend_type == TrendType.UP:
                weights = np.array([0.2, 0.15, 0.25, 0.2, 0.15, 0.05, 0.1])
            elif trend_type == TrendType.DOWN:
                weights = np.array([0.3, 0.2, 0.2, 0.15, 0.1, 0.05, 0.1])
            else:  # CONSOLIDATION
                weights = np.array([0.15, 0.1, 0.35, 0.15, 0.15, 0.1, 0.1])

            # 加权求和 + 非线性激活
            linear_output = np.dot(features, weights)

            # Sigmoid激活函数
            probability = 1 / (1 + np.exp(-5 * (linear_output - 0.5)))

            return probability

        except Exception as e:
            logger.warning(f"ML变换失败: {str(e)}")
            return 0.5

    def _calculate_ensemble_probability(self, scenarios: List[TrendScenario],
                                      factors: ProbabilityFactors) -> List[TrendScenario]:
        """集成方法计算概率"""
        try:
            # 结合多种方法的结果
            traditional_scenarios = self._calculate_traditional_probability(scenarios.copy(), factors)
            ml_scenarios = self._calculate_ml_probability(scenarios.copy(), factors)
            adaptive_scenarios = self._calculate_adaptive_probability(scenarios.copy(), factors)

            # 集成权重
            ensemble_weights = [0.3, 0.4, 0.3]  # 传统、ML、自适应

            for i, scenario in enumerate(scenarios):
                # 加权平均
                ensemble_prob = (
                    traditional_scenarios[i].probability * ensemble_weights[0] +
                    ml_scenarios[i].probability * ensemble_weights[1] +
                    adaptive_scenarios[i].probability * ensemble_weights[2]
                )

                scenario.probability = min(0.8, max(0.1, ensemble_prob))

            return scenarios

        except Exception as e:
            logger.warning(f"集成概率计算失败: {str(e)}")
            return scenarios

    def _apply_historical_feedback(self, scenarios: List[TrendScenario]) -> List[TrendScenario]:
        """应用历史反馈调整"""
        try:
            if not self.prediction_history:
                return scenarios

            # 分析历史预测的偏差
            for scenario in scenarios:
                # 根据历史表现调整概率
                historical_bias = self._calculate_historical_bias(scenario.trend_type)

                # 应用偏差修正
                scenario.probability *= (1 + historical_bias)
                scenario.probability = min(0.8, max(0.1, scenario.probability))

            return scenarios

        except Exception as e:
            logger.warning(f"应用历史反馈失败: {str(e)}")
            return scenarios

    def _calculate_historical_bias(self, trend_type: TrendType) -> float:
        """计算历史偏差"""
        try:
            # 简化的偏差计算
            if len(self.prediction_history) < 5:
                return 0.0

            # 统计该趋势类型的历史准确率
            type_predictions = [p for p in self.prediction_history if p['predicted_type'] == trend_type]

            if not type_predictions:
                return 0.0

            # 计算准确率
            correct_predictions = sum(1 for p in type_predictions if p['correct'])
            accuracy = correct_predictions / len(type_predictions)

            # 转换为偏差调整
            if accuracy > 0.6:
                return 0.1  # 历史表现好，增加概率
            elif accuracy < 0.4:
                return -0.1  # 历史表现差，减少概率
            else:
                return 0.0

        except Exception as e:
            logger.warning(f"计算历史偏差失败: {str(e)}")
            return 0.0

    def _normalize_and_sort_probabilities(self, scenarios: List[TrendScenario]) -> List[TrendScenario]:
        """概率归一化和排序"""
        try:
            # 归一化概率
            total_prob = sum(s.probability for s in scenarios)
            if total_prob > 0:
                for scenario in scenarios:
                    scenario.probability = scenario.probability / total_prob

            # 按概率排序
            scenarios.sort(key=lambda x: x.probability, reverse=True)

            return scenarios

        except Exception as e:
            logger.warning(f"概率归一化失败: {str(e)}")
            return scenarios

    def _validate_probability_distribution(self, scenarios: List[TrendScenario]) -> List[TrendScenario]:
        """验证概率分布合理性"""
        try:
            # 检查概率分布是否合理
            probabilities = [s.probability for s in scenarios]

            # 最高概率不应该过于极端
            max_prob = max(probabilities)
            if max_prob > 0.8:
                # 重新分配概率
                excess = max_prob - 0.8
                scenarios[0].probability = 0.8

                # 将多余的概率分配给其他情景
                if len(scenarios) > 1:
                    for i in range(1, len(scenarios)):
                        scenarios[i].probability += excess / (len(scenarios) - 1)

            # 最低概率不应该过低
            min_prob = min(probabilities)
            if min_prob < 0.1:
                for scenario in scenarios:
                    if scenario.probability < 0.1:
                        scenario.probability = 0.1

            # 重新归一化
            total_prob = sum(s.probability for s in scenarios)
            if total_prob > 0:
                for scenario in scenarios:
                    scenario.probability = scenario.probability / total_prob

            return scenarios

        except Exception as e:
            logger.warning(f"验证概率分布失败: {str(e)}")
            return scenarios

    def record_prediction_result(self, predicted_scenario: TrendScenario, actual_result: str):
        """记录预测结果用于反馈学习"""
        try:
            # 判断预测是否正确
            correct = (predicted_scenario.trend_type.value == actual_result)

            # 记录预测历史
            prediction_record = {
                'predicted_type': predicted_scenario.trend_type,
                'predicted_probability': predicted_scenario.probability,
                'actual_result': actual_result,
                'correct': correct,
                'timestamp': pd.Timestamp.now()
            }

            self.prediction_history.append(prediction_record)

            # 更新准确率历史
            self.accuracy_history.append(1.0 if correct else 0.0)

            # 保持历史记录在合理范围内
            if len(self.prediction_history) > 100:
                self.prediction_history = self.prediction_history[-100:]
            if len(self.accuracy_history) > 100:
                self.accuracy_history = self.accuracy_history[-100:]

            logger.info(f"记录预测结果: 预测={predicted_scenario.trend_type.value}, "
                       f"实际={actual_result}, 正确={correct}")

        except Exception as e:
            logger.warning(f"记录预测结果失败: {str(e)}")

    def get_probability_confidence(self, scenarios: List[TrendScenario]) -> Dict[str, Any]:
        """获取概率置信度评估"""
        try:
            if not scenarios:
                return {'confidence': 'low', 'score': 0.0, 'reason': '无有效情景'}

            # 计算概率分布的集中度
            probabilities = [s.probability for s in scenarios]
            max_prob = max(probabilities)
            prob_std = np.std(probabilities)

            # 置信度评分
            confidence_score = max_prob - prob_std

            # 置信度等级
            if confidence_score >= 0.6:
                confidence_level = 'very_high'
            elif confidence_score >= 0.5:
                confidence_level = 'high'
            elif confidence_score >= 0.4:
                confidence_level = 'medium'
            elif confidence_score >= 0.3:
                confidence_level = 'low'
            else:
                confidence_level = 'very_low'

            # 生成置信度理由
            reason = f"主要情景概率{max_prob:.1%}，概率分布标准差{prob_std:.3f}"

            return {
                'confidence': confidence_level,
                'score': confidence_score,
                'reason': reason,
                'max_probability': max_prob,
                'probability_std': prob_std
            }

        except Exception as e:
            logger.warning(f"获取概率置信度失败: {str(e)}")
            return {'confidence': 'unknown', 'score': 0.0, 'reason': '计算失败'}

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
走势推演引擎集成示例
演示如何将走势推演引擎集成到现有的交易系统中
"""

import pandas as pd
import numpy as np
from datetime import datetime
from trend_evolution_engine import TrendEvolutionEngine, TrendType, EvolutionResult

class EnhancedTradingSystem:
    """增强的交易系统，集成走势推演功能"""
    
    def __init__(self):
        """初始化交易系统"""
        self.evolution_engine = TrendEvolutionEngine()
        self.current_position = None
        self.position_size = 0
        self.entry_price = 0
        self.stop_loss = 0
        self.profit_targets = []
        
        print("🚀 增强交易系统初始化完成")
    
    def analyze_market_with_evolution(self, market_data: dict, structure_data: dict, 
                                    current_level: str = '15m') -> dict:
        """
        使用走势推演引擎分析市场
        
        参数:
            market_data: 多级别市场数据
            structure_data: 缠论结构数据
            current_level: 主要分析级别
            
        返回:
            dict: 综合分析结果
        """
        print(f"📊 开始走势推演分析 (主级别: {current_level})")
        
        try:
            # 执行走势推演
            evolution_result = self.evolution_engine.evolve_trend(
                market_data, structure_data, current_level
            )
            
            # 获取当前价格
            current_price = float(market_data[current_level]['close'].iloc[-1])
            
            # 生成综合分析结果
            analysis_result = {
                'evolution_result': evolution_result,
                'current_price': current_price,
                'analysis_summary': self._generate_analysis_summary(evolution_result),
                'trading_signals': self._generate_trading_signals(evolution_result, current_price),
                'risk_management': self._generate_risk_management(evolution_result, current_price),
                'position_advice': self._generate_position_advice(evolution_result)
            }
            
            print(f"✅ 走势推演分析完成")
            return analysis_result
            
        except Exception as e:
            print(f"❌ 走势推演分析失败: {str(e)}")
            return self._get_default_analysis()
    
    def _generate_analysis_summary(self, result: EvolutionResult) -> dict:
        """生成分析摘要"""
        primary = result.primary_scenario
        
        return {
            'main_trend': primary.trend_type.value,
            'probability': primary.probability,
            'confidence': primary.confidence_level,
            'time_horizon': primary.time_horizon,
            'key_factors': primary.supporting_factors[:3],  # 取前3个关键因素
            'main_risks': result.risk_assessment.get('primary_risks', [])[:2]  # 取前2个主要风险
        }
    
    def _generate_trading_signals(self, result: EvolutionResult, current_price: float) -> dict:
        """生成交易信号"""
        primary = result.primary_scenario
        
        signals = {
            'action': result.recommended_action,
            'strength': self._calculate_signal_strength(result),
            'entry_conditions': [],
            'exit_conditions': []
        }
        
        # 生成入场条件
        if result.recommended_action == 'buy':
            signals['entry_conditions'] = [
                f"价格突破 {current_price * 1.002:.2f}",
                "成交量放大确认",
                f"止损设置在 {result.stop_loss:.2f}"
            ]
        elif result.recommended_action == 'sell':
            signals['entry_conditions'] = [
                f"价格跌破 {current_price * 0.998:.2f}",
                "成交量配合下跌",
                f"止损设置在 {result.stop_loss:.2f}"
            ]
        else:
            signals['entry_conditions'] = [
                "等待更明确的信号",
                "观察关键位置突破",
                "保持轻仓或空仓"
            ]
        
        # 生成出场条件
        if result.profit_target:
            profit_pct = (result.profit_target - current_price) / current_price * 100
            signals['exit_conditions'] = [
                f"止盈目标: {result.profit_target:.2f} ({profit_pct:+.1f}%)",
                f"止损位: {result.stop_loss:.2f}",
                "根据后续信号调整"
            ]
        
        return signals
    
    def _calculate_signal_strength(self, result: EvolutionResult) -> str:
        """计算信号强度"""
        probability = result.primary_scenario.probability
        confidence = result.primary_scenario.confidence_level
        
        if probability >= 0.7 and confidence == 'high':
            return 'strong'
        elif probability >= 0.6 and confidence in ['high', 'medium']:
            return 'medium'
        else:
            return 'weak'
    
    def _generate_risk_management(self, result: EvolutionResult, current_price: float) -> dict:
        """生成风险管理建议"""
        risk_mgmt = {
            'position_sizing': result.position_size,
            'max_risk_per_trade': '2%',  # 每笔交易最大风险
            'stop_loss_levels': result.risk_assessment.get('stop_loss_levels', {}),
            'risk_factors': result.risk_assessment.get('primary_risks', []),
            'contingency_plans': []
        }
        
        # 生成应急计划
        primary_prob = result.primary_scenario.probability
        if primary_prob < 0.6:
            risk_mgmt['contingency_plans'].append("主要情景概率较低，准备快速止损")
        
        if len(result.alternative_scenarios) > 0:
            alt_prob = result.alternative_scenarios[0].probability
            if alt_prob > 0.3:
                risk_mgmt['contingency_plans'].append(f"备选情景概率{alt_prob:.1%}，准备调整策略")
        
        return risk_mgmt
    
    def _generate_position_advice(self, result: EvolutionResult) -> dict:
        """生成仓位建议"""
        advice = {
            'recommended_size': result.position_size,
            'entry_strategy': 'single',  # single/batch
            'holding_period': result.primary_scenario.time_horizon,
            'adjustment_triggers': []
        }
        
        # 根据置信度调整入场策略
        if result.primary_scenario.confidence_level == 'low':
            advice['entry_strategy'] = 'batch'
            advice['adjustment_triggers'].append("分批建仓，降低风险")
        
        # 根据概率调整持仓策略
        if result.primary_scenario.probability < 0.6:
            advice['adjustment_triggers'].append("概率较低，保持灵活性")
        
        return advice
    
    def execute_trading_decision(self, analysis_result: dict) -> dict:
        """执行交易决策"""
        signals = analysis_result['trading_signals']
        risk_mgmt = analysis_result['risk_management']
        current_price = analysis_result['current_price']
        
        decision = {
            'action_taken': 'none',
            'reason': '',
            'position_change': 0,
            'new_stop_loss': None,
            'new_targets': []
        }
        
        try:
            action = signals['action']
            strength = signals['strength']
            
            # 根据信号强度和当前仓位决定操作
            if action == 'buy' and strength in ['strong', 'medium']:
                if self.current_position != 'long':
                    decision = self._execute_buy(analysis_result)
                else:
                    decision['reason'] = '已持有多头仓位'
            
            elif action == 'sell' and strength in ['strong', 'medium']:
                if self.current_position != 'short':
                    decision = self._execute_sell(analysis_result)
                else:
                    decision['reason'] = '已持有空头仓位'
            
            elif action == 'wait' or strength == 'weak':
                if self.current_position is not None:
                    decision = self._evaluate_current_position(analysis_result)
                else:
                    decision['reason'] = '等待更好的入场机会'
            
            return decision
            
        except Exception as e:
            print(f"❌ 执行交易决策失败: {str(e)}")
            decision['reason'] = f'执行失败: {str(e)}'
            return decision
    
    def _execute_buy(self, analysis_result: dict) -> dict:
        """执行买入操作"""
        evolution_result = analysis_result['evolution_result']
        current_price = analysis_result['current_price']
        
        # 计算仓位大小
        position_ratio = self._get_position_ratio(evolution_result.position_size)
        
        # 更新仓位信息
        self.current_position = 'long'
        self.position_size = position_ratio
        self.entry_price = current_price
        self.stop_loss = evolution_result.stop_loss
        self.profit_targets = [evolution_result.profit_target] if evolution_result.profit_target else []
        
        return {
            'action_taken': 'buy',
            'reason': f'走势推演显示{evolution_result.primary_scenario.trend_type.value}概率{evolution_result.primary_scenario.probability:.1%}',
            'position_change': position_ratio,
            'new_stop_loss': self.stop_loss,
            'new_targets': self.profit_targets
        }
    
    def _execute_sell(self, analysis_result: dict) -> dict:
        """执行卖出操作"""
        evolution_result = analysis_result['evolution_result']
        current_price = analysis_result['current_price']
        
        # 计算仓位大小
        position_ratio = self._get_position_ratio(evolution_result.position_size)
        
        # 更新仓位信息
        self.current_position = 'short'
        self.position_size = position_ratio
        self.entry_price = current_price
        self.stop_loss = evolution_result.stop_loss
        self.profit_targets = [evolution_result.profit_target] if evolution_result.profit_target else []
        
        return {
            'action_taken': 'sell',
            'reason': f'走势推演显示{evolution_result.primary_scenario.trend_type.value}概率{evolution_result.primary_scenario.probability:.1%}',
            'position_change': -position_ratio,
            'new_stop_loss': self.stop_loss,
            'new_targets': self.profit_targets
        }
    
    def _evaluate_current_position(self, analysis_result: dict) -> dict:
        """评估当前仓位"""
        evolution_result = analysis_result['evolution_result']
        current_price = analysis_result['current_price']
        
        # 检查是否需要调整止损或止盈
        decision = {
            'action_taken': 'hold',
            'reason': '持有当前仓位',
            'position_change': 0,
            'new_stop_loss': None,
            'new_targets': []
        }
        
        # 根据新的推演结果调整止损止盈
        if evolution_result.stop_loss and abs(evolution_result.stop_loss - self.stop_loss) / self.stop_loss > 0.01:
            decision['new_stop_loss'] = evolution_result.stop_loss
            decision['reason'] += '，调整止损位'
        
        return decision
    
    def _get_position_ratio(self, position_size: str) -> float:
        """获取仓位比例"""
        size_mapping = {
            'light': 0.3,
            'medium': 0.5,
            'heavy': 0.8
        }
        return size_mapping.get(position_size, 0.3)
    
    def _get_default_analysis(self) -> dict:
        """获取默认分析结果"""
        return {
            'evolution_result': None,
            'current_price': 0,
            'analysis_summary': {'main_trend': 'unknown', 'probability': 0.5},
            'trading_signals': {'action': 'wait', 'strength': 'weak'},
            'risk_management': {'position_sizing': 'light'},
            'position_advice': {'recommended_size': 'light'}
        }
    
    def print_analysis_report(self, analysis_result: dict):
        """打印分析报告"""
        print("\n" + "="*80)
        print("📊 走势推演分析报告")
        print("="*80)
        
        summary = analysis_result['analysis_summary']
        signals = analysis_result['trading_signals']
        risk_mgmt = analysis_result['risk_management']
        
        print(f"\n🎯 主要结论:")
        print(f"   走势类型: {summary['main_trend']}")
        print(f"   概率: {summary['probability']:.1%}")
        print(f"   置信度: {summary['confidence']}")
        
        print(f"\n📈 交易信号:")
        print(f"   推荐操作: {signals['action']}")
        print(f"   信号强度: {signals['strength']}")
        
        if signals['entry_conditions']:
            print(f"   入场条件: {', '.join(signals['entry_conditions'][:2])}")
        
        print(f"\n⚠️ 风险管理:")
        print(f"   建议仓位: {risk_mgmt['position_sizing']}")
        print(f"   主要风险: {', '.join(summary['main_risks'][:2])}")
        
        print(f"\n📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def demo_integration():
    """演示集成使用"""
    print("🚀 开始走势推演引擎集成演示")
    
    # 创建增强交易系统
    trading_system = EnhancedTradingSystem()
    
    # 模拟市场数据（实际使用时从数据源获取）
    dates = pd.date_range(start='2024-01-01', periods=100, freq='15min')
    np.random.seed(42)
    
    base_price = 50000
    prices = []
    for i in range(100):
        change = np.random.normal(0, 0.01)
        if i > 0:
            base_price = prices[-1]['close'] * (1 + change)
        
        prices.append({
            'timestamp': dates[i],
            'open': base_price,
            'high': base_price * 1.005,
            'low': base_price * 0.995,
            'close': base_price,
            'volume': np.random.randint(1000, 10000)
        })
    
    df = pd.DataFrame(prices)
    
    # 创建多级别数据
    market_data = {
        '5m': df.copy(),
        '15m': df.copy(),
        '30m': df.copy(),
        '1h': df.copy()
    }
    
    # 模拟结构数据
    structure_data = {
        'bi_list': [(10, 30, 49500, 51200, 'up'), (30, 50, 51200, 49800, 'down')],
        'xd_list': [(10, 50, 49500, 51200, 'up')],
        'pivot_zones': [{'high': 51000, 'low': 49500, 'level': '15m', 'oscillation_count': 3}]
    }
    
    # 执行分析
    analysis_result = trading_system.analyze_market_with_evolution(market_data, structure_data)
    
    # 打印分析报告
    trading_system.print_analysis_report(analysis_result)
    
    # 执行交易决策
    decision = trading_system.execute_trading_decision(analysis_result)
    
    print(f"\n💡 交易决策:")
    print(f"   执行操作: {decision['action_taken']}")
    print(f"   决策理由: {decision['reason']}")
    if decision['position_change'] != 0:
        print(f"   仓位变化: {decision['position_change']:+.1%}")
    
    print(f"\n✅ 集成演示完成")


if __name__ == "__main__":
    demo_integration()

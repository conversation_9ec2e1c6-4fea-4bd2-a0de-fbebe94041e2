#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
走势推演引擎轻量版演示
展示核心功能：线上数据获取 + 走势分析 + 图表数据输出
"""

import json
from datetime import datetime
from loguru import logger
from trend_evolution_lite import TrendEvolutionLite

def display_result(result):
    """显示分析结果"""
    print("\n" + "=" * 80)
    print("📈 走势推演分析结果")
    print("=" * 80)
    
    # 主要情景
    primary = result.primary_scenario
    print(f"🎯 主要情景: {primary.trend_type.value.upper()}")
    print(f"📊 概率: {primary.probability:.1%}")
    print(f"🔍 置信度: {primary.confidence_level.upper()}")
    print(f"⏰ 时间预期: {primary.time_horizon}分钟" if primary.time_horizon else "⏰ 时间预期: 未知")
    print(f"📝 描述: {primary.description}")
    
    # 支持和风险因素
    if primary.supporting_factors:
        print(f"\n✅ 支持因素:")
        for factor in primary.supporting_factors:
            print(f"   • {factor}")
            
    if primary.risk_factors:
        print(f"\n⚠️ 风险因素:")
        for factor in primary.risk_factors:
            print(f"   • {factor}")
    
    # 操作建议
    print(f"\n💡 推荐操作: {result.recommended_action.upper()}")
    print(f"💰 仓位建议: {result.position_size.upper()}")
    
    # 价格目标
    if result.profit_target:
        print(f"🎯 止盈目标: ${result.profit_target:,.2f}")
    if result.stop_loss:
        print(f"🛡️ 止损位: ${result.stop_loss:,.2f}")
    
    # 备选情景
    if result.alternative_scenarios:
        print(f"\n🔄 备选情景:")
        for i, scenario in enumerate(result.alternative_scenarios, 1):
            print(f"   {i}. {scenario.trend_type.value.upper()} "
                  f"(概率: {scenario.probability:.1%}, "
                  f"置信度: {scenario.confidence_level})")
    
    # 图表信息
    if result.chart_info:
        print(f"\n📊 图表数据已保存: {result.chart_info}")
        
        # 显示图表数据内容
        try:
            with open(result.chart_info, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            print(f"\n📈 技术指标:")
            print(f"   当前价格: ${chart_data['current_price']:,.2f}")
            print(f"   支撑位: ${chart_data['support']:,.2f}")
            print(f"   阻力位: ${chart_data['resistance']:,.2f}")
            print(f"   MA5: ${chart_data['ma_levels']['ma_5']:,.2f}")
            print(f"   MA10: ${chart_data['ma_levels']['ma_10']:,.2f}")
            print(f"   MA20: ${chart_data['ma_levels']['ma_20']:,.2f}")
            print(f"   15m趋势: {chart_data['trend_15m'].upper()}")
            print(f"   1h趋势: {chart_data['trend_1h'].upper()}")
            
        except Exception as e:
            print(f"   (图表数据读取失败: {str(e)})")
    
    print(f"\n📅 分析时间: {result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 数据来源: {result.data_source.upper()}")

def main():
    """主演示函数"""
    print("=" * 80)
    print("🚀 走势推演引擎轻量版演示")
    print("=" * 80)
    print("功能展示：")
    print("✅ 1. 接入线上数据 - 自动获取实时K线数据")
    print("✅ 2. 多级别分析 - 15分钟和1小时级别联立分析")
    print("✅ 3. 走势推演 - 基于缠论理论的概率分析")
    print("✅ 4. 图表数据输出 - 生成JSON格式的图表数据")
    print("=" * 80)
    
    # 配置日志
    logger.add("logs/demo_lite_{time}.log", rotation="10 MB")
    
    try:
        # 创建走势推演引擎
        print("\n📊 初始化走势推演引擎...")
        engine = TrendEvolutionLite(symbol="BTC/USDT", exchange="gate")
        print("✅ 引擎初始化完成")
        
        # 执行走势推演
        print("\n📡 开始获取线上数据并执行走势推演...")
        print("⏳ 正在分析中，请稍候...")
        
        result = engine.evolve_trend_live()
        
        # 显示结果
        display_result(result)
        
        # 操作建议总结
        print("\n" + "=" * 80)
        print("💡 操作建议总结")
        print("=" * 80)
        
        action_map = {
            'buy': '🟢 建议买入',
            'sell': '🔴 建议卖出', 
            'wait': '🟡 建议观望'
        }
        
        print(f"{action_map.get(result.recommended_action, '❓ 未知操作')}")
        
        if result.recommended_action != 'wait':
            print(f"仓位: {result.position_size}")
            if result.profit_target:
                print(f"目标价: ${result.profit_target:,.2f}")
            if result.stop_loss:
                print(f"止损价: ${result.stop_loss:,.2f}")
        
        print("\n⚠️ 风险提示:")
        print("• 本分析仅供参考，不构成投资建议")
        print("• 市场有风险，投资需谨慎")
        print("• 请结合其他分析方法综合判断")
        print("• 严格执行风险管理策略")
        
        print("\n🔄 建议:")
        print("• 定期重新运行分析以获取最新状态")
        print("• 关注市场突发事件对分析的影响")
        print("• 可以尝试不同的交易对进行分析")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        return False
        
    except Exception as e:
        logger.error(f"演示失败: {str(e)}")
        print(f"\n❌ 演示失败: {str(e)}")
        print("💡 请检查网络连接和数据源配置")
        return False

def quick_analysis(symbol: str = "BTC/USDT"):
    """快速分析"""
    try:
        print(f"🔍 快速分析 {symbol}...")
        
        engine = TrendEvolutionLite(symbol=symbol)
        result = engine.evolve_trend_live()
        
        primary = result.primary_scenario
        print(f"📊 {symbol} 主要情景: {primary.trend_type.value.upper()} "
              f"(概率: {primary.probability:.1%}, 置信度: {primary.confidence_level})")
        print(f"💡 建议: {result.recommended_action.upper()}")
        
        return result
        
    except Exception as e:
        print(f"❌ 快速分析失败: {str(e)}")
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            # 快速分析模式
            symbol = sys.argv[2] if len(sys.argv) > 2 else "BTC/USDT"
            quick_analysis(symbol)
        else:
            print("用法: python3 demo_lite.py [quick] [symbol]")
            print("示例: python3 demo_lite.py quick ETH/USDT")
    else:
        # 完整演示模式
        main()

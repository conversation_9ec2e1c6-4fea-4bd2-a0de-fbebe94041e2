#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
走势推演引擎：
基于缠论理论和走势推演文章的理解，实现最大概率走势类型判断功能

核心功能：
1. 结构思维分析 - 分析当前走势结构的完整性和生长状态
2. 多空博弈分析 - 判断多空力量对比和转换迹象
3. 联立思维分析 - 多级别、多维度综合分析
4. 时空思维分析 - 时间和空间维度的测算
5. 概率计算 - 量化各种走势类型的概率
6. 风险评估 - 为预期外走势做准备
7. 线上数据接入 - 自动获取实时市场数据
8. 图表输出 - 生成走势推演分析图表
"""

import pandas as pd
import numpy as np
import os
from loguru import logger
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# 导入数据获取和分析模块
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter

class TrendType(Enum):
    """走势类型枚举"""
    UP = "up"           # 上涨
    DOWN = "down"       # 下跌
    CONSOLIDATION = "consolidation"  # 盘整

class StructureState(Enum):
    """结构状态枚举"""
    FORMING = "forming"         # 形成中
    DEVELOPING = "developing"   # 发展中
    MATURE = "mature"          # 成熟
    EXHAUSTED = "exhausted"    # 衰竭

@dataclass
class TrendScenario:
    """走势情景"""
    trend_type: TrendType
    probability: float
    target_price: Optional[float]
    time_horizon: Optional[int]  # 预期时间（分钟）
    confidence_level: str  # high/medium/low
    supporting_factors: List[str]
    risk_factors: List[str]
    description: str
    detailed_reasoning: Optional[str] = None  # 新增：详细判断理由

@dataclass
class EvolutionResult:
    """推演结果"""
    primary_scenario: TrendScenario
    alternative_scenarios: List[TrendScenario]
    risk_assessment: Dict[str, Any]
    recommended_action: str
    profit_target: Optional[float]
    stop_loss: Optional[float]
    position_size: str  # light/medium/heavy
    analysis_timestamp: datetime
    chart_path: Optional[str] = None  # 新增：图表保存路径
    data_source: str = "live"  # 新增：数据来源标识

class TrendEvolutionEngine:
    """走势推演引擎"""

    def __init__(self, symbol: str = "BTC/USDT", exchange: str = "gate",
                 chart_dir: str = "charts/trend_evolution"):
        """
        初始化推演引擎

        参数:
            symbol: 交易对符号
            exchange: 交易所名称
            chart_dir: 图表保存目录
        """
        self.symbol = symbol
        self.exchange = exchange
        self.chart_dir = chart_dir

        # 确保图表目录存在
        os.makedirs(chart_dir, exist_ok=True)

        # 初始化数据获取器
        self.data_fetcher = None
        self.chan_analyzer = ChanAnalysis()
        self.chart_plotter = ChartPlotter(save_dir=chart_dir)

        # 权重配置
        self.weight_config = {
            'structure_analysis': 0.35,    # 结构分析权重
            'multi_space_game': 0.25,      # 多空博弈权重
            'multi_level_joint': 0.20,     # 多级别联立权重
            'time_space_analysis': 0.15,   # 时空分析权重
            'technical_indicators': 0.05   # 技术指标权重
        }

        # 概率阈值配置
        self.probability_thresholds = {
            'high_confidence': 0.75,
            'medium_confidence': 0.60,
            'low_confidence': 0.45
        }

        logger.info(f"走势推演引擎初始化完成 - 交易对: {symbol}, 交易所: {exchange}")

    def evolve_trend_live(self, timeframes: List[str] = None,
                         limit: int = 200, generate_chart: bool = True) -> EvolutionResult:
        """
        使用线上数据执行走势推演分析

        参数:
            timeframes: 时间周期列表，默认为 ['5m', '15m', '30m', '1h']
            limit: 获取K线数量
            generate_chart: 是否生成分析图表

        返回:
            EvolutionResult: 推演结果
        """
        try:
            if timeframes is None:
                timeframes = ['5m', '15m', '30m', '1h']

            logger.info(f"开始获取线上数据进行走势推演 - {self.symbol}")

            # 获取多级别数据
            market_data = self._fetch_multi_timeframe_data(timeframes, limit)

            # 执行缠论分析
            structure_data = self._analyze_market_structure(market_data)

            # 执行走势推演
            result = self.evolve_trend(market_data, structure_data, '15m')

            # 生成图表
            if generate_chart:
                chart_path = self._generate_evolution_chart(market_data, structure_data, result)
                result.chart_path = chart_path

            result.data_source = "live"

            logger.info(f"线上数据走势推演完成 - 主要情景: {result.primary_scenario.trend_type.value}")
            return result

        except Exception as e:
            logger.error(f"线上数据走势推演失败: {str(e)}")
            return self._create_default_result()

    def evolve_trend(self, market_data: Dict[str, pd.DataFrame],
                    structure_data: Dict[str, Any],
                    current_level: str = '15m') -> EvolutionResult:
        """
        主要推演方法
        
        参数:
            market_data: 多级别市场数据
            structure_data: 当前结构分析数据
            current_level: 当前主要分析级别
            
        返回:
            EvolutionResult: 推演结果
        """
        try:
            logger.info("开始执行走势推演分析")
            
            # 1. 结构思维分析
            structure_analysis = self._analyze_structure_state(structure_data, market_data[current_level])
            
            # 2. 多空博弈分析
            game_analysis = self._analyze_multi_space_game(market_data[current_level], structure_data)
            
            # 3. 多级别联立分析
            multi_level_analysis = self._analyze_multi_level_joint(market_data, structure_data)
            
            # 4. 时空思维分析
            time_space_analysis = self._analyze_time_space(market_data[current_level], structure_data)
            
            # 5. 生成走势情景
            scenarios = self._generate_trend_scenarios(
                structure_analysis, game_analysis, multi_level_analysis, time_space_analysis
            )
            
            # 6. 计算各情景概率
            scenarios_with_probability = self._calculate_scenario_probabilities(
                scenarios, structure_analysis, game_analysis, multi_level_analysis, time_space_analysis
            )

            # 6.5. 为主要情景添加详细判断理由
            if scenarios_with_probability:
                scenarios_with_probability[0] = self._add_detailed_reasoning(
                    scenarios_with_probability[0], structure_data, structure_analysis,
                    game_analysis, multi_level_analysis, time_space_analysis
                )

            # 7. 风险评估
            risk_assessment = self._assess_risks(scenarios_with_probability, market_data[current_level])

            # 8. 生成最终推演结果
            result = self._generate_evolution_result(
                scenarios_with_probability, risk_assessment, market_data[current_level]
            )
            
            logger.info(f"走势推演完成，主要情景: {result.primary_scenario.trend_type.value}, "
                       f"概率: {result.primary_scenario.probability:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"走势推演分析失败: {str(e)}")
            # 返回默认结果
            return self._create_default_result()

    def _fetch_multi_timeframe_data(self, timeframes: List[str], limit: int) -> Dict[str, pd.DataFrame]:
        """
        获取多级别市场数据

        参数:
            timeframes: 时间周期列表
            limit: 获取K线数量

        返回:
            Dict[str, pd.DataFrame]: 多级别数据字典
        """
        try:
            market_data = {}

            for timeframe in timeframes:
                logger.info(f"获取 {timeframe} 级别数据...")

                # 初始化数据获取器
                if self.data_fetcher is None or self.data_fetcher.timeframe != timeframe:
                    self.data_fetcher = DataFetcher(
                        symbols=self.symbol,
                        timeframe=timeframe,
                        exchange_id=self.exchange
                    )

                # 获取K线数据
                df = self.data_fetcher.get_klines(symbol=self.symbol, limit=limit)

                if df is not None and not df.empty:
                    # 添加交易对和时间周期信息
                    df.attrs['symbol'] = self.symbol
                    df.attrs['timeframe'] = timeframe
                    market_data[timeframe] = df
                    logger.info(f"成功获取 {timeframe} 级别数据: {len(df)} 条")
                else:
                    logger.warning(f"获取 {timeframe} 级别数据失败")

            if not market_data:
                raise Exception("未能获取任何有效的市场数据")

            return market_data

        except Exception as e:
            logger.error(f"获取多级别数据失败: {str(e)}")
            raise

    def _analyze_market_structure(self, market_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        分析市场结构

        参数:
            market_data: 多级别市场数据

        返回:
            Dict[str, Any]: 结构分析数据
        """
        try:
            structure_data = {
                'multi_level_analysis': {},
                'bi_list': [],
                'xd_list': [],
                'pivot_zones': [],
                'buy_points': {},
                'sell_points': {}
            }

            # 对每个时间级别进行缠论分析
            for timeframe, df in market_data.items():
                logger.info(f"分析 {timeframe} 级别结构...")

                # 执行缠论分析
                self.chan_analyzer.analyze(df)

                # 保存分析结果
                level_analysis = {
                    'fx_list': self.chan_analyzer.fx_list.copy(),
                    'bi_list': self.chan_analyzer.bi_list.copy(),
                    'xd_list': self.chan_analyzer.xd_list.copy(),
                    'pivot_zones': getattr(self.chan_analyzer, 'pivot_zones', []),
                    'buy_points': getattr(self.chan_analyzer, 'buy_points', {}),
                    'sell_points': getattr(self.chan_analyzer, 'sell_points', {})
                }

                structure_data['multi_level_analysis'][timeframe] = level_analysis

                # 使用15m级别作为主要分析级别
                if timeframe == '15m':
                    structure_data['bi_list'] = level_analysis['bi_list']
                    structure_data['xd_list'] = level_analysis['xd_list']
                    structure_data['pivot_zones'] = level_analysis['pivot_zones']
                    structure_data['buy_points'] = level_analysis['buy_points']
                    structure_data['sell_points'] = level_analysis['sell_points']

            logger.info("市场结构分析完成")
            return structure_data

        except Exception as e:
            logger.error(f"市场结构分析失败: {str(e)}")
            return {
                'multi_level_analysis': {},
                'bi_list': [],
                'xd_list': [],
                'pivot_zones': [],
                'buy_points': {},
                'sell_points': {}
            }

    def _generate_evolution_chart(self, market_data: Dict[str, pd.DataFrame],
                                 structure_data: Dict[str, Any],
                                 result: EvolutionResult) -> Optional[str]:
        """
        生成走势推演分析图表

        参数:
            market_data: 多级别市场数据
            structure_data: 结构分析数据
            result: 推演结果

        返回:
            Optional[str]: 图表保存路径
        """
        try:
            logger.info("开始生成走势推演分析图表...")

            # 准备多级别数据和分析结果
            fx_dict = {}
            bi_dict = {}
            xd_dict = {}

            for timeframe, analysis in structure_data['multi_level_analysis'].items():
                fx_dict[timeframe] = analysis.get('fx_list', [])
                bi_dict[timeframe] = analysis.get('bi_list', [])
                xd_dict[timeframe] = analysis.get('xd_list', [])

            # 生成时间戳
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 创建图表标题
            primary_scenario = result.primary_scenario
            title = (f"{self.symbol} 走势推演分析 - {timestamp}\n"
                    f"主要情景: {primary_scenario.trend_type.value.upper()} "
                    f"(概率: {primary_scenario.probability:.1%}, "
                    f"置信度: {primary_scenario.confidence_level})")

            # 生成推演走势数据
            evolution_data = self._generate_evolution_projection(market_data, result)

            # 确保结果中的目标价格与推演数据一致
            if evolution_data and 'target_price' in evolution_data:
                result.primary_scenario.target_price = evolution_data['target_price']
                logger.info(f"同步目标价格: {evolution_data['target_price']:.2f}")

            # 生成多级别分析图表（包含走势推演）
            chart_path = self.chart_plotter.plot_multi_timeframe_with_evolution(
                data_dict=market_data,
                symbol=self.symbol,
                fx_dict=fx_dict,
                bi_dict=bi_dict,
                xd_dict=xd_dict,
                evolution_data=evolution_data,
                show_pivots=True,
                show_line=True,
                show_buy_sell_points=True,
                add_analysis_text=True,
                title=title
            )

            if chart_path:
                logger.info(f"走势推演图表已生成: {chart_path}")
                return chart_path
            else:
                logger.warning("图表生成失败")
                return None

        except Exception as e:
            logger.error(f"生成走势推演图表失败: {str(e)}")
            return None

    def _generate_evolution_projection(self, market_data: Dict[str, pd.DataFrame],
                                     result: EvolutionResult) -> Dict[str, Any]:
        """
        生成走势推演投影数据

        参数:
            market_data: 多级别市场数据
            result: 推演结果

        返回:
            Dict[str, Any]: 推演投影数据
        """
        try:
            # 使用15m级别作为主要推演级别
            main_timeframe = '15m'
            if main_timeframe not in market_data:
                main_timeframe = list(market_data.keys())[0]

            df = market_data[main_timeframe]
            current_price = df['close'].iloc[-1]

            # 确保时间格式正确
            if 'timestamp' in df.columns:
                current_time = df['timestamp'].iloc[-1]
                if isinstance(current_time, str):
                    current_time = pd.to_datetime(current_time)
            else:
                current_time = df.index[-1]
                if isinstance(current_time, str):
                    current_time = pd.to_datetime(current_time)

            # 根据推演结果生成未来走势点
            primary_scenario = result.primary_scenario
            time_horizon = primary_scenario.time_horizon or 60  # 默认60分钟

            # 计算推演时间点（未来几个周期）
            if main_timeframe == '15m':
                period_minutes = 15
                periods_ahead = max(4, time_horizon // 15)  # 至少4个周期
            elif main_timeframe == '5m':
                period_minutes = 5
                periods_ahead = max(8, time_horizon // 5)
            elif main_timeframe == '30m':
                period_minutes = 30
                periods_ahead = max(3, time_horizon // 30)
            else:  # 1h
                period_minutes = 60
                periods_ahead = max(2, time_horizon // 60)

            evolution_points = []

            # 计算目标价格
            if primary_scenario.trend_type.value == 'up':
                if primary_scenario.target_price:
                    target_price = primary_scenario.target_price
                else:
                    # 基于当前价格计算合理的上涨目标
                    target_price = current_price * 1.05  # 5%上涨目标
            elif primary_scenario.trend_type.value == 'down':
                if primary_scenario.target_price:
                    target_price = primary_scenario.target_price
                else:
                    # 基于当前价格计算合理的下跌目标
                    target_price = current_price * 0.95  # 5%下跌目标
            else:  # consolidation
                target_price = current_price  # 震荡保持当前价格

            # 生成推演路径点
            for i in range(1, periods_ahead + 1):
                # 计算时间点 - 从当前时间开始递增
                future_time = current_time + timedelta(minutes=period_minutes * i)

                # 根据推演情景计算价格
                if primary_scenario.trend_type.value == 'up':
                    # 上涨情景：逐步上涨到目标价，添加合理波动
                    price_progress = i / periods_ahead
                    base_price = current_price + (target_price - current_price) * price_progress

                    # 添加小幅波动（模拟真实市场）
                    volatility = 0.005 * (1 + 0.5 * np.sin(i * 0.5))  # 0.5%波动
                    projected_price = base_price * (1 + volatility)

                elif primary_scenario.trend_type.value == 'down':
                    # 下跌情景：逐步下跌到目标价，添加合理波动
                    price_progress = i / periods_ahead
                    base_price = current_price + (target_price - current_price) * price_progress

                    # 添加小幅波动
                    volatility = -0.005 * (1 + 0.5 * np.sin(i * 0.5))  # -0.5%波动
                    projected_price = base_price * (1 + volatility)

                else:  # consolidation
                    # 震荡情景：在当前价格附近波动
                    volatility = 0.01 * np.sin(i * 0.8)  # 1%震荡幅度
                    projected_price = current_price * (1 + volatility)

                evolution_points.append({
                    'timestamp': future_time,
                    'price': projected_price,
                    'scenario': primary_scenario.trend_type.value,
                    'confidence': primary_scenario.probability
                })

            logger.info(f"生成推演投影数据: {main_timeframe}级别, {len(evolution_points)}个点, "
                       f"从{current_time}开始, 目标价格{target_price:.2f}")

            return {
                'timeframe': main_timeframe,
                'current_price': current_price,
                'current_time': current_time,
                'evolution_points': evolution_points,
                'primary_scenario': primary_scenario,
                'alternative_scenarios': result.alternative_scenarios,
                'target_price': target_price
            }

        except Exception as e:
            logger.error(f"生成推演投影数据失败: {str(e)}")
            return {}
    
    def _analyze_structure_state(self, structure_data: Dict[str, Any], df: pd.DataFrame) -> Dict[str, Any]:
        """
        结构思维分析：分析当前走势结构的完整性和生长状态
        
        参数:
            structure_data: 结构数据（包含分型、笔、线段、中枢等）
            df: K线数据
            
        返回:
            Dict: 结构分析结果
        """
        try:
            analysis = {
                'pen_state': {},      # 笔的状态
                'segment_state': {},  # 线段状态
                'center_state': {},   # 中枢状态
                'structure_integrity': 0,  # 结构完整性评分 0-10
                'growth_stage': StructureState.FORMING,  # 生长阶段
                'completion_probability': 0  # 完成概率
            }
            
            # 分析笔的状态
            bi_list = structure_data.get('bi_list', [])
            if bi_list:
                current_bi = bi_list[-1]
                analysis['pen_state'] = {
                    'direction': current_bi[4] if len(current_bi) > 4 else 'unknown',
                    'strength': self._calculate_pen_strength(current_bi, df),
                    'completion_status': self._assess_pen_completion(current_bi, df),
                    'continuation_probability': self._estimate_pen_continuation(current_bi, df)
                }
            
            # 分析线段状态
            xd_list = structure_data.get('xd_list', [])
            if xd_list:
                current_xd = xd_list[-1]
                analysis['segment_state'] = {
                    'direction': current_xd[4] if len(current_xd) > 4 else 'unknown',
                    'strength': self._calculate_segment_strength(current_xd, bi_list),
                    'completion_status': self._assess_segment_completion(current_xd, df),
                    'break_probability': self._estimate_segment_break(current_xd, df)
                }
            
            # 分析中枢状态
            pivot_zones = structure_data.get('pivot_zones', [])
            if pivot_zones:
                current_pivot = pivot_zones[-1]
                analysis['center_state'] = {
                    'level': current_pivot.get('level', 'unknown'),
                    'strength': self._calculate_center_strength(current_pivot, bi_list),
                    'expansion_probability': self._estimate_center_expansion(current_pivot, df),
                    'break_direction': self._predict_center_break_direction(current_pivot, df)
                }
            
            # 计算结构完整性评分
            analysis['structure_integrity'] = self._calculate_structure_integrity(analysis)
            
            # 判断生长阶段
            analysis['growth_stage'] = self._determine_growth_stage(analysis, df)
            
            # 计算完成概率
            analysis['completion_probability'] = self._calculate_completion_probability(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"结构状态分析失败: {str(e)}")
            return self._get_default_structure_analysis()
    
    def _analyze_multi_space_game(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        多空博弈分析：判断多空力量对比和转换迹象
        
        参数:
            df: K线数据
            structure_data: 结构数据
            
        返回:
            Dict: 多空博弈分析结果
        """
        try:
            analysis = {
                'current_advantage': 'neutral',  # bull/bear/neutral
                'strength_ratio': 0.5,  # 多空力量比值 0-1，0.5为平衡
                'momentum_change': 'stable',  # increasing/decreasing/stable
                'volume_support': False,  # 成交量是否支持当前方向
                'key_level_battle': {},  # 关键位置争夺情况
                'reversal_signals': []  # 反转信号
            }
            
            # 分析价格行为判断多空优势
            recent_bars = min(20, len(df))
            recent_df = df.tail(recent_bars)
            
            # 计算多空力量比值
            up_bars = len(recent_df[recent_df['close'] > recent_df['open']])
            down_bars = len(recent_df[recent_df['close'] < recent_df['open']])
            total_bars = len(recent_df)
            
            if total_bars > 0:
                bull_ratio = up_bars / total_bars
                analysis['strength_ratio'] = bull_ratio
                
                if bull_ratio > 0.6:
                    analysis['current_advantage'] = 'bull'
                elif bull_ratio < 0.4:
                    analysis['current_advantage'] = 'bear'
                else:
                    analysis['current_advantage'] = 'neutral'
            
            # 分析动量变化
            analysis['momentum_change'] = self._analyze_momentum_change(df)
            
            # 分析成交量支持
            analysis['volume_support'] = self._analyze_volume_support(df)
            
            # 分析关键位置争夺
            analysis['key_level_battle'] = self._analyze_key_level_battle(df, structure_data)
            
            # 识别反转信号
            analysis['reversal_signals'] = self._identify_reversal_signals(df, structure_data)
            
            return analysis

        except Exception as e:
            logger.error(f"多空博弈分析失败: {str(e)}")
            return self._get_default_game_analysis()

    def _analyze_multi_level_joint(self, market_data: Dict[str, pd.DataFrame],
                                  structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        多级别联立分析：多级别、多维度综合分析

        参数:
            market_data: 多级别市场数据
            structure_data: 结构数据

        返回:
            Dict: 多级别联立分析结果
        """
        try:
            analysis = {
                'level_consensus': {},  # 各级别共识情况
                'trend_consistency': False,  # 趋势一致性
                'resonance_strength': 0,  # 共振强度 0-10
                'dominant_level': None,  # 主导级别
                'conflict_levels': [],  # 冲突级别
                'joint_probability': 0  # 联立概率
            }

            # 定义级别权重（大级别权重更高）
            level_weights = {
                '1m': 0.05, '5m': 0.15, '15m': 0.25,
                '30m': 0.30, '1h': 0.20, '4h': 0.05
            }

            # 分析各级别趋势方向
            level_trends = {}
            for timeframe, df in market_data.items():
                if len(df) >= 20:  # 确保有足够数据
                    trend_direction = self._determine_level_trend(df)
                    level_trends[timeframe] = trend_direction

            # 计算趋势一致性
            unique_trends = set([t for t in level_trends.values() if t != 'unknown'])
            analysis['trend_consistency'] = len(unique_trends) <= 1

            # 计算共振强度
            if analysis['trend_consistency'] and unique_trends:
                main_trend = list(unique_trends)[0]
                resonance_score = 0
                for level, trend in level_trends.items():
                    if trend == main_trend and level in level_weights:
                        resonance_score += level_weights[level] * 10
                analysis['resonance_strength'] = min(10, resonance_score)

            # 确定主导级别
            analysis['dominant_level'] = self._determine_dominant_level(level_trends, level_weights)

            # 识别冲突级别
            if not analysis['trend_consistency']:
                analysis['conflict_levels'] = self._identify_conflict_levels(level_trends)

            # 计算联立概率
            analysis['joint_probability'] = self._calculate_joint_probability(
                level_trends, level_weights, analysis['resonance_strength']
            )

            analysis['level_consensus'] = level_trends

            return analysis

        except Exception as e:
            logger.error(f"多级别联立分析失败: {str(e)}")
            return self._get_default_multi_level_analysis()

    def _analyze_time_space(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        时空思维分析：时间和空间维度的测算

        参数:
            df: K线数据
            structure_data: 结构数据

        返回:
            Dict: 时空分析结果
        """
        try:
            analysis = {
                'time_cycle': {},      # 时间周期分析
                'space_measurement': {},  # 空间测算
                'time_space_resonance': False,  # 时空共振
                'critical_time_points': [],  # 关键时间点
                'target_levels': {},   # 目标位测算
                'time_efficiency': 0   # 时间效率评分
            }

            # 时间周期分析
            analysis['time_cycle'] = self._analyze_time_cycles(df, structure_data)

            # 空间测算
            analysis['space_measurement'] = self._analyze_space_measurement(df, structure_data)

            # 时空共振分析
            analysis['time_space_resonance'] = self._check_time_space_resonance(
                analysis['time_cycle'], analysis['space_measurement']
            )

            # 关键时间点识别
            analysis['critical_time_points'] = self._identify_critical_time_points(df, structure_data)

            # 目标位测算
            analysis['target_levels'] = self._calculate_target_levels(df, structure_data)

            # 时间效率评分
            analysis['time_efficiency'] = self._calculate_time_efficiency(df, structure_data)

            return analysis

        except Exception as e:
            logger.error(f"时空分析失败: {str(e)}")
            return self._get_default_time_space_analysis()

    def _generate_trend_scenarios(self, structure_analysis: Dict[str, Any],
                                 game_analysis: Dict[str, Any],
                                 multi_level_analysis: Dict[str, Any],
                                 time_space_analysis: Dict[str, Any]) -> List[TrendScenario]:
        """
        生成走势情景

        参数:
            structure_analysis: 结构分析结果
            game_analysis: 多空博弈分析结果
            multi_level_analysis: 多级别分析结果
            time_space_analysis: 时空分析结果

        返回:
            List[TrendScenario]: 走势情景列表
        """
        try:
            scenarios = []

            # 基于结构分析生成情景
            structure_scenarios = self._generate_structure_scenarios(structure_analysis)
            scenarios.extend(structure_scenarios)

            # 基于多空博弈生成情景
            game_scenarios = self._generate_game_scenarios(game_analysis)
            scenarios.extend(game_scenarios)

            # 基于多级别分析生成情景
            multi_level_scenarios = self._generate_multi_level_scenarios(multi_level_analysis)
            scenarios.extend(multi_level_scenarios)

            # 基于时空分析生成情景
            time_space_scenarios = self._generate_time_space_scenarios(time_space_analysis)
            scenarios.extend(time_space_scenarios)

            # 去重和合并相似情景
            merged_scenarios = self._merge_similar_scenarios(scenarios)

            return merged_scenarios

        except Exception as e:
            logger.error(f"生成走势情景失败: {str(e)}")
            return self._get_default_scenarios()

    def _calculate_scenario_probabilities(self, scenarios: List[TrendScenario],
                                        structure_analysis: Dict[str, Any],
                                        game_analysis: Dict[str, Any],
                                        multi_level_analysis: Dict[str, Any],
                                        time_space_analysis: Dict[str, Any]) -> List[TrendScenario]:
        """
        计算各情景概率

        参数:
            scenarios: 走势情景列表
            structure_analysis: 结构分析结果
            game_analysis: 多空博弈分析结果
            multi_level_analysis: 多级别分析结果
            time_space_analysis: 时空分析结果

        返回:
            List[TrendScenario]: 带概率的走势情景列表
        """
        try:
            scenarios_with_prob = []

            for scenario in scenarios:
                # 计算各维度评分
                structure_score = self._evaluate_structure_support(scenario, structure_analysis)
                game_score = self._evaluate_game_support(scenario, game_analysis)
                multi_level_score = self._evaluate_multi_level_support(scenario, multi_level_analysis)
                time_space_score = self._evaluate_time_space_support(scenario, time_space_analysis)

                # 加权计算总概率
                total_probability = (
                    structure_score * self.weight_config['structure_analysis'] +
                    game_score * self.weight_config['multi_space_game'] +
                    multi_level_score * self.weight_config['multi_level_joint'] +
                    time_space_score * self.weight_config['time_space_analysis']
                )

                # 更新情景概率
                scenario.probability = min(max(total_probability, 0.0), 1.0)

                # 设置置信度等级
                if scenario.probability >= self.probability_thresholds['high_confidence']:
                    scenario.confidence_level = 'high'
                elif scenario.probability >= self.probability_thresholds['medium_confidence']:
                    scenario.confidence_level = 'medium'
                else:
                    scenario.confidence_level = 'low'

                scenarios_with_prob.append(scenario)

            # 归一化概率
            total_prob = sum(s.probability for s in scenarios_with_prob)
            if total_prob > 0:
                for scenario in scenarios_with_prob:
                    scenario.probability = scenario.probability / total_prob

            # 按概率排序
            scenarios_with_prob.sort(key=lambda x: x.probability, reverse=True)

            return scenarios_with_prob

        except Exception as e:
            logger.error(f"计算情景概率失败: {str(e)}")
            return scenarios

    def _assess_risks(self, scenarios: List[TrendScenario], df: pd.DataFrame) -> Dict[str, Any]:
        """
        风险评估：为预期外走势做准备

        参数:
            scenarios: 带概率的走势情景列表
            df: K线数据

        返回:
            Dict: 风险评估结果
        """
        try:
            current_price = float(df['close'].iloc[-1])

            # 识别主要风险
            primary_scenario = scenarios[0] if scenarios else None
            primary_risks = self._identify_primary_risks(primary_scenario, scenarios) if primary_scenario else ['数据不足']

            # 识别尾部风险
            tail_risks = self._identify_tail_risks(scenarios, current_price)

            # 计算最大回撤估计
            max_drawdown_estimate = self._estimate_max_drawdown(scenarios, current_price)

            # 生成止损位建议
            stop_loss_levels = self._generate_stop_loss_levels(scenarios, current_price)

            # 评估整体风险等级
            risk_level = self._evaluate_overall_risk_level(scenarios, max_drawdown_estimate)

            # 仓位建议
            position_sizing = self._recommend_position_sizing(scenarios, {'max_drawdown_estimate': max_drawdown_estimate})

            # 风险缓解措施
            risk_mitigation = self._generate_risk_mitigation(scenarios, {
                'primary_risks': primary_risks,
                'max_drawdown_estimate': max_drawdown_estimate,
                'risk_level': risk_level
            })

            risk_assessment = {
                'primary_risks': primary_risks,      # 主要风险
                'tail_risks': tail_risks,         # 尾部风险（低概率高影响）
                'risk_mitigation': risk_mitigation,    # 风险缓解措施
                'max_drawdown_estimate': max_drawdown_estimate,  # 最大回撤估计
                'stop_loss_levels': stop_loss_levels,   # 止损位建议
                'position_sizing': position_sizing,  # 仓位建议
                'risk_level': risk_level,  # 新增：整体风险等级
                'max_drawdown_percent': f"{max_drawdown_estimate:.1%}"  # 新增：回撤百分比显示
            }

            return risk_assessment

        except Exception as e:
            logger.error(f"风险评估失败: {str(e)}")
            return self._get_default_risk_assessment()

    def _evaluate_overall_risk_level(self, scenarios: List[TrendScenario], max_drawdown: float) -> str:
        """评估整体风险等级"""
        try:
            if not scenarios:
                return "HIGH"

            primary_scenario = scenarios[0]
            primary_prob = primary_scenario.probability
            confidence = primary_scenario.confidence_level

            # 基于概率、置信度和最大回撤评估风险
            if primary_prob >= 0.7 and confidence == 'high' and max_drawdown <= 0.03:
                return "LOW"
            elif primary_prob >= 0.5 and confidence in ['high', 'medium'] and max_drawdown <= 0.05:
                return "MEDIUM"
            elif primary_prob >= 0.4 and max_drawdown <= 0.08:
                return "MEDIUM-HIGH"
            else:
                return "HIGH"

        except Exception as e:
            logger.warning(f"评估风险等级失败: {str(e)}")
            return "MEDIUM"

    def _add_detailed_reasoning(self, scenario: TrendScenario, structure_data: Dict[str, Any],
                               structure_analysis: Dict[str, Any], game_analysis: Dict[str, Any],
                               multi_level_analysis: Dict[str, Any], time_space_analysis: Dict[str, Any]) -> TrendScenario:
        """为主要情景添加详细判断理由"""
        try:
            reasoning_parts = []

            # 1. 多级别分析理由
            level_confirmations = []
            level_conflicts = []

            multi_level = structure_data.get('multi_level_analysis', {})
            for timeframe, analysis in multi_level.items():
                # 检查笔的方向
                bi_list = analysis.get('bi_list', [])
                if bi_list:
                    last_bi = bi_list[-1]
                    bi_direction = last_bi[4] if len(last_bi) > 4 else 'unknown'
                    if bi_direction == scenario.trend_type.value:
                        level_confirmations.append(f"{timeframe}级别最新笔方向为{bi_direction}")
                    elif bi_direction != 'unknown':
                        level_conflicts.append(f"{timeframe}级别最新笔方向为{bi_direction}")

                # 检查线段的方向
                xd_list = analysis.get('xd_list', [])
                if xd_list:
                    last_xd = xd_list[-1]
                    xd_direction = last_xd[4] if len(last_xd) > 4 else 'unknown'
                    if xd_direction == scenario.trend_type.value:
                        level_confirmations.append(f"{timeframe}级别最新线段方向为{xd_direction}")

            if level_confirmations:
                reasoning_parts.append(f"多级别确认: {', '.join(level_confirmations)}")
            if level_conflicts:
                reasoning_parts.append(f"级别分歧: {', '.join(level_conflicts)}")

            # 2. 结构分析理由
            structure_reasoning = []
            pen_state = structure_analysis.get('pen_state', {})
            if pen_state:
                pen_direction = pen_state.get('direction', 'unknown')
                pen_strength = pen_state.get('strength', 0)
                completion_prob = pen_state.get('continuation_probability', 0)

                if pen_direction == scenario.trend_type.value:
                    structure_reasoning.append(f"当前笔方向为{pen_direction}，强度{pen_strength:.1f}，延续概率{completion_prob:.1%}")

            segment_state = structure_analysis.get('segment_state', {})
            if segment_state:
                segment_direction = segment_state.get('direction', 'unknown')
                break_prob = segment_state.get('break_probability', 0)

                if segment_direction == scenario.trend_type.value:
                    structure_reasoning.append(f"当前线段方向为{segment_direction}，突破概率{break_prob:.1%}")

            if structure_reasoning:
                reasoning_parts.append(f"结构分析: {', '.join(structure_reasoning)}")

            # 3. 多空博弈理由
            game_reasoning = []
            current_advantage = game_analysis.get('current_advantage', 'neutral')
            strength_ratio = game_analysis.get('strength_ratio', 0.5)
            volume_support = game_analysis.get('volume_support', False)

            if scenario.trend_type.value == 'up' and current_advantage == 'bull':
                game_reasoning.append(f"多方占优，力量比值{strength_ratio:.1%}")
            elif scenario.trend_type.value == 'down' and current_advantage == 'bear':
                game_reasoning.append(f"空方占优，力量比值{(1-strength_ratio):.1%}")

            if volume_support:
                game_reasoning.append("成交量支持当前方向")

            if game_reasoning:
                reasoning_parts.append(f"多空博弈: {', '.join(game_reasoning)}")

            # 4. 技术指标理由
            technical_reasoning = []
            for factor in scenario.supporting_factors:
                if 'MACD' in factor or 'macd' in factor:
                    technical_reasoning.append(f"MACD指标{factor}")
                elif '均线' in factor or 'MA' in factor:
                    technical_reasoning.append(f"均线系统{factor}")
                elif '背离' in factor:
                    technical_reasoning.append(f"价格与指标{factor}")
                elif '突破' in factor:
                    technical_reasoning.append(f"关键位置{factor}")

            if technical_reasoning:
                reasoning_parts.append(f"技术指标: {', '.join(technical_reasoning)}")

            # 5. 概率计算理由
            prob_reasoning = f"综合概率计算: 基于{len(scenario.supporting_factors)}个支持因素，" \
                           f"置信度{scenario.confidence_level}，" \
                           f"最终概率{scenario.probability:.1%}"
            reasoning_parts.append(prob_reasoning)

            # 6. 风险提示
            if scenario.risk_factors:
                risk_reasoning = f"风险提示: {', '.join(scenario.risk_factors)}"
                reasoning_parts.append(risk_reasoning)

            # 组合详细理由
            detailed_reasoning = "\n".join([f"{i+1}. {part}" for i, part in enumerate(reasoning_parts)])

            # 创建新的scenario对象
            new_scenario = TrendScenario(
                trend_type=scenario.trend_type,
                probability=scenario.probability,
                target_price=scenario.target_price,
                time_horizon=scenario.time_horizon,
                confidence_level=scenario.confidence_level,
                supporting_factors=scenario.supporting_factors,
                risk_factors=scenario.risk_factors,
                description=scenario.description,
                detailed_reasoning=detailed_reasoning
            )

            logger.info(f"已生成详细判断理由，共{len(reasoning_parts)}个要点")
            return new_scenario

        except Exception as e:
            logger.error(f"生成详细判断理由失败: {str(e)}")
            return scenario

    def _generate_evolution_result(self, scenarios: List[TrendScenario],
                                  risk_assessment: Dict[str, Any],
                                  df: pd.DataFrame) -> EvolutionResult:
        """
        生成最终推演结果

        参数:
            scenarios: 带概率的走势情景列表
            risk_assessment: 风险评估结果
            df: K线数据

        返回:
            EvolutionResult: 推演结果
        """
        try:
            if not scenarios:
                return self._create_default_result()

            primary_scenario = scenarios[0]
            alternative_scenarios = scenarios[1:3]  # 取前3个备选情景
            current_price = float(df['close'].iloc[-1])

            # 生成操作建议
            recommended_action = self._generate_recommended_action(primary_scenario, risk_assessment)

            # 计算止盈止损
            profit_target = self._calculate_profit_target(primary_scenario, current_price)
            stop_loss = risk_assessment['stop_loss_levels'].get('conservative', current_price * 0.98)

            # 仓位建议
            position_size = risk_assessment['position_sizing']

            result = EvolutionResult(
                primary_scenario=primary_scenario,
                alternative_scenarios=alternative_scenarios,
                risk_assessment=risk_assessment,
                recommended_action=recommended_action,
                profit_target=profit_target,
                stop_loss=stop_loss,
                position_size=position_size,
                analysis_timestamp=datetime.now()
            )

            return result

        except Exception as e:
            logger.error(f"生成推演结果失败: {str(e)}")
            return self._create_default_result()

    # ==================== 辅助方法 ====================

    def _calculate_pen_strength(self, bi: Tuple, df: pd.DataFrame) -> float:
        """计算笔的强度"""
        try:
            if len(bi) < 5:
                return 5.0

            start_idx, end_idx = bi[0], bi[1]
            start_price, end_price = bi[2], bi[3]
            direction = bi[4]

            # 计算价格变化幅度
            price_change = abs(end_price - start_price) / start_price

            # 计算时间跨度
            time_span = end_idx - start_idx

            # 计算成交量支持
            volume_support = self._calculate_volume_support(df, start_idx, end_idx)

            # 综合评分
            strength = min(10, (price_change * 100 + time_span * 0.1 + volume_support * 2))
            return max(1.0, strength)

        except Exception as e:
            logger.warning(f"计算笔强度失败: {str(e)}")
            return 5.0

    def _assess_pen_completion(self, bi: Tuple, df: pd.DataFrame) -> str:
        """评估笔的完成状态"""
        try:
            if len(bi) < 5:
                return 'unknown'

            end_idx = bi[1]
            current_idx = len(df) - 1

            # 如果笔的结束点就是最新的K线，说明可能未完成
            if abs(end_idx - current_idx) <= 2:
                return 'incomplete'
            else:
                return 'complete'

        except Exception as e:
            logger.warning(f"评估笔完成状态失败: {str(e)}")
            return 'unknown'

    def _estimate_pen_continuation(self, bi: Tuple, df: pd.DataFrame) -> float:
        """估计笔延续的概率"""
        try:
            if len(bi) < 5:
                return 0.5

            direction = bi[4]
            end_price = bi[3]
            current_price = float(df['close'].iloc[-1])

            # 如果当前价格朝着笔的方向继续移动，延续概率较高
            if direction == 'up' and current_price > end_price:
                return 0.7
            elif direction == 'down' and current_price < end_price:
                return 0.7
            else:
                return 0.3

        except Exception as e:
            logger.warning(f"估计笔延续概率失败: {str(e)}")
            return 0.5

    def _calculate_segment_strength(self, xd: Tuple, bi_list: List) -> float:
        """计算线段强度"""
        try:
            if len(xd) < 5:
                return 5.0

            start_price, end_price = xd[2], xd[3]
            direction = xd[4]

            # 计算价格变化幅度
            price_change = abs(end_price - start_price) / start_price

            # 计算包含的笔数量
            start_idx, end_idx = xd[0], xd[1]
            pen_count = len([bi for bi in bi_list if bi[0] >= start_idx and bi[1] <= end_idx])

            # 综合评分
            strength = min(10, (price_change * 50 + pen_count * 0.5))
            return max(1.0, strength)

        except Exception as e:
            logger.warning(f"计算线段强度失败: {str(e)}")
            return 5.0

    def _assess_segment_completion(self, xd: Tuple, df: pd.DataFrame) -> str:
        """评估线段完成状态"""
        try:
            if len(xd) < 5:
                return 'unknown'

            end_idx = xd[1]
            current_idx = len(df) - 1

            # 如果线段的结束点接近最新K线，可能未完成
            if abs(end_idx - current_idx) <= 5:
                return 'incomplete'
            else:
                return 'complete'

        except Exception as e:
            logger.warning(f"评估线段完成状态失败: {str(e)}")
            return 'unknown'

    def _estimate_segment_break(self, xd: Tuple, df: pd.DataFrame) -> float:
        """估计线段突破概率"""
        try:
            if len(xd) < 5:
                return 0.5

            direction = xd[4]
            end_price = xd[3]
            current_price = float(df['close'].iloc[-1])

            # 计算当前价格相对于线段终点的位置
            price_ratio = abs(current_price - end_price) / end_price

            # 如果价格已经明显突破线段终点，突破概率较高
            if price_ratio > 0.02:  # 超过2%
                return 0.8
            elif price_ratio > 0.01:  # 超过1%
                return 0.6
            else:
                return 0.3

        except Exception as e:
            logger.warning(f"估计线段突破概率失败: {str(e)}")
            return 0.5

    def _calculate_center_strength(self, pivot: Dict, bi_list: List) -> float:
        """计算中枢强度"""
        try:
            # 中枢的震荡次数
            oscillation_count = pivot.get('oscillation_count', 3)

            # 中枢的持续时间
            duration = pivot.get('duration', 10)

            # 中枢的价格区间
            high = pivot.get('high', 0)
            low = pivot.get('low', 0)
            range_ratio = (high - low) / low if low > 0 else 0

            # 综合评分
            strength = min(10, (oscillation_count * 1.5 + duration * 0.1 + range_ratio * 100))
            return max(1.0, strength)

        except Exception as e:
            logger.warning(f"计算中枢强度失败: {str(e)}")
            return 5.0

    def _estimate_center_expansion(self, pivot: Dict, df: pd.DataFrame) -> float:
        """估计中枢扩展概率"""
        try:
            current_price = float(df['close'].iloc[-1])
            pivot_high = pivot.get('high', current_price)
            pivot_low = pivot.get('low', current_price)

            # 如果当前价格在中枢区间内，扩展概率较高
            if pivot_low <= current_price <= pivot_high:
                return 0.7
            else:
                return 0.3

        except Exception as e:
            logger.warning(f"估计中枢扩展概率失败: {str(e)}")
            return 0.5

    def _predict_center_break_direction(self, pivot: Dict, df: pd.DataFrame) -> str:
        """预测中枢突破方向"""
        try:
            current_price = float(df['close'].iloc[-1])
            pivot_high = pivot.get('high', current_price)
            pivot_low = pivot.get('low', current_price)

            # 基于当前价格位置预测突破方向
            if current_price > pivot_high:
                return 'up'
            elif current_price < pivot_low:
                return 'down'
            else:
                # 在中枢内部，分析最近的动量
                recent_change = self._calculate_recent_momentum(df)
                return 'up' if recent_change > 0 else 'down'

        except Exception as e:
            logger.warning(f"预测中枢突破方向失败: {str(e)}")
            return 'unknown'

    def _calculate_structure_integrity(self, analysis: Dict[str, Any]) -> float:
        """计算结构完整性评分"""
        try:
            score = 5.0  # 基础分数

            # 笔的完整性加分
            pen_state = analysis.get('pen_state', {})
            if pen_state.get('completion_status') == 'complete':
                score += 1.5

            # 线段的完整性加分
            segment_state = analysis.get('segment_state', {})
            if segment_state.get('completion_status') == 'complete':
                score += 2.0

            # 中枢的存在加分
            center_state = analysis.get('center_state', {})
            if center_state:
                score += 1.5

            return min(10.0, score)

        except Exception as e:
            logger.warning(f"计算结构完整性失败: {str(e)}")
            return 5.0

    def _determine_growth_stage(self, analysis: Dict[str, Any], df: pd.DataFrame) -> StructureState:
        """判断生长阶段"""
        try:
            # 基于结构完整性和市场状态判断
            integrity = analysis.get('structure_integrity', 5)

            if integrity < 3:
                return StructureState.FORMING
            elif integrity < 6:
                return StructureState.DEVELOPING
            elif integrity < 8:
                return StructureState.MATURE
            else:
                return StructureState.EXHAUSTED

        except Exception as e:
            logger.warning(f"判断生长阶段失败: {str(e)}")
            return StructureState.FORMING

    def _calculate_completion_probability(self, analysis: Dict[str, Any]) -> float:
        """计算完成概率"""
        try:
            # 基于各种状态计算完成概率
            pen_completion = analysis.get('pen_state', {}).get('continuation_probability', 0.5)
            segment_break = analysis.get('segment_state', {}).get('break_probability', 0.5)
            center_expansion = analysis.get('center_state', {}).get('expansion_probability', 0.5)

            # 加权平均
            completion_prob = (pen_completion * 0.3 + segment_break * 0.4 + center_expansion * 0.3)
            return min(1.0, max(0.0, completion_prob))

        except Exception as e:
            logger.warning(f"计算完成概率失败: {str(e)}")
            return 0.5

    def _analyze_momentum_change(self, df: pd.DataFrame) -> str:
        """分析动量变化"""
        try:
            if len(df) < 10:
                return 'stable'

            # 计算最近的价格动量
            recent_prices = df['close'].tail(10).values
            early_avg = np.mean(recent_prices[:5])
            late_avg = np.mean(recent_prices[5:])

            change_ratio = (late_avg - early_avg) / early_avg

            if change_ratio > 0.01:
                return 'increasing'
            elif change_ratio < -0.01:
                return 'decreasing'
            else:
                return 'stable'

        except Exception as e:
            logger.warning(f"分析动量变化失败: {str(e)}")
            return 'stable'

    def _analyze_volume_support(self, df: pd.DataFrame) -> bool:
        """分析成交量支持"""
        try:
            if len(df) < 20:
                return False

            recent_volume = df['volume'].tail(5).mean()
            avg_volume = df['volume'].tail(20).mean()

            # 如果最近成交量明显高于平均水平，认为有支持
            return recent_volume > avg_volume * 1.2

        except Exception as e:
            logger.warning(f"分析成交量支持失败: {str(e)}")
            return False

    def _analyze_key_level_battle(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析关键位置争夺"""
        try:
            current_price = float(df['close'].iloc[-1])

            # 获取关键支撑阻力位
            pivot_zones = structure_data.get('pivot_zones', [])
            key_levels = []

            for pivot in pivot_zones:
                key_levels.extend([pivot.get('high', 0), pivot.get('low', 0)])

            # 找到最近的关键位置
            nearest_level = None
            min_distance = float('inf')

            for level in key_levels:
                if level > 0:
                    distance = abs(current_price - level) / current_price
                    if distance < min_distance:
                        min_distance = distance
                        nearest_level = level

            battle_info = {
                'nearest_level': nearest_level,
                'distance_ratio': min_distance,
                'battle_intensity': 'low'
            }

            # 判断争夺激烈程度
            if min_distance < 0.01:  # 距离关键位置1%以内
                battle_info['battle_intensity'] = 'high'
            elif min_distance < 0.02:  # 距离关键位置2%以内
                battle_info['battle_intensity'] = 'medium'

            return battle_info

        except Exception as e:
            logger.warning(f"分析关键位置争夺失败: {str(e)}")
            return {'battle_intensity': 'low'}

    def _identify_reversal_signals(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> List[str]:
        """识别反转信号"""
        try:
            signals = []

            if len(df) < 20:
                return signals

            # 检查价格反转信号
            recent_high = df['high'].tail(10).max()
            recent_low = df['low'].tail(10).min()
            current_price = float(df['close'].iloc[-1])

            # 检查是否在高位或低位
            if current_price >= recent_high * 0.98:
                signals.append("接近近期高点，注意顶部反转")
            elif current_price <= recent_low * 1.02:
                signals.append("接近近期低点，注意底部反转")

            # 检查成交量异常
            if self._check_volume_anomaly(df):
                signals.append("成交量异常，可能预示反转")

            return signals

        except Exception as e:
            logger.warning(f"识别反转信号失败: {str(e)}")
            return []

    # ==================== 默认方法 ====================

    def _get_default_structure_analysis(self) -> Dict[str, Any]:
        """获取默认结构分析结果"""
        return {
            'pen_state': {'direction': 'unknown', 'strength': 5, 'completion_status': 'unknown'},
            'segment_state': {'direction': 'unknown', 'strength': 5, 'completion_status': 'unknown'},
            'center_state': {'level': 'unknown', 'strength': 5},
            'structure_integrity': 5,
            'growth_stage': StructureState.FORMING,
            'completion_probability': 0.5
        }

    def _get_default_game_analysis(self) -> Dict[str, Any]:
        """获取默认多空博弈分析结果"""
        return {
            'current_advantage': 'neutral',
            'strength_ratio': 0.5,
            'momentum_change': 'stable',
            'volume_support': False,
            'key_level_battle': {'battle_intensity': 'low'},
            'reversal_signals': []
        }

    def _get_default_multi_level_analysis(self) -> Dict[str, Any]:
        """获取默认多级别分析结果"""
        return {
            'level_consensus': {},
            'trend_consistency': False,
            'resonance_strength': 0,
            'dominant_level': None,
            'conflict_levels': [],
            'joint_probability': 0.5
        }

    def _get_default_time_space_analysis(self) -> Dict[str, Any]:
        """获取默认时空分析结果"""
        return {
            'time_cycle': {},
            'space_measurement': {},
            'time_space_resonance': False,
            'critical_time_points': [],
            'target_levels': {},
            'time_efficiency': 5
        }

    def _get_default_scenarios(self) -> List[TrendScenario]:
        """获取默认走势情景"""
        return [
            TrendScenario(
                trend_type=TrendType.CONSOLIDATION,
                probability=0.6,
                target_price=None,
                time_horizon=60,
                confidence_level='medium',
                supporting_factors=['数据不足'],
                risk_factors=['不确定性高'],
                description='数据不足，预期震荡整理'
            ),
            TrendScenario(
                trend_type=TrendType.UP,
                probability=0.2,
                target_price=None,
                time_horizon=120,
                confidence_level='low',
                supporting_factors=[],
                risk_factors=['不确定性高'],
                description='上涨可能性较低'
            ),
            TrendScenario(
                trend_type=TrendType.DOWN,
                probability=0.2,
                target_price=None,
                time_horizon=120,
                confidence_level='low',
                supporting_factors=[],
                risk_factors=['不确定性高'],
                description='下跌可能性较低'
            )
        ]

    def _get_default_risk_assessment(self) -> Dict[str, Any]:
        """获取默认风险评估结果"""
        return {
            'primary_risks': ['数据不足', '不确定性高'],
            'tail_risks': [],
            'risk_mitigation': {'建议': '等待更多信号确认'},
            'max_drawdown_estimate': 0.05,
            'stop_loss_levels': {'conservative': 0.98, 'aggressive': 0.95},
            'position_sizing': 'light',
            'risk_level': 'HIGH',  # 新增：默认高风险
            'max_drawdown_percent': '5.0%'  # 新增：回撤百分比显示
        }

    def _create_default_result(self) -> EvolutionResult:
        """创建默认推演结果"""
        default_scenario = TrendScenario(
            trend_type=TrendType.CONSOLIDATION,
            probability=1.0,
            target_price=None,
            time_horizon=60,
            confidence_level='low',
            supporting_factors=['数据不足'],
            risk_factors=['高不确定性'],
            description='数据不足，建议等待'
        )

        return EvolutionResult(
            primary_scenario=default_scenario,
            alternative_scenarios=[],
            risk_assessment=self._get_default_risk_assessment(),
            recommended_action='wait',
            profit_target=None,
            stop_loss=None,
            position_size='light',
            analysis_timestamp=datetime.now()
        )

    # ==================== 缺失的辅助方法 ====================

    def _calculate_volume_support(self, df: pd.DataFrame, start_idx: int, end_idx: int) -> float:
        """计算成交量支持度"""
        try:
            if start_idx >= end_idx or end_idx >= len(df):
                return 0.0

            period_volume = df['volume'].iloc[start_idx:end_idx+1].mean()
            total_avg_volume = df['volume'].mean()

            return min(5.0, period_volume / total_avg_volume) if total_avg_volume > 0 else 0.0

        except Exception as e:
            logger.warning(f"计算成交量支持度失败: {str(e)}")
            return 0.0

    def _calculate_recent_momentum(self, df: pd.DataFrame) -> float:
        """计算最近动量"""
        try:
            if len(df) < 5:
                return 0.0

            recent_prices = df['close'].tail(5)
            return (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]

        except Exception as e:
            logger.warning(f"计算最近动量失败: {str(e)}")
            return 0.0

    def _check_volume_anomaly(self, df: pd.DataFrame) -> bool:
        """检查成交量异常"""
        try:
            if len(df) < 20:
                return False

            recent_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].tail(20).mean()

            return recent_volume > avg_volume * 2.0

        except Exception as e:
            logger.warning(f"检查成交量异常失败: {str(e)}")
            return False

    def _determine_level_trend(self, df: pd.DataFrame) -> str:
        """确定级别趋势"""
        try:
            if len(df) < 20:
                return 'unknown'

            # 使用简单的移动平均判断趋势
            short_ma = df['close'].tail(5).mean()
            long_ma = df['close'].tail(20).mean()

            if short_ma > long_ma * 1.01:
                return 'up'
            elif short_ma < long_ma * 0.99:
                return 'down'
            else:
                return 'sideways'

        except Exception as e:
            logger.warning(f"确定级别趋势失败: {str(e)}")
            return 'unknown'

    def _determine_dominant_level(self, level_trends: Dict[str, str], level_weights: Dict[str, float]) -> Optional[str]:
        """确定主导级别"""
        try:
            # 找到权重最高且有明确趋势的级别
            max_weight = 0
            dominant_level = None

            for level, trend in level_trends.items():
                if trend in ['up', 'down'] and level in level_weights:
                    weight = level_weights[level]
                    if weight > max_weight:
                        max_weight = weight
                        dominant_level = level

            return dominant_level

        except Exception as e:
            logger.warning(f"确定主导级别失败: {str(e)}")
            return None

    def _identify_conflict_levels(self, level_trends: Dict[str, str]) -> List[str]:
        """识别冲突级别"""
        try:
            # 找到趋势方向不一致的级别
            trend_groups = {}
            for level, trend in level_trends.items():
                if trend not in trend_groups:
                    trend_groups[trend] = []
                trend_groups[trend].append(level)

            # 如果有多个不同的趋势方向，返回较少的那组
            if len(trend_groups) > 1:
                min_group = min(trend_groups.values(), key=len)
                return min_group

            return []

        except Exception as e:
            logger.warning(f"识别冲突级别失败: {str(e)}")
            return []

    def _calculate_joint_probability(self, level_trends: Dict[str, str],
                                   level_weights: Dict[str, float],
                                   resonance_strength: float) -> float:
        """计算联立概率"""
        try:
            # 基于共振强度和趋势一致性计算联立概率
            base_probability = 0.5

            # 共振强度加成
            resonance_bonus = resonance_strength / 10 * 0.3

            # 趋势一致性加成
            unique_trends = set([t for t in level_trends.values() if t != 'unknown'])
            consistency_bonus = 0.2 if len(unique_trends) <= 1 else -0.1

            joint_prob = base_probability + resonance_bonus + consistency_bonus
            return min(1.0, max(0.0, joint_prob))

        except Exception as e:
            logger.warning(f"计算联立概率失败: {str(e)}")
            return 0.5

    # ==================== 核心生成方法 ====================

    def _generate_structure_scenarios(self, structure_analysis: Dict[str, Any]) -> List[TrendScenario]:
        """基于结构分析生成情景"""
        scenarios = []

        try:
            pen_state = structure_analysis.get('pen_state', {})
            segment_state = structure_analysis.get('segment_state', {})
            center_state = structure_analysis.get('center_state', {})

            # 基于笔的状态生成情景
            pen_direction = pen_state.get('direction', 'unknown')
            if pen_direction in ['up', 'down']:
                # 笔延续情景
                scenarios.append(TrendScenario(
                    trend_type=TrendType.UP if pen_direction == 'up' else TrendType.DOWN,
                    probability=0.0,  # 稍后计算
                    target_price=None,
                    time_horizon=30,
                    confidence_level='medium',
                    supporting_factors=[f'当前{pen_direction}笔可能延续'],
                    risk_factors=[],
                    description=f'笔延续情景：{pen_direction}笔继续发展'
                ))

                # 笔反转情景
                opposite_type = TrendType.DOWN if pen_direction == 'up' else TrendType.UP
                scenarios.append(TrendScenario(
                    trend_type=opposite_type,
                    probability=0.0,
                    target_price=None,
                    time_horizon=60,
                    confidence_level='medium',
                    supporting_factors=[],
                    risk_factors=[f'{pen_direction}笔可能结束'],
                    description=f'笔反转情景：{pen_direction}笔结束，开始反向'
                ))

            # 基于中枢状态生成情景
            if center_state:
                # 中枢震荡情景
                scenarios.append(TrendScenario(
                    trend_type=TrendType.CONSOLIDATION,
                    probability=0.0,
                    target_price=None,
                    time_horizon=120,
                    confidence_level='medium',
                    supporting_factors=['存在中枢结构'],
                    risk_factors=[],
                    description='中枢震荡情景：在中枢区间内震荡'
                ))

                # 中枢突破情景
                break_direction = center_state.get('break_direction', 'unknown')
                if break_direction in ['up', 'down']:
                    scenarios.append(TrendScenario(
                        trend_type=TrendType.UP if break_direction == 'up' else TrendType.DOWN,
                        probability=0.0,
                        target_price=None,
                        time_horizon=180,
                        confidence_level='medium',
                        supporting_factors=[f'中枢可能向{break_direction}突破'],
                        risk_factors=[],
                        description=f'中枢突破情景：向{break_direction}突破中枢'
                    ))

        except Exception as e:
            logger.warning(f"基于结构分析生成情景失败: {str(e)}")

        return scenarios

    def _generate_game_scenarios(self, game_analysis: Dict[str, Any]) -> List[TrendScenario]:
        """基于多空博弈生成情景"""
        scenarios = []

        try:
            advantage = game_analysis.get('current_advantage', 'neutral')
            momentum = game_analysis.get('momentum_change', 'stable')

            if advantage == 'bull':
                scenarios.append(TrendScenario(
                    trend_type=TrendType.UP,
                    probability=0.0,
                    target_price=None,
                    time_horizon=90,
                    confidence_level='medium',
                    supporting_factors=['多方占优'],
                    risk_factors=[],
                    description='多方优势情景：多方力量占优，预期上涨'
                ))
            elif advantage == 'bear':
                scenarios.append(TrendScenario(
                    trend_type=TrendType.DOWN,
                    probability=0.0,
                    target_price=None,
                    time_horizon=90,
                    confidence_level='medium',
                    supporting_factors=['空方占优'],
                    risk_factors=[],
                    description='空方优势情景：空方力量占优，预期下跌'
                ))
            else:
                scenarios.append(TrendScenario(
                    trend_type=TrendType.CONSOLIDATION,
                    probability=0.0,
                    target_price=None,
                    time_horizon=60,
                    confidence_level='medium',
                    supporting_factors=['多空平衡'],
                    risk_factors=[],
                    description='多空平衡情景：多空力量平衡，预期震荡'
                ))

        except Exception as e:
            logger.warning(f"基于多空博弈生成情景失败: {str(e)}")

        return scenarios

    def _generate_multi_level_scenarios(self, multi_level_analysis: Dict[str, Any]) -> List[TrendScenario]:
        """基于多级别分析生成情景"""
        scenarios = []

        try:
            trend_consistency = multi_level_analysis.get('trend_consistency', False)
            dominant_level = multi_level_analysis.get('dominant_level')
            level_consensus = multi_level_analysis.get('level_consensus', {})

            if trend_consistency and dominant_level and dominant_level in level_consensus:
                dominant_trend = level_consensus[dominant_level]

                if dominant_trend == 'up':
                    scenarios.append(TrendScenario(
                        trend_type=TrendType.UP,
                        probability=0.0,
                        target_price=None,
                        time_horizon=240,
                        confidence_level='high',
                        supporting_factors=['多级别共振向上'],
                        risk_factors=[],
                        description='多级别共振上涨：各级别趋势一致向上'
                    ))
                elif dominant_trend == 'down':
                    scenarios.append(TrendScenario(
                        trend_type=TrendType.DOWN,
                        probability=0.0,
                        target_price=None,
                        time_horizon=240,
                        confidence_level='high',
                        supporting_factors=['多级别共振向下'],
                        risk_factors=[],
                        description='多级别共振下跌：各级别趋势一致向下'
                    ))

        except Exception as e:
            logger.warning(f"基于多级别分析生成情景失败: {str(e)}")

        return scenarios

    def _generate_time_space_scenarios(self, time_space_analysis: Dict[str, Any]) -> List[TrendScenario]:
        """基于时空分析生成情景"""
        scenarios = []

        try:
            time_space_resonance = time_space_analysis.get('time_space_resonance', False)
            target_levels = time_space_analysis.get('target_levels', {})

            if time_space_resonance:
                scenarios.append(TrendScenario(
                    trend_type=TrendType.UP,  # 这里需要根据实际分析确定
                    probability=0.0,
                    target_price=target_levels.get('upside_target'),
                    time_horizon=180,
                    confidence_level='high',
                    supporting_factors=['时空共振'],
                    risk_factors=[],
                    description='时空共振情景：时间和空间因素共振'
                ))

        except Exception as e:
            logger.warning(f"基于时空分析生成情景失败: {str(e)}")

        return scenarios

    def _merge_similar_scenarios(self, scenarios: List[TrendScenario]) -> List[TrendScenario]:
        """合并相似情景"""
        try:
            # 按趋势类型分组
            scenario_groups = {}
            for scenario in scenarios:
                trend_type = scenario.trend_type
                if trend_type not in scenario_groups:
                    scenario_groups[trend_type] = []
                scenario_groups[trend_type].append(scenario)

            # 合并每组中的情景
            merged_scenarios = []
            for trend_type, group in scenario_groups.items():
                if len(group) == 1:
                    merged_scenarios.append(group[0])
                else:
                    # 合并多个相同类型的情景
                    merged_scenario = self._merge_scenario_group(group)
                    merged_scenarios.append(merged_scenario)

            return merged_scenarios

        except Exception as e:
            logger.warning(f"合并相似情景失败: {str(e)}")
            return scenarios

    def _merge_scenario_group(self, scenarios: List[TrendScenario]) -> TrendScenario:
        """合并一组相同类型的情景"""
        if not scenarios:
            return self._get_default_scenarios()[0]

        if len(scenarios) == 1:
            return scenarios[0]

        # 取第一个作为基础
        base_scenario = scenarios[0]

        # 合并支持因素和风险因素
        all_supporting_factors = []
        all_risk_factors = []

        for scenario in scenarios:
            all_supporting_factors.extend(scenario.supporting_factors)
            all_risk_factors.extend(scenario.risk_factors)

        # 去重
        unique_supporting = list(set(all_supporting_factors))
        unique_risks = list(set(all_risk_factors))

        # 创建合并后的情景
        merged_scenario = TrendScenario(
            trend_type=base_scenario.trend_type,
            probability=0.0,  # 稍后计算
            target_price=base_scenario.target_price,
            time_horizon=max(s.time_horizon or 60 for s in scenarios),
            confidence_level=base_scenario.confidence_level,
            supporting_factors=unique_supporting,
            risk_factors=unique_risks,
            description=f"综合{base_scenario.trend_type.value}情景"
        )

        return merged_scenario

    # ==================== 评估方法 ====================

    def _evaluate_structure_support(self, scenario: TrendScenario, structure_analysis: Dict[str, Any]) -> float:
        """评估结构对情景的支持度"""
        try:
            score = 0.5  # 基础分数

            # 结构完整性加分
            integrity = structure_analysis.get('structure_integrity', 5)
            score += (integrity - 5) / 10 * 0.3

            # 笔的方向一致性
            pen_direction = structure_analysis.get('pen_state', {}).get('direction', 'unknown')
            if scenario.trend_type == TrendType.UP and pen_direction == 'up':
                score += 0.2
            elif scenario.trend_type == TrendType.DOWN and pen_direction == 'down':
                score += 0.2

            # 线段的方向一致性
            segment_direction = structure_analysis.get('segment_state', {}).get('direction', 'unknown')
            if scenario.trend_type == TrendType.UP and segment_direction == 'up':
                score += 0.2
            elif scenario.trend_type == TrendType.DOWN and segment_direction == 'down':
                score += 0.2

            return min(1.0, max(0.0, score))

        except Exception as e:
            logger.warning(f"评估结构支持度失败: {str(e)}")
            return 0.5

    def _evaluate_game_support(self, scenario: TrendScenario, game_analysis: Dict[str, Any]) -> float:
        """评估多空博弈对情景的支持度"""
        try:
            score = 0.5

            advantage = game_analysis.get('current_advantage', 'neutral')
            strength_ratio = game_analysis.get('strength_ratio', 0.5)

            # 多空优势一致性
            if scenario.trend_type == TrendType.UP and advantage == 'bull':
                score += 0.3
            elif scenario.trend_type == TrendType.DOWN and advantage == 'bear':
                score += 0.3
            elif scenario.trend_type == TrendType.CONSOLIDATION and advantage == 'neutral':
                score += 0.2

            # 力量比值支持
            if scenario.trend_type == TrendType.UP:
                score += (strength_ratio - 0.5) * 0.4
            elif scenario.trend_type == TrendType.DOWN:
                score += (0.5 - strength_ratio) * 0.4

            # 成交量支持
            if game_analysis.get('volume_support', False):
                score += 0.1

            return min(1.0, max(0.0, score))

        except Exception as e:
            logger.warning(f"评估多空博弈支持度失败: {str(e)}")
            return 0.5

    def _evaluate_multi_level_support(self, scenario: TrendScenario, multi_level_analysis: Dict[str, Any]) -> float:
        """评估多级别对情景的支持度"""
        try:
            score = 0.5

            trend_consistency = multi_level_analysis.get('trend_consistency', False)
            resonance_strength = multi_level_analysis.get('resonance_strength', 0)

            # 趋势一致性加分
            if trend_consistency:
                score += 0.3

            # 共振强度加分
            score += resonance_strength / 10 * 0.4

            # 联立概率加分
            joint_prob = multi_level_analysis.get('joint_probability', 0.5)
            score += (joint_prob - 0.5) * 0.3

            return min(1.0, max(0.0, score))

        except Exception as e:
            logger.warning(f"评估多级别支持度失败: {str(e)}")
            return 0.5

    def _evaluate_time_space_support(self, scenario: TrendScenario, time_space_analysis: Dict[str, Any]) -> float:
        """评估时空对情景的支持度"""
        try:
            score = 0.5

            time_space_resonance = time_space_analysis.get('time_space_resonance', False)
            time_efficiency = time_space_analysis.get('time_efficiency', 5)

            # 时空共振加分
            if time_space_resonance:
                score += 0.4

            # 时间效率加分
            score += (time_efficiency - 5) / 10 * 0.3

            return min(1.0, max(0.0, score))

        except Exception as e:
            logger.warning(f"评估时空支持度失败: {str(e)}")
            return 0.5

    def _identify_primary_risks(self, primary_scenario: TrendScenario, all_scenarios: List[TrendScenario]) -> List[str]:
        """识别主要风险"""
        risks = []

        try:
            # 概率风险
            if primary_scenario.probability < 0.6:
                risks.append(f"主要情景概率较低({primary_scenario.probability:.1%})")

            # 备选情景风险
            if len(all_scenarios) > 1:
                second_scenario = all_scenarios[1]
                if second_scenario.probability > 0.3:
                    risks.append(f"备选情景概率较高({second_scenario.probability:.1%})")

            # 置信度风险
            if primary_scenario.confidence_level == 'low':
                risks.append("主要情景置信度较低")

            # 情景特定风险
            risks.extend(primary_scenario.risk_factors)

        except Exception as e:
            logger.warning(f"识别主要风险失败: {str(e)}")

        return risks

    def _identify_tail_risks(self, scenarios: List[TrendScenario], current_price: float) -> List[str]:
        """识别尾部风险"""
        tail_risks = []

        try:
            # 低概率但高影响的情景
            for scenario in scenarios:
                if scenario.probability < 0.1 and scenario.target_price:
                    impact = abs(scenario.target_price - current_price) / current_price
                    if impact > 0.1:  # 影响超过10%
                        tail_risks.append(f"低概率高影响: {scenario.description}")

            # 通用尾部风险
            tail_risks.extend([
                "突发事件风险",
                "流动性风险",
                "系统性风险"
            ])

        except Exception as e:
            logger.warning(f"识别尾部风险失败: {str(e)}")

        return tail_risks

    def _estimate_max_drawdown(self, scenarios: List[TrendScenario], current_price: float) -> float:
        """估计最大回撤"""
        try:
            max_drawdown = 0.02  # 默认2%

            for scenario in scenarios:
                if scenario.target_price and scenario.trend_type in [TrendType.DOWN]:
                    drawdown = abs(current_price - scenario.target_price) / current_price
                    max_drawdown = max(max_drawdown, drawdown)
                elif scenario.target_price and scenario.trend_type in [TrendType.UP]:
                    # 上涨情景的潜在回撤（如果判断错误）
                    potential_drawdown = 0.03  # 3%潜在回撤
                    max_drawdown = max(max_drawdown, potential_drawdown)

            return min(0.2, max_drawdown)  # 限制在20%以内

        except Exception as e:
            logger.warning(f"估计最大回撤失败: {str(e)}")
            return 0.05

    def _generate_stop_loss_levels(self, scenarios: List[TrendScenario], current_price: float) -> Dict[str, float]:
        """生成止损位建议"""
        try:
            # 保守止损位
            conservative_stop = current_price * 0.98

            # 激进止损位
            aggressive_stop = current_price * 0.95

            # 根据主要情景调整
            if scenarios:
                primary = scenarios[0]
                if primary.trend_type == TrendType.UP:
                    conservative_stop = current_price * 0.97
                    aggressive_stop = current_price * 0.94
                elif primary.trend_type == TrendType.DOWN:
                    conservative_stop = current_price * 1.03
                    aggressive_stop = current_price * 1.06

            return {
                'conservative': conservative_stop,
                'aggressive': aggressive_stop
            }

        except Exception as e:
            logger.warning(f"生成止损位失败: {str(e)}")
            return {'conservative': current_price * 0.98, 'aggressive': current_price * 0.95}

    def _generate_risk_mitigation(self, scenarios: List[TrendScenario], risk_assessment: Dict[str, Any]) -> Dict[str, str]:
        """生成风险缓解措施"""
        mitigation = {}

        try:
            # 基于主要风险生成缓解措施
            primary_risks = risk_assessment.get('primary_risks', [])

            if any('概率较低' in risk for risk in primary_risks):
                mitigation['概率风险'] = '降低仓位，等待更强信号'

            if any('置信度较低' in risk for risk in primary_risks):
                mitigation['置信度风险'] = '分批建仓，严格止损'

            # 通用缓解措施
            mitigation['通用建议'] = '严格执行风险管理，保持灵活性'

        except Exception as e:
            logger.warning(f"生成风险缓解措施失败: {str(e)}")

        return mitigation

    def _recommend_position_sizing(self, scenarios: List[TrendScenario], risk_assessment: Dict[str, Any]) -> str:
        """推荐仓位大小"""
        try:
            if not scenarios:
                return 'light'

            primary = scenarios[0]

            # 基于概率和置信度决定仓位
            if primary.probability > 0.7 and primary.confidence_level == 'high':
                return 'heavy'
            elif primary.probability > 0.6 and primary.confidence_level in ['high', 'medium']:
                return 'medium'
            else:
                return 'light'

        except Exception as e:
            logger.warning(f"推荐仓位大小失败: {str(e)}")
            return 'light'

    def _generate_recommended_action(self, primary_scenario: TrendScenario, risk_assessment: Dict[str, Any]) -> str:
        """生成推荐操作"""
        try:
            if primary_scenario.probability < 0.5:
                return 'wait'

            if primary_scenario.trend_type == TrendType.UP:
                return 'buy'
            elif primary_scenario.trend_type == TrendType.DOWN:
                return 'sell'
            else:
                return 'wait'

        except Exception as e:
            logger.warning(f"生成推荐操作失败: {str(e)}")
            return 'wait'

    def _calculate_profit_target(self, primary_scenario: TrendScenario, current_price: float) -> Optional[float]:
        """计算止盈目标"""
        try:
            if primary_scenario.target_price:
                return primary_scenario.target_price

            # 基于趋势类型估算目标
            if primary_scenario.trend_type == TrendType.UP:
                return current_price * 1.05
            elif primary_scenario.trend_type == TrendType.DOWN:
                return current_price * 0.95
            else:
                return None

        except Exception as e:
            logger.warning(f"计算止盈目标失败: {str(e)}")
            return None

    # ==================== 时空分析方法 ====================

    def _analyze_time_cycles(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析时间周期"""
        try:
            cycles = {
                'current_cycle_stage': 'unknown',
                'cycle_completion': 0.5,
                'next_critical_time': None,
                'cycle_strength': 5
            }

            # 简化的时间周期分析
            if len(df) >= 20:
                # 基于K线数量估算周期阶段
                total_bars = len(df)
                if total_bars < 50:
                    cycles['current_cycle_stage'] = 'early'
                elif total_bars < 100:
                    cycles['current_cycle_stage'] = 'middle'
                else:
                    cycles['current_cycle_stage'] = 'late'

                cycles['cycle_completion'] = min(1.0, total_bars / 100)

            return cycles

        except Exception as e:
            logger.warning(f"分析时间周期失败: {str(e)}")
            return {'current_cycle_stage': 'unknown', 'cycle_completion': 0.5}

    def _analyze_space_measurement(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析空间测算"""
        try:
            measurement = {
                'price_range': 0,
                'support_levels': [],
                'resistance_levels': [],
                'target_calculation': {}
            }

            if len(df) >= 10:
                high_price = df['high'].max()
                low_price = df['low'].min()
                current_price = float(df['close'].iloc[-1])

                measurement['price_range'] = (high_price - low_price) / low_price

                # 简化的支撑阻力位计算
                measurement['support_levels'] = [low_price, current_price * 0.98]
                measurement['resistance_levels'] = [high_price, current_price * 1.02]

                # 目标位计算
                measurement['target_calculation'] = {
                    'upside_target': current_price * 1.05,
                    'downside_target': current_price * 0.95
                }

            return measurement

        except Exception as e:
            logger.warning(f"分析空间测算失败: {str(e)}")
            return {'price_range': 0, 'target_calculation': {}}

    def _check_time_space_resonance(self, time_cycle: Dict[str, Any], space_measurement: Dict[str, Any]) -> bool:
        """检查时空共振"""
        try:
            # 简化的时空共振判断
            cycle_stage = time_cycle.get('current_cycle_stage', 'unknown')
            price_range = space_measurement.get('price_range', 0)

            # 如果在周期关键阶段且价格波动较大，认为有共振
            if cycle_stage in ['early', 'late'] and price_range > 0.05:
                return True

            return False

        except Exception as e:
            logger.warning(f"检查时空共振失败: {str(e)}")
            return False

    def _identify_critical_time_points(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> List[str]:
        """识别关键时间点"""
        try:
            critical_points = []

            # 基于结构数据识别关键时间点
            bi_list = structure_data.get('bi_list', [])
            for bi in bi_list[-3:]:  # 最近3个笔
                if len(bi) >= 2:
                    critical_points.append(f"笔结束时间点: 第{bi[1]}根K线")

            return critical_points

        except Exception as e:
            logger.warning(f"识别关键时间点失败: {str(e)}")
            return []

    def _calculate_target_levels(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> Dict[str, float]:
        """计算目标位"""
        try:
            current_price = float(df['close'].iloc[-1])

            targets = {
                'upside_target': current_price * 1.05,
                'downside_target': current_price * 0.95,
                'neutral_target': current_price
            }

            # 基于中枢计算更精确的目标位
            pivot_zones = structure_data.get('pivot_zones', [])
            if pivot_zones:
                pivot = pivot_zones[-1]
                pivot_high = pivot.get('high', current_price)
                pivot_low = pivot.get('low', current_price)

                targets['upside_target'] = pivot_high * 1.02
                targets['downside_target'] = pivot_low * 0.98

            return targets

        except Exception as e:
            logger.warning(f"计算目标位失败: {str(e)}")
            current_price = float(df['close'].iloc[-1])
            return {
                'upside_target': current_price * 1.05,
                'downside_target': current_price * 0.95
            }

    def _calculate_time_efficiency(self, df: pd.DataFrame, structure_data: Dict[str, Any]) -> float:
        """计算时间效率"""
        try:
            # 基于价格变化和时间的比值计算效率
            if len(df) < 10:
                return 5.0

            price_change = abs(df['close'].iloc[-1] - df['close'].iloc[-10]) / df['close'].iloc[-10]
            time_span = 10  # K线数量

            efficiency = (price_change * 100) / time_span * 10
            return min(10.0, max(1.0, efficiency))

        except Exception as e:
            logger.warning(f"计算时间效率失败: {str(e)}")
            return 5.0


# ==================== 测试代码 ====================

def test_trend_evolution_engine_live():
    """测试走势推演引擎 - 线上数据版本"""
    print("🚀 开始测试走势推演引擎（线上数据）...")

    try:
        # 创建推演引擎
        engine = TrendEvolutionEngine(symbol="BTC/USDT", exchange="gate")
        print("✅ 推演引擎创建成功")

        # 使用线上数据执行推演
        print("📡 开始获取线上数据并执行推演...")
        result = engine.evolve_trend_live(
            timeframes=['5m', '15m', '30m', '1h'],
            limit=100,
            generate_chart=True
        )

        # 打印结果
        print("\n" + "="*60)
        print("📈 走势推演结果（线上数据）")
        print("="*60)

        print(f"🎯 主要情景: {result.primary_scenario.trend_type.value}")
        print(f"📊 概率: {result.primary_scenario.probability:.2%}")
        print(f"🔍 置信度: {result.primary_scenario.confidence_level}")
        print(f"📝 描述: {result.primary_scenario.description}")

        if result.primary_scenario.supporting_factors:
            print(f"✅ 支持因素: {', '.join(result.primary_scenario.supporting_factors)}")

        if result.primary_scenario.risk_factors:
            print(f"⚠️ 风险因素: {', '.join(result.primary_scenario.risk_factors)}")

        print(f"\n💡 推荐操作: {result.recommended_action}")
        print(f"💰 仓位建议: {result.position_size}")
        print(f"📊 数据来源: {result.data_source}")

        if result.profit_target:
            print(f"🎯 止盈目标: {result.profit_target:.2f}")

        if result.stop_loss:
            print(f"🛡️ 止损位: {result.stop_loss:.2f}")

        if result.chart_path:
            print(f"📈 分析图表: {result.chart_path}")

        print(f"\n📅 分析时间: {result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

        # 显示备选情景
        if result.alternative_scenarios:
            print(f"\n🔄 备选情景:")
            for i, scenario in enumerate(result.alternative_scenarios, 1):
                print(f"   {i}. {scenario.trend_type.value} (概率: {scenario.probability:.2%})")

        print("\n✅ 线上数据走势推演测试完成")
        return result

    except Exception as e:
        print(f"❌ 线上数据测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_trend_evolution_engine():
    """测试走势推演引擎 - 模拟数据版本"""
    print("🚀 开始测试走势推演引擎（模拟数据）...")

    try:
        # 创建推演引擎
        engine = TrendEvolutionEngine()
        print("✅ 推演引擎创建成功")

        # 创建模拟数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='15min')
        np.random.seed(42)

        # 生成模拟K线数据
        base_price = 50000
        prices = []
        volumes = []

        for i in range(100):
            # 模拟价格波动
            change = np.random.normal(0, 0.01)
            if i > 0:
                base_price = prices[-1]['close'] * (1 + change)

            high = base_price * (1 + abs(np.random.normal(0, 0.005)))
            low = base_price * (1 - abs(np.random.normal(0, 0.005)))
            open_price = base_price
            close_price = base_price

            prices.append({
                'timestamp': dates[i],
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': np.random.randint(1000, 10000)
            })

        # 创建DataFrame
        df = pd.DataFrame(prices)

        # 创建多级别数据
        market_data = {
            '5m': df.copy(),
            '15m': df.copy(),
            '30m': df.copy(),
            '1h': df.copy()
        }

        # 创建模拟结构数据
        structure_data = {
            'bi_list': [
                (0, 10, 49000, 51000, 'up'),
                (10, 20, 51000, 49500, 'down'),
                (20, 30, 49500, 52000, 'up')
            ],
            'xd_list': [
                (0, 30, 49000, 52000, 'up')
            ],
            'pivot_zones': [
                {'high': 51500, 'low': 49500, 'level': '15m', 'oscillation_count': 3}
            ]
        }

        print("📊 模拟数据创建完成")

        # 执行走势推演
        result = engine.evolve_trend(market_data, structure_data, '15m')

        # 打印结果
        print("\n" + "="*60)
        print("📈 走势推演结果")
        print("="*60)

        print(f"🎯 主要情景: {result.primary_scenario.trend_type.value}")
        print(f"📊 概率: {result.primary_scenario.probability:.2%}")
        print(f"🔍 置信度: {result.primary_scenario.confidence_level}")
        print(f"📝 描述: {result.primary_scenario.description}")

        if result.primary_scenario.supporting_factors:
            print(f"✅ 支持因素: {', '.join(result.primary_scenario.supporting_factors)}")

        if result.primary_scenario.risk_factors:
            print(f"⚠️ 风险因素: {', '.join(result.primary_scenario.risk_factors)}")

        print(f"\n💡 推荐操作: {result.recommended_action}")
        print(f"💰 仓位建议: {result.position_size}")

        if result.profit_target:
            print(f"🎯 止盈目标: {result.profit_target:.2f}")

        if result.stop_loss:
            print(f"🛡️ 止损位: {result.stop_loss:.2f}")

        print(f"\n📅 分析时间: {result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

        # 显示备选情景
        if result.alternative_scenarios:
            print(f"\n🔄 备选情景:")
            for i, scenario in enumerate(result.alternative_scenarios, 1):
                print(f"   {i}. {scenario.trend_type.value} (概率: {scenario.probability:.2%})")

        print("\n✅ 模拟数据走势推演测试完成")
        return result

    except Exception as e:
        print(f"❌ 模拟数据测试失败: {str(e)}")
        return None


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 线上数据测试（推荐）")
    print("2. 模拟数据测试")

    choice = input("请输入选择 (1 或 2): ").strip()

    if choice == "1":
        test_trend_evolution_engine_live()
    elif choice == "2":
        test_trend_evolution_engine()
    else:
        print("使用默认线上数据测试...")
        test_trend_evolution_engine_live()
